package com.facishare.paas.appframework.metadata.util;

import com.google.common.collect.Maps;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for FileAttachmentUtils
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("FileAttachmentUtils 单元测试")
class FileAttachmentUtilsJUnit5Test {

    // ==================== 常量测试 ====================

    @Test
    @DisplayName("常量值验证")
    void testConstants() {
        assertEquals("TN_", FileAttachmentUtils.TN_PATH_PREFIX);
        assertEquals("N_", FileAttachmentUtils.N_PATH_PREFIX);
        assertEquals("online_doc_url", FileAttachmentUtils.ONLINE_DOCUMENT_URL_KEY);
    }

    // ==================== isN 方法测试 ====================

    @Test
    @DisplayName("isN方法 - 正常N前缀路径")
    void testIsN_ValidNPrefix() {
        // Arrange & Act & Assert
        assertTrue(FileAttachmentUtils.isN("N_test_file.txt"));
        assertTrue(FileAttachmentUtils.isN("N_document.pdf"));
        assertTrue(FileAttachmentUtils.isN("N_"));
        assertTrue(FileAttachmentUtils.isN("N_folder/subfolder/file.doc"));
    }

    @Test
    @DisplayName("isN方法 - 非N前缀路径")
    void testIsN_InvalidNPrefix() {
        // Arrange & Act & Assert
        assertFalse(FileAttachmentUtils.isN("TN_test_file.txt"));
        assertFalse(FileAttachmentUtils.isN("test_file.txt"));
        assertFalse(FileAttachmentUtils.isN(""));
        assertFalse(FileAttachmentUtils.isN("n_lowercase.txt"));
        assertFalse(FileAttachmentUtils.isN("file_N_middle.txt"));
    }

    @Test
    @DisplayName("isN方法 - null路径")
    void testIsN_NullPath() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            FileAttachmentUtils.isN(null);
        });
    }

    @Test
    @DisplayName("isN方法 - 边界情况")
    void testIsN_EdgeCases() {
        // Arrange & Act & Assert
        assertFalse(FileAttachmentUtils.isN("N"));  // 只有一个字符N
        assertTrue(FileAttachmentUtils.isN("N_"));  // 正好是前缀
        assertFalse(FileAttachmentUtils.isN(" N_test.txt"));  // 前面有空格
    }

    // ==================== isTN 方法测试 ====================

    @Test
    @DisplayName("isTN方法 - 正常TN前缀路径")
    void testIsTN_ValidTNPrefix() {
        // Arrange & Act & Assert
        assertTrue(FileAttachmentUtils.isTN("TN_test_file.txt"));
        assertTrue(FileAttachmentUtils.isTN("TN_document.pdf"));
        assertTrue(FileAttachmentUtils.isTN("TN_"));
        assertTrue(FileAttachmentUtils.isTN("TN_folder/subfolder/file.doc"));
    }

    @Test
    @DisplayName("isTN方法 - 非TN前缀路径")
    void testIsTN_InvalidTNPrefix() {
        // Arrange & Act & Assert
        assertFalse(FileAttachmentUtils.isTN("N_test_file.txt"));
        assertFalse(FileAttachmentUtils.isTN("test_file.txt"));
        assertFalse(FileAttachmentUtils.isTN(""));
        assertFalse(FileAttachmentUtils.isTN("tn_lowercase.txt"));
        assertFalse(FileAttachmentUtils.isTN("file_TN_middle.txt"));
    }

    @Test
    @DisplayName("isTN方法 - null路径")
    void testIsTN_NullPath() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            FileAttachmentUtils.isTN(null);
        });
    }

    @Test
    @DisplayName("isTN方法 - 边界情况")
    void testIsTN_EdgeCases() {
        // Arrange & Act & Assert
        assertFalse(FileAttachmentUtils.isTN("TN"));  // 只有两个字符TN
        assertTrue(FileAttachmentUtils.isTN("TN_"));  // 正好是前缀
        assertFalse(FileAttachmentUtils.isTN(" TN_test.txt"));  // 前面有空格
    }

    // ==================== isOnLine 方法测试 ====================

    @Test
    @DisplayName("isOnLine方法 - 包含在线文档URL键")
    void testIsOnLine_ContainsOnlineDocKey() {
        // Arrange
        Map<String, Object> mapWithOnlineDoc = Maps.newHashMap();
        mapWithOnlineDoc.put(FileAttachmentUtils.ONLINE_DOCUMENT_URL_KEY, "http://example.com/doc");
        mapWithOnlineDoc.put("other_key", "other_value");

        // Act & Assert
        assertTrue(FileAttachmentUtils.isOnLine(mapWithOnlineDoc));
    }

    @Test
    @DisplayName("isOnLine方法 - 包含在线文档URL键但值为null")
    void testIsOnLine_ContainsOnlineDocKeyWithNullValue() {
        // Arrange
        Map<String, Object> mapWithNullValue = Maps.newHashMap();
        mapWithNullValue.put(FileAttachmentUtils.ONLINE_DOCUMENT_URL_KEY, null);

        // Act & Assert
        assertTrue(FileAttachmentUtils.isOnLine(mapWithNullValue));  // containsKey只检查键是否存在
    }

    @Test
    @DisplayName("isOnLine方法 - 不包含在线文档URL键")
    void testIsOnLine_DoesNotContainOnlineDocKey() {
        // Arrange
        Map<String, Object> mapWithoutOnlineDoc = Maps.newHashMap();
        mapWithoutOnlineDoc.put("file_path", "/path/to/file.txt");
        mapWithoutOnlineDoc.put("file_size", 1024);

        // Act & Assert
        assertFalse(FileAttachmentUtils.isOnLine(mapWithoutOnlineDoc));
    }

    @Test
    @DisplayName("isOnLine方法 - 空Map")
    void testIsOnLine_EmptyMap() {
        // Arrange
        Map<String, Object> emptyMap = Maps.newHashMap();

        // Act & Assert
        assertFalse(FileAttachmentUtils.isOnLine(emptyMap));
    }

    @Test
    @DisplayName("isOnLine方法 - null Map")
    void testIsOnLine_NullMap() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            FileAttachmentUtils.isOnLine(null);
        });
    }

    @Test
    @DisplayName("isOnLine方法 - 使用HashMap")
    void testIsOnLine_WithHashMap() {
        // Arrange
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put(FileAttachmentUtils.ONLINE_DOCUMENT_URL_KEY, "https://docs.example.com");

        // Act & Assert
        assertTrue(FileAttachmentUtils.isOnLine(hashMap));
    }

    // ==================== 组合测试 ====================

    @Test
    @DisplayName("组合测试 - 同一路径的N和TN前缀检查")
    void testCombined_NAndTNPrefixCheck() {
        // Arrange
        String nPath = "N_test_file.txt";
        String tnPath = "TN_test_file.txt";
        String normalPath = "test_file.txt";

        // Act & Assert
        // N前缀路径
        assertTrue(FileAttachmentUtils.isN(nPath));
        assertFalse(FileAttachmentUtils.isTN(nPath));

        // TN前缀路径
        assertFalse(FileAttachmentUtils.isN(tnPath));
        assertTrue(FileAttachmentUtils.isTN(tnPath));

        // 普通路径
        assertFalse(FileAttachmentUtils.isN(normalPath));
        assertFalse(FileAttachmentUtils.isTN(normalPath));
    }

    @Test
    @DisplayName("组合测试 - 文件路径和在线文档检查")
    void testCombined_FilePathAndOnlineDocCheck() {
        // Arrange
        String nPath = "N_document.pdf";
        String tnPath = "TN_image.jpg";
        
        Map<String, Object> onlineDocMap = Maps.newHashMap();
        onlineDocMap.put(FileAttachmentUtils.ONLINE_DOCUMENT_URL_KEY, "https://online.doc.com");
        onlineDocMap.put("file_path", nPath);

        Map<String, Object> offlineDocMap = Maps.newHashMap();
        offlineDocMap.put("file_path", tnPath);

        // Act & Assert
        // 在线文档
        assertTrue(FileAttachmentUtils.isOnLine(onlineDocMap));
        assertTrue(FileAttachmentUtils.isN(nPath));

        // 离线文档
        assertFalse(FileAttachmentUtils.isOnLine(offlineDocMap));
        assertTrue(FileAttachmentUtils.isTN(tnPath));
    }

    // ==================== 性能测试 ====================

    @Test
    @DisplayName("性能测试 - 大量路径前缀检查")
    void testPerformance_MassivePrefixCheck() {
        // Arrange
        int testCount = 10000;
        String[] nPaths = new String[testCount];
        String[] tnPaths = new String[testCount];
        
        for (int i = 0; i < testCount; i++) {
            nPaths[i] = "N_file_" + i + ".txt";
            tnPaths[i] = "TN_file_" + i + ".txt";
        }

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            assertTrue(FileAttachmentUtils.isN(nPaths[i]));
            assertFalse(FileAttachmentUtils.isTN(nPaths[i]));
            
            assertFalse(FileAttachmentUtils.isN(tnPaths[i]));
            assertTrue(FileAttachmentUtils.isTN(tnPaths[i]));
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：10000次操作应该在1秒内完成
        assertTrue(duration < 1000, "Performance test failed: took " + duration + "ms for " + (testCount * 4) + " operations");
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 特殊字符路径")
    void testEdgeCase_SpecialCharacterPaths() {
        // Arrange & Act & Assert
        assertTrue(FileAttachmentUtils.isN("N_文件.txt"));  // 中文字符
        assertTrue(FileAttachmentUtils.isTN("TN_файл.doc"));  // 俄文字符
        assertTrue(FileAttachmentUtils.isN("N_file with spaces.pdf"));  // 空格
        assertTrue(FileAttachmentUtils.isTN("TN_file-with-dashes.jpg"));  // 连字符
        assertTrue(FileAttachmentUtils.isN("N_file_with_underscores.png"));  // 下划线
    }

    @Test
    @DisplayName("边界测试 - 极长路径")
    void testEdgeCase_VeryLongPaths() {
        // Arrange
        StringBuilder longPath = new StringBuilder("N_");
        for (int i = 0; i < 1000; i++) {
            longPath.append("very_long_path_segment_");
        }
        longPath.append("file.txt");

        // Act & Assert
        assertTrue(FileAttachmentUtils.isN(longPath.toString()));
        assertFalse(FileAttachmentUtils.isTN(longPath.toString()));
    }

    @Test
    @DisplayName("集成测试 - 完整的文件附件处理流程")
    void testIntegration_CompleteFileAttachmentFlow() {
        // Arrange
        String nFilePath = "N_important_document.pdf";
        String tnFilePath = "TN_thumbnail_image.jpg";
        String regularFilePath = "regular_file.txt";
        
        Map<String, Object> onlineFileInfo = Maps.newHashMap();
        onlineFileInfo.put(FileAttachmentUtils.ONLINE_DOCUMENT_URL_KEY, "https://docs.company.com/document");
        onlineFileInfo.put("file_path", nFilePath);
        onlineFileInfo.put("file_size", 2048576);
        
        Map<String, Object> offlineFileInfo = Maps.newHashMap();
        offlineFileInfo.put("file_path", tnFilePath);
        offlineFileInfo.put("file_size", 102400);

        // Act & Assert - 完整流程验证
        // 1. 检查文件路径类型
        assertTrue(FileAttachmentUtils.isN(nFilePath), "N前缀文件路径检查失败");
        assertTrue(FileAttachmentUtils.isTN(tnFilePath), "TN前缀文件路径检查失败");
        assertFalse(FileAttachmentUtils.isN(regularFilePath), "普通文件路径N检查失败");
        assertFalse(FileAttachmentUtils.isTN(regularFilePath), "普通文件路径TN检查失败");
        
        // 2. 检查在线文档状态
        assertTrue(FileAttachmentUtils.isOnLine(onlineFileInfo), "在线文档检查失败");
        assertFalse(FileAttachmentUtils.isOnLine(offlineFileInfo), "离线文档检查失败");
        
        // 3. 组合验证
        // 在线文档通常使用N前缀
        if (FileAttachmentUtils.isOnLine(onlineFileInfo)) {
            String filePath = (String) onlineFileInfo.get("file_path");
            assertTrue(FileAttachmentUtils.isN(filePath), "在线文档应该使用N前缀");
        }
        
        // 缩略图通常使用TN前缀且为离线
        if (!FileAttachmentUtils.isOnLine(offlineFileInfo)) {
            String filePath = (String) offlineFileInfo.get("file_path");
            assertTrue(FileAttachmentUtils.isTN(filePath), "离线缩略图应该使用TN前缀");
        }
    }
}
