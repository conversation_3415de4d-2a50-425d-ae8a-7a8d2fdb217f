package com.facishare.paas.appframework.metadata.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for I18KeyBuildUtils
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("I18KeyBuildUtils 单元测试")
class I18KeyBuildUtilsJUnit5Test {

    // ==================== getChangeRuleChangePromptKey 方法测试 ====================

    @Test
    @DisplayName("获取变更规则变更提示键 - 正常情况")
    void testGetChangeRuleChangePromptKey_Success() {
        // Arrange
        String ruleApiName = "test_rule";
        String expectedKey = "change_rule.test_rule.change_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取变更规则变更提示键 - 复杂规则名称")
    void testGetChangeRuleChangePromptKey_ComplexRuleName() {
        // Arrange
        String ruleApiName = "complex_business_rule_v2";
        String expectedKey = "change_rule.complex_business_rule_v2.change_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取变更规则变更提示键 - 空字符串规则名称")
    void testGetChangeRuleChangePromptKey_EmptyRuleName() {
        // Arrange
        String ruleApiName = "";
        String expectedKey = "change_rule..change_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取变更规则变更提示键 - null规则名称")
    void testGetChangeRuleChangePromptKey_NullRuleName() {
        // Arrange
        String ruleApiName = null;
        String expectedKey = "change_rule.null.change_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    // ==================== getChangeRuleVerificationPromptKey 方法测试 ====================

    @Test
    @DisplayName("获取变更规则验证提示键 - 正常情况")
    void testGetChangeRuleVerificationPromptKey_Success() {
        // Arrange
        String ruleApiName = "validation_rule";
        String expectedKey = "change_rule.validation_rule.verification_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取变更规则验证提示键 - 带特殊字符的规则名称")
    void testGetChangeRuleVerificationPromptKey_SpecialCharacters() {
        // Arrange
        String ruleApiName = "rule_with-special.chars_123";
        String expectedKey = "change_rule.rule_with-special.chars_123.verification_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取变更规则验证提示键 - 空字符串规则名称")
    void testGetChangeRuleVerificationPromptKey_EmptyRuleName() {
        // Arrange
        String ruleApiName = "";
        String expectedKey = "change_rule..verification_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取变更规则验证提示键 - null规则名称")
    void testGetChangeRuleVerificationPromptKey_NullRuleName() {
        // Arrange
        String ruleApiName = null;
        String expectedKey = "change_rule.null.verification_prompt";

        // Act
        String result = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    // ==================== getDuplicatedRulesNameKey 方法测试 ====================

    @Test
    @DisplayName("获取重复规则名称键 - 正常情况")
    void testGetDuplicatedRulesNameKey_Success() {
        // Arrange
        String objectApiName = "TestObject";
        String ruleName = "duplicate_rule";
        String expectedKey = "repeat_rule.TestObject.duplicate_rule.name";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则名称键 - 复杂对象和规则名称")
    void testGetDuplicatedRulesNameKey_ComplexNames() {
        // Arrange
        String objectApiName = "ComplexBusinessObject_v2";
        String ruleName = "advanced_validation_rule_123";
        String expectedKey = "repeat_rule.ComplexBusinessObject_v2.advanced_validation_rule_123.name";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则名称键 - 空字符串参数")
    void testGetDuplicatedRulesNameKey_EmptyParameters() {
        // Arrange
        String objectApiName = "";
        String ruleName = "";
        String expectedKey = "repeat_rule...name";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则名称键 - null参数")
    void testGetDuplicatedRulesNameKey_NullParameters() {
        // Arrange
        String objectApiName = null;
        String ruleName = null;
        String expectedKey = "repeat_rule.null.null.name";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则名称键 - 混合null和非null参数")
    void testGetDuplicatedRulesNameKey_MixedNullParameters() {
        // Arrange & Act & Assert
        String result1 = I18KeyBuildUtils.getDuplicatedRulesNameKey("TestObject", null);
        assertEquals("repeat_rule.TestObject.null.name", result1);

        String result2 = I18KeyBuildUtils.getDuplicatedRulesNameKey(null, "test_rule");
        assertEquals("repeat_rule.null.test_rule.name", result2);
    }

    // ==================== getDuplicatedRulesDescriptionKey 方法测试 ====================

    @Test
    @DisplayName("获取重复规则描述键 - 正常情况")
    void testGetDuplicatedRulesDescriptionKey_Success() {
        // Arrange
        String objectApiName = "TestObject";
        String ruleName = "duplicate_rule";
        String expectedKey = "repeat_rule.TestObject.duplicate_rule.description";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则描述键 - 复杂对象和规则名称")
    void testGetDuplicatedRulesDescriptionKey_ComplexNames() {
        // Arrange
        String objectApiName = "CustomerManagementObject";
        String ruleName = "customer_validation_rule_v3";
        String expectedKey = "repeat_rule.CustomerManagementObject.customer_validation_rule_v3.description";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则描述键 - 空字符串参数")
    void testGetDuplicatedRulesDescriptionKey_EmptyParameters() {
        // Arrange
        String objectApiName = "";
        String ruleName = "";
        String expectedKey = "repeat_rule...description";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则描述键 - null参数")
    void testGetDuplicatedRulesDescriptionKey_NullParameters() {
        // Arrange
        String objectApiName = null;
        String ruleName = null;
        String expectedKey = "repeat_rule.null.null.description";

        // Act
        String result = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取重复规则描述键 - 混合null和非null参数")
    void testGetDuplicatedRulesDescriptionKey_MixedNullParameters() {
        // Arrange & Act & Assert
        String result1 = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey("TestObject", null);
        assertEquals("repeat_rule.TestObject.null.description", result1);

        String result2 = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(null, "test_rule");
        assertEquals("repeat_rule.null.test_rule.description", result2);
    }

    // ==================== 组合测试 ====================

    @Test
    @DisplayName("组合测试 - 同一规则的不同键类型")
    void testCombined_SameRuleDifferentKeyTypes() {
        // Arrange
        String ruleApiName = "business_rule_001";
        String objectApiName = "BusinessObject";
        String ruleName = "validation_rule";

        // Act
        String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleApiName);
        String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleApiName);
        String duplicateNameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectApiName, ruleName);
        String duplicateDescKey = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectApiName, ruleName);

        // Assert
        assertEquals("change_rule.business_rule_001.change_prompt", changePromptKey);
        assertEquals("change_rule.business_rule_001.verification_prompt", verificationPromptKey);
        assertEquals("repeat_rule.BusinessObject.validation_rule.name", duplicateNameKey);
        assertEquals("repeat_rule.BusinessObject.validation_rule.description", duplicateDescKey);

        // 验证键的唯一性
        assertNotEquals(changePromptKey, verificationPromptKey);
        assertNotEquals(duplicateNameKey, duplicateDescKey);
    }

    @Test
    @DisplayName("组合测试 - 键格式一致性验证")
    void testCombined_KeyFormatConsistency() {
        // Arrange
        String testRule = "test_rule";
        String testObject = "TestObject";
        String testRuleName = "test_rule_name";

        // Act
        String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(testRule);
        String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(testRule);
        String duplicateNameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(testObject, testRuleName);
        String duplicateDescKey = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(testObject, testRuleName);

        // Assert - 验证键格式一致性
        assertTrue(changePromptKey.startsWith("change_rule."));
        assertTrue(changePromptKey.endsWith(".change_prompt"));
        
        assertTrue(verificationPromptKey.startsWith("change_rule."));
        assertTrue(verificationPromptKey.endsWith(".verification_prompt"));
        
        assertTrue(duplicateNameKey.startsWith("repeat_rule."));
        assertTrue(duplicateNameKey.endsWith(".name"));
        
        assertTrue(duplicateDescKey.startsWith("repeat_rule."));
        assertTrue(duplicateDescKey.endsWith(".description"));

        // 验证键的分段数量
        assertEquals(3, changePromptKey.split("\\.").length);
        assertEquals(3, verificationPromptKey.split("\\.").length);
        assertEquals(4, duplicateNameKey.split("\\.").length);
        assertEquals(4, duplicateDescKey.split("\\.").length);
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 特殊字符处理")
    void testEdgeCase_SpecialCharacters() {
        // Arrange
        String specialRule = "rule.with.dots_and-dashes";
        String specialObject = "Object-With_Special.Chars";
        String specialRuleName = "rule@name#with$special%chars";

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(specialRule);
            String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(specialRule);
            String duplicateNameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(specialObject, specialRuleName);
            String duplicateDescKey = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(specialObject, specialRuleName);

            // 验证结果包含特殊字符
            assertTrue(changePromptKey.contains(specialRule));
            assertTrue(verificationPromptKey.contains(specialRule));
            assertTrue(duplicateNameKey.contains(specialObject));
            assertTrue(duplicateNameKey.contains(specialRuleName));
            assertTrue(duplicateDescKey.contains(specialObject));
            assertTrue(duplicateDescKey.contains(specialRuleName));
        });
    }

    @Test
    @DisplayName("边界测试 - 极长字符串")
    void testEdgeCase_VeryLongStrings() {
        // Arrange
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longString.append("very_long_string_segment_").append(i).append("_");
        }
        String longRuleName = longString.toString();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(longRuleName);
            String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(longRuleName);
            String duplicateNameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(longRuleName, longRuleName);
            String duplicateDescKey = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(longRuleName, longRuleName);

            // 验证结果不为空且包含长字符串
            assertNotNull(changePromptKey);
            assertNotNull(verificationPromptKey);
            assertNotNull(duplicateNameKey);
            assertNotNull(duplicateDescKey);
            
            assertTrue(changePromptKey.contains(longRuleName));
            assertTrue(verificationPromptKey.contains(longRuleName));
            assertTrue(duplicateNameKey.contains(longRuleName));
            assertTrue(duplicateDescKey.contains(longRuleName));
        });
    }

    // ==================== 性能测试 ====================

    @Test
    @DisplayName("性能测试 - 大量键生成")
    void testPerformance_MassiveKeyGeneration() {
        // Arrange
        int testCount = 10000;
        String[] ruleNames = new String[testCount];
        String[] objectNames = new String[testCount];
        
        for (int i = 0; i < testCount; i++) {
            ruleNames[i] = "rule_" + i;
            objectNames[i] = "object_" + i;
        }

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(ruleNames[i]);
            String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(ruleNames[i]);
            String duplicateNameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(objectNames[i], ruleNames[i]);
            String duplicateDescKey = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(objectNames[i], ruleNames[i]);
            
            // 简单验证
            assertNotNull(changePromptKey);
            assertNotNull(verificationPromptKey);
            assertNotNull(duplicateNameKey);
            assertNotNull(duplicateDescKey);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：40000次操作应该在1秒内完成
        assertTrue(duration < 1000, "Performance test failed: took " + duration + "ms for " + (testCount * 4) + " operations");
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的国际化键构建流程")
    void testIntegration_CompleteI18nKeyBuildingFlow() {
        // Arrange - 模拟真实业务场景
        String businessRuleApiName = "customer_validation_rule";
        String businessObjectApiName = "CustomerObject";
        String businessRuleName = "email_format_validation";

        // Act - 执行完整的键构建流程
        // 1. 构建变更规则相关键
        String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(businessRuleApiName);
        String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(businessRuleApiName);
        
        // 2. 构建重复规则相关键
        String duplicateNameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(businessObjectApiName, businessRuleName);
        String duplicateDescKey = I18KeyBuildUtils.getDuplicatedRulesDescriptionKey(businessObjectApiName, businessRuleName);

        // Assert - 验证完整流程结果
        // 验证变更规则键
        assertEquals("change_rule.customer_validation_rule.change_prompt", changePromptKey);
        assertEquals("change_rule.customer_validation_rule.verification_prompt", verificationPromptKey);
        
        // 验证重复规则键
        assertEquals("repeat_rule.CustomerObject.email_format_validation.name", duplicateNameKey);
        assertEquals("repeat_rule.CustomerObject.email_format_validation.description", duplicateDescKey);
        
        // 验证键的业务语义
        assertTrue(changePromptKey.contains("customer_validation_rule"), "变更提示键应包含业务规则名称");
        assertTrue(verificationPromptKey.contains("customer_validation_rule"), "验证提示键应包含业务规则名称");
        assertTrue(duplicateNameKey.contains("CustomerObject"), "重复规则名称键应包含对象名称");
        assertTrue(duplicateNameKey.contains("email_format_validation"), "重复规则名称键应包含规则名称");
        assertTrue(duplicateDescKey.contains("CustomerObject"), "重复规则描述键应包含对象名称");
        assertTrue(duplicateDescKey.contains("email_format_validation"), "重复规则描述键应包含规则名称");
        
        // 验证键的唯一性
        java.util.Set<String> uniqueKeys = new java.util.HashSet<>();
        uniqueKeys.add(changePromptKey);
        uniqueKeys.add(verificationPromptKey);
        uniqueKeys.add(duplicateNameKey);
        uniqueKeys.add(duplicateDescKey);
        assertEquals(4, uniqueKeys.size(), "所有生成的键应该是唯一的");
    }
}
