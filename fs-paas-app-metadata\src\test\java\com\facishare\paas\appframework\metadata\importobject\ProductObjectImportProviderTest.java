package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductObjectImportProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IUniqueRule uniqueRule;

    @Spy
    private ProductObjectImportProvider productObjectImportProvider;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的产品API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的产品API名称")
    void testGetObjectCode_返回正确的产品API名称() {
        // 执行被测试方法
        String result = productObjectImportProvider.getObjectCode();

        // 验证结果
        assertEquals(Utils.PRODUCT_API_NAME, result);
        assertEquals("ProductObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportObject方法调用父类方法
     */
    @Test
    @DisplayName("getImportObject - 调用父类方法")
    void testGetImportObject_调用父类方法() {
        // 准备测试数据
        ImportObject expectedImportObject = ImportObject.builder()
                .objectCode(Utils.PRODUCT_API_NAME)
                .objectName("产品")
                .build();

        // 配置Mock行为
        doReturn(Optional.of(expectedImportObject))
                .when(productObjectImportProvider, "getImportObject", objectDescribe, uniqueRule);

        // 执行被测试方法
        Optional<ImportObject> result = productObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(expectedImportObject, result.get());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportType方法返回默认导入类型
     */
    @Test
    @DisplayName("getImportType - 返回默认导入类型")
    void testGetImportType_返回默认导入类型() {
        // 执行被测试方法
        ImportType result = productObjectImportProvider.getImportType(objectDescribe, uniqueRule);

        // 验证结果
        assertEquals(ImportType.DEFAULT, result);
        assertEquals(1, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOpenWorkFlow方法返回true
     */
    @Test
    @DisplayName("getOpenWorkFlow - 返回true")
    void testGetOpenWorkFlow_返回true() {
        // 执行被测试方法
        boolean result = productObjectImportProvider.getOpenWorkFlow(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsCheckOutOwner方法返回true
     */
    @Test
    @DisplayName("getIsCheckOutOwner - 返回true")
    void testGetIsCheckOutOwner_返回true() {
        // 执行被测试方法
        boolean result = productObjectImportProvider.getIsCheckOutOwner(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsRemoveOutTeamMember方法返回true
     */
    @Test
    @DisplayName("getIsRemoveOutTeamMember - 返回true")
    void testGetIsRemoveOutTeamMember_返回true() {
        // 执行被测试方法
        boolean result = productObjectImportProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - objectDescribe为null时的处理")
    void test各方法_objectDescribe为null时的处理() {
        // 测试getImportType
        ImportType importType = productObjectImportProvider.getImportType(null, uniqueRule);
        assertEquals(ImportType.DEFAULT, importType);

        // 测试getOpenWorkFlow
        boolean openWorkFlow = productObjectImportProvider.getOpenWorkFlow(null);
        assertTrue(openWorkFlow);

        // 测试getIsCheckOutOwner
        boolean checkOutOwner = productObjectImportProvider.getIsCheckOutOwner(null);
        assertTrue(checkOutOwner);

        // 测试getIsRemoveOutTeamMember
        boolean removeOutTeamMember = productObjectImportProvider.getIsRemoveOutTeamMember(null);
        assertTrue(removeOutTeamMember);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当uniqueRule为null时，getImportType方法的处理
     */
    @Test
    @DisplayName("getImportType - uniqueRule为null时的处理")
    void testGetImportType_uniqueRule为null时的处理() {
        // 执行被测试方法
        ImportType result = productObjectImportProvider.getImportType(objectDescribe, null);

        // 验证结果
        assertEquals(ImportType.DEFAULT, result);
        assertEquals(1, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试所有布尔方法的一致性，确保都返回true
     */
    @Test
    @DisplayName("布尔方法 - 一致性测试确保都返回true")
    void test布尔方法_一致性测试确保都返回true() {
        // 执行所有布尔方法
        boolean openWorkFlow = productObjectImportProvider.getOpenWorkFlow(objectDescribe);
        boolean checkOutOwner = productObjectImportProvider.getIsCheckOutOwner(objectDescribe);
        boolean removeOutTeamMember = productObjectImportProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证结果
        assertTrue(openWorkFlow);
        assertTrue(checkOutOwner);
        assertTrue(removeOutTeamMember);

        // 验证一致性
        assertEquals(openWorkFlow, checkOutOwner);
        assertEquals(checkOutOwner, removeOutTeamMember);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试产品对象的业务特性 - 支持默认导入和工作流
     */
    @Test
    @DisplayName("业务特性 - 产品对象支持默认导入和工作流")
    void test业务特性_产品对象支持默认导入和工作流() {
        // 执行被测试方法
        String objectCode = productObjectImportProvider.getObjectCode();
        ImportType importType = productObjectImportProvider.getImportType(objectDescribe, uniqueRule);
        boolean openWorkFlow = productObjectImportProvider.getOpenWorkFlow(objectDescribe);
        boolean checkOutOwner = productObjectImportProvider.getIsCheckOutOwner(objectDescribe);
        boolean removeOutTeamMember = productObjectImportProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证业务特性
        assertEquals(Utils.PRODUCT_API_NAME, objectCode);
        assertEquals(ImportType.DEFAULT, importType);
        assertTrue(openWorkFlow);
        assertTrue(checkOutOwner);
        assertTrue(removeOutTeamMember);

        // 验证支持默认导入的特性
        assertNotEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, importType);
        assertNotEquals(ImportType.UNSUPPORT_INSERT_IMPORT, importType);
        assertEquals(1, importType.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportObject方法当父类返回空Optional时的处理
     */
    @Test
    @DisplayName("getImportObject - 父类返回空Optional时的处理")
    void testGetImportObject_父类返回空Optional时的处理() {
        // 配置Mock行为
        doReturn(Optional.empty())
                .when(productObjectImportProvider, "getImportObject", objectDescribe, uniqueRule);

        // 执行被测试方法
        Optional<ImportObject> result = productObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

        // 验证结果
        assertFalse(result.isPresent());
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当所有参数为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - 所有参数为null时的处理")
    void test各方法_所有参数为null时的处理() {
        // 测试getImportType
        ImportType importType = productObjectImportProvider.getImportType(null, null);
        assertEquals(ImportType.DEFAULT, importType);

        // 测试布尔方法
        boolean openWorkFlow = productObjectImportProvider.getOpenWorkFlow(null);
        boolean checkOutOwner = productObjectImportProvider.getIsCheckOutOwner(null);
        boolean removeOutTeamMember = productObjectImportProvider.getIsRemoveOutTeamMember(null);

        // 验证结果
        assertTrue(openWorkFlow);
        assertTrue(checkOutOwner);
        assertTrue(removeOutTeamMember);

        // 验证一致性
        assertEquals(openWorkFlow, checkOutOwner);
        assertEquals(checkOutOwner, removeOutTeamMember);
    }
}
