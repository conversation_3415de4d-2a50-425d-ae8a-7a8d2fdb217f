package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.LanguageClientUtil;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.I18nTrans;
import com.facishare.paas.appframework.metadata.i18nKeyPlatform.OnTimeTranslate;
import com.facishare.paas.appframework.metadata.i18nKeyPlatform.Save;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IMultipleMultilingualTranslateService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.arg.MultiLingualInfo;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Language;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("i18nSettingService")
public class I18nSettingServiceImpl implements I18nSettingService {

    @Autowired
    private I18nSettingProxy i18nSettingProxy;
    @Autowired
    private LicenseService licenseService;
    @Resource
    private IMultipleMultilingualTranslateService multilingualTranslateService;

    @Override
    public Map<String, String> getTransValue(List<String> keys, boolean skipLicenseValidate, boolean isOnTime) {
        RequestContext requestContext = RequestContextManager.getContext();
        if (Objects.isNull(requestContext)) {
            return Maps.newHashMap();
        }
        String tenantId = String.valueOf(requestContext.getTenantId());
        String language = requestContext.getLang().getValue();
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(language)) {
            return Maps.newHashMap();
        }
        Map<String, Localization> localizationMap = getLocalization(keys, tenantId, skipLicenseValidate, isOnTime);
        if (CollectionUtils.empty(localizationMap)) {
            return Maps.newHashMap();
        }
        // log.info("getTransValue key: {}, value: {}, context: {}, language: {}", keys, localizationMap, requestContext, language);
        return localizationMap.keySet().stream().filter(x -> localizationMap.get(x) != null)
                .filter(x -> localizationMap.get(x).get(language, null) != null)
                .collect(Collectors.toMap(x -> x, x -> localizationMap.get(x).get(language, null)));
    }

    @Override
    public String getTransValue(String key, String defaultValue, boolean isOnTime) {
        RequestContext requestContext = RequestContextManager.getContext();
        if (Objects.isNull(requestContext)) {
            return defaultValue;
        }
        String tenantId = String.valueOf(requestContext.getTenantId());
        String language = requestContext.getLang().getValue();
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(language)) {
            return defaultValue;
        }
        return getTransValue(tenantId, key, defaultValue, language, isOnTime);
    }


    @Override
    public Map<String, Localization> getLocalization(List<String> keys, String tenantId, boolean skipLicenseValidate, boolean isOnTime) {

        if (CollectionUtils.empty(keys) || (!skipLicenseValidate && !licenseService.isSupportMultiLanguage(tenantId))) {
            return Maps.newHashMap();
        }
        if (!isOnTime || !RequestUtil.isCepRequest()) {
            return I18nClient.getInstance().get(keys, I18N.getContext().getTenantId());
        }

        List<OnTimeTranslate.Arg> argList = keys.stream().map(x -> OnTimeTranslate.Arg.builder().key(x).tenantId(tenantId).build()).collect(Collectors.toList());
        OnTimeTranslate.Result onTimeTransValue = OnTimeTranslate.Result.builder().build();
        try {
            onTimeTransValue = i18nSettingProxy.load(argList);
        } catch (Exception e) {
            log.error("i18nSettingProxy.getOnTimeTransValue: tenantId:{},keys:{}", tenantId, keys, e);
            return I18nClient.getInstance().get(keys, I18N.getContext().getTenantId());
        }
        Map<String, Localization> resultMap = Maps.newHashMap();
        CollectionUtils.nullToEmpty(onTimeTransValue.getResult()).forEach((key, value) -> resultMap.put(key, value.toLocalization()));
        return resultMap;
    }

    @Override
    public void customObjSave(Save.Arg arg, String tenantId) {
        if (Objects.isNull(arg)) {
            return;
        }
        List<Save.TransDataRow> dataRows = arg.getDataRows();
        if (CollectionUtils.empty(dataRows)) {
            return;
        }
        Map<String, String> keyToNewName = Maps.newHashMap();
        dataRows.forEach(x -> keyToNewName.put(x.getTranslateKey(), x.getTranslateValue()));
        syncTransValue(keyToNewName, arg.getLanguage(), tenantId);
    }

    @Override
    public void saveTransValue(Map<String, String> keyToNewName, boolean needLicenseCheck) {
        if (CollectionUtils.empty(keyToNewName)) {
            return;
        }
        RequestContext context = RequestContextManager.getContext();
        if (Objects.isNull(context)) {
            return;
        }
        String tenantId = String.valueOf(context.getTenantId());
        if (StringUtils.isBlank(tenantId)) {
            return;
        }
        if (needLicenseCheck && !licenseService.isSupportMultiLanguage(tenantId)) {
            return;
        }
        String language = context.getLang().getValue();
        syncTransValue(keyToNewName, language, tenantId);
    }

    @Override
    public void syncTransValue(Map<String, String> keyToNewName, String lang, String tenantId) {
        List<Localization> localizations = buildLocalizations(keyToNewName, lang, tenantId);
        I18nClient.getInstance().save4Translate(Long.parseLong(tenantId), localizations, false);
    }

    @Override
    public List<Localization> buildLocalizations(Map<String, String> keyToNewName, String lang, String tenantId) {
        if (CollectionUtils.empty(keyToNewName) || !licenseService.isSupportMultiLanguage(tenantId)) {
            return Lists.newArrayList();
        }
        Set<String> keys = keyToNewName.keySet();
        Map<String, Localization> onTimeTransValue = getLocalization(Lists.newArrayList(keys), tenantId, true, true);
        List<Localization> localizations = Lists.newArrayList();
        keyToNewName.forEach((key, value) -> {
            Localization translateInfo = onTimeTransValue.get(key);
            if (Objects.isNull(translateInfo)) {
                return;
            }
            String transValue = translateInfo.get(lang, null);
            if (!StringUtils.equals(transValue, value)) {
                Localization localization = new Localization();
                localization.set(lang, value);
                localization.setKey(key);
                localization.setTenantId(Long.parseLong(tenantId));
                localization.setTags(Lists.newArrayList("server"));
                localizations.add(localization);
            }
        });
        return localizations;
    }


    @Override
    public String getTransValue(String tenantId, String key, String defaultValue, String lang, boolean isOnTime) {
        if (!licenseService.isSupportMultiLanguage(tenantId)) {
            return defaultValue;
        }
        if (!isOnTime || !RequestUtil.isCepRequest()) {
            return I18NExt.getOnlyTextOrDefault(key, defaultValue);
        }
        Map<String, Localization> localization = getLocalization(Lists.newArrayList(key), tenantId, true, isOnTime);
        if (Objects.isNull(localization)) {
            return defaultValue;
        }
        Localization translateInfo = localization.get(key);
        if (Objects.isNull(translateInfo)) {
            return defaultValue;
        }
        return translateInfo.get(lang, defaultValue);
    }


    @Override
    public void syncTransValueIncludePreKey(String tenantId, List<I18nTrans.TransArg> transArgList, String lang) {
        if (CollectionUtils.empty(transArgList) || tenantId == null || StringUtils.isEmpty(lang)) {
            return;
        }
        if (!licenseService.isSupportMultiLanguage(tenantId)) {
            return;
        }
        List<String> keyList = Lists.newArrayList();
        transArgList.forEach(x -> keyList.add(x.getCustomKey())); // 翻译工作台key
        if (CollectionUtils.empty(keyList)) {
            return;
        }
        Map<String, Localization> keyToLocalization = getLocalization(keyList, tenantId, false, true);
        List<Localization> localizations = Lists.newArrayList();
        for (I18nTrans.TransArg transArg : transArgList) {
            String customKey = transArg.getCustomKey();
            Localization customLocalization = keyToLocalization.get(customKey);
            //有自定义翻译的时候，只要不一样，不管当前预设多语是否有值，都会同步
            if (Objects.nonNull(customLocalization)) {
                String customValue = customLocalization.get(lang,null);
                if (!StringUtils.equals(customValue, transArg.getName())) {
                    localizations.add(buildLocalization(tenantId, customKey, transArg.getName(), lang, Lists.newArrayList("server")));
                }
                continue;
            }
            //没有自定义翻译时，取预设多语，当前value与预设多语不一致时，则往自定义多语上同步value
            List<String> preKeyList = CollectionUtils.nullToEmpty(transArg.getPreKeyList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
            Localization allTransValueByOrder = I18NExt.getAllTransValueByOrder(preKeyList, Integer.valueOf(tenantId));
            String preTransValue = allTransValueByOrder.get(lang,null);
            if (CollectionUtils.notEmpty(allTransValueByOrder.getData()) && CollectionUtils.notEmpty(allTransValueByOrder.getData().values())
                    && !StringUtils.equals(preTransValue, transArg.getName())) {
                Map<Byte, String> newData = Maps.newHashMap(allTransValueByOrder.getData());
                Localization localizationParam = buildLocalization(tenantId, customKey, transArg.getName(), lang, Lists.newArrayList("server"));
                localizationParam.setData(newData);
                localizationParam.set(lang, transArg.getName());
                localizations.add(localizationParam);
            }
        }
        localizations.removeIf(x -> CollectionUtils.empty(x.getData()));
        //同步多语
        I18nClient.getInstance().save4Translate(Long.parseLong(tenantId), localizations, false);
    }

    private Localization buildLocalization(String tenantId, String key, String value, String
            lang, List<String> tags) {
        Localization localization = new Localization();
        localization.set(lang, value);
        localization.setKey(key);
        localization.setTenantId(Long.parseLong(tenantId));
        localization.setTags(tags);
        return localization;
    }


    public Map<String, String> getTransValue(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang, boolean isOnTime) {
        if (CollectionUtils.empty(transArgList) || tenantId == null || StringUtils.isEmpty(lang)) {
            return null;
        }
        List<String> customKeyList = transArgList.stream().map(I18nTrans.TransArg::getCustomKey).collect(Collectors.toList());
        Map<String, String> customTransValueMap;
        if (isOnTime) {
            customTransValueMap = getTransValue(customKeyList, false, true);
        } else {
            customTransValueMap = I18nClient.getInstance().get(customKeyList, tenantId, lang);
        }

        Map<String, String> result = Maps.newHashMap();
        for (I18nTrans.TransArg transArg : transArgList) {
            String customValue = customTransValueMap.get(transArg.getCustomKey());
            if (StringUtils.isNotEmpty(customValue)) {
                result.put(transArg.getCustomKey(), customValue);
                continue;
            }
            List<String> prekeyList = transArg.getPreKeyList();
            List<String> preKeyListRemoveTenantId = prekeyList.stream().map(x -> x.replace(tenantId + "_", "")).collect(Collectors.toList());
            String preValue = I18nClient.getInstance().getByOrder(preKeyListRemoveTenantId, tenantId, lang);
            if (StringUtils.isNotEmpty(preValue)) {
                result.put(transArg.getCustomKey(), preValue);
            }
        }
        return result;
    }

    @Override
    public List<Language> getTenantLanguage(String ei) {
        if (StringUtils.isBlank(ei)) {
            return Lists.newArrayList();
        }
        return LanguageClientUtil.getLanguages(ei);
    }

    @Override
    public List<String> getTenantLanguageTags(String ei) {
        if (StringUtils.isBlank(ei)) {
            return Lists.newArrayList();
        }
        return LanguageClientUtil.getLanguageTags(ei);
    }

    private boolean fieldNameAndRecordTypeGray(String ei) {
        if (StringUtils.isBlank(ei)) {
            return false;
        }
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIELD_NAME_RECORD_TYPE_FIELD_PACKET, ei);
    }

    @Override
    public List<I18nInfo> queryTranslation(String tenantId, String describeApiName, List<I18nInfo> i18nInfos) {
        if (!fieldNameAndRecordTypeGray(tenantId) || CollectionUtils.empty(i18nInfos)) {
            return Lists.newArrayList();
        }
        RequestContext requestContext = RequestContextManager.getContext();
        IActionContext context = Objects.isNull(requestContext) ? new ActionContext() :
                ActionContextExt.of(requestContext.getUser(), requestContext).getContext();
        List<MultiLingualInfo> multiLingualInfos = Lists.newArrayList();
        try {
            context.setLangs(getTenantLanguageTags(tenantId));
            multiLingualInfos = multilingualTranslateService.queryTranslation(tenantId, describeApiName,
                    I18nInfo.toMetaMultiLanguages(i18nInfos),
                    context);
        } catch (MetadataServiceException e) {
            log.warn("queryTranslation failed, ei{}, obj:{}, i18nInfo:{}",
                    tenantId, describeApiName,
                    i18nInfos.stream().map(x -> x.getApiName() + x.getType())
                            .collect(Collectors.toList()),
                    e);
        }
        return multiLingualInfos.stream()
                .map(I18nInfo::of)
                .collect(Collectors.toList());
    }

    @Override
    public void synTranslateToMetaQuietly(String tenantId, String describeApiName, List<I18nInfo> i18nInfos, IObjectDescribe describe) {
        if (!fieldNameAndRecordTypeGray(tenantId) || CollectionUtils.empty(i18nInfos) || StringUtils.isBlank(describeApiName)) {
            return;
        }
        RequestContext requestContext = RequestContextManager.getContext();
        IActionContext context = Objects.isNull(requestContext) ? new ActionContext() :
                ActionContextExt.of(requestContext.getUser(), requestContext).getContext();
        try {
            List<MultiLingualInfo> metaMultiLanguages = I18nInfo.toMetaMultiLanguages(i18nInfos).stream()
                    .filter(multiLingualInfo -> CollectionUtils.notEmpty(multiLingualInfo.getLanguageInfo()))
                    .collect(Collectors.toList());
            if(CollectionUtils.empty(metaMultiLanguages)) {
                return;
            }
            context.setLangs(getTenantLanguageTags(tenantId));
            if (Objects.isNull(describe)) {
                multilingualTranslateService.translate(tenantId, describeApiName, metaMultiLanguages, context);
            }
            else {
                multilingualTranslateService.translateWithObjectDescribe(describe, metaMultiLanguages, context);
            }
        } catch (MetadataServiceException e) {
            log.warn("synTranslation failed, ei{}, obj:{}, i18nInfo:{}",
                    tenantId, describeApiName,
                    i18nInfos.stream().map(I18nInfo::getApiName)
                            .collect(Collectors.toList()),
                    e);
        } catch (Exception e) {
            log.warn("synTranslation failed, ei{}, obj:{}",
                    tenantId, describeApiName,
                    e);
        }
    }

    @Override
    public void synTranslateToMetaQuietly(String tenantId, String describeApiName, List<I18nInfo> i18nInfos) {
        synTranslateToMetaQuietly(tenantId, describeApiName, i18nInfos, null);
    }

    /*
     * @description: 通过循环比较, 获取当前的name是否属于预置key之一, 没有的话返回null
     */
    @Override
    public Optional<Localization> getPreLocalization(String name, String ei, List<String> preKeys) {
        return I18NExt.getLocalization(name, ei, preKeys);
    }
}
