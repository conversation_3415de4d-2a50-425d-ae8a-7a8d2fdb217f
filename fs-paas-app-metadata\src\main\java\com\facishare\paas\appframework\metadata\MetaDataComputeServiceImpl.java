package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.count.CountFieldCalculatorFactory;
import com.facishare.paas.appframework.metadata.count.CountValues;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.quote.FillQuoteFieldValueArg;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceProxy;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.CountFieldResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.IdUtil;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.CALCULATE_FOR_UPDATE;

/**
 * 对元数据服务的封装
 * <p>
 * Created by yusb on 2017/12/14.
 */
@Slf4j
@Service("metaDataComputeService")
public class MetaDataComputeServiceImpl implements MetaDataComputeService {

    @Autowired
    private ObjectDataProxy dataProxy;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private MetaDataActionService metaDataActionService;
    @Autowired
    private MetaDataMiscService metaDataMiscService;
    @Autowired
    private QuoteValueService quoteValueService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private FieldRelationGraphService fieldRelationGraphService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private RecordTypeLogicService recordTypeLogicService;
    @Autowired
    private EnterpriseRelationServiceProxy enterpriseRelationService;

    @Override
    public void calculateAndUpdateByOriginalData(User user, String objectApiName, String dataId, Map<String, Object> beforeData) {
        if (Strings.isNullOrEmpty(objectApiName) || Strings.isNullOrEmpty(dataId)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("calculateAndUpdateByOriginalData");
        try {
            IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), objectApiName);
            stopWatch.lap("findObject");
            IObjectData objectData = metaDataFindService.findObjectDataIgnoreStatusAndFormula(user, dataId, objectApiName);
            stopWatch.lap("findObjectData");
            FieldRelationGraph graph = fieldRelationGraphService.buildReverseFullDependencyGraph(describe, null,
                    true, false, true, true, true);
            stopWatch.lap("buildGraph");

            List<IFieldDescribe> selfCalculateFields = Lists.newArrayList();
            Set<NodeEdgePair> refCalculateNodePairs = Sets.newHashSet();
            ObjectDescribeExt.of(describe).getActiveFieldDescribes().forEach(field -> {
                if (FieldDescribeExt.of(field).isCalculateFieldsNeedStoreInDB()) {
                    selfCalculateFields.add(field);
                }
                graph.getNode(describe.getApiName(), field.getApiName()).ifPresent(node -> {
                    graph.successors(node).forEach(successor -> {
                        Optional<RelateEdge> relateEdge = graph.edgeValue(node, successor);
                        relateEdge.filter(edge -> !edge.allMatchRelateType(RelateType.S2S))
                                .ifPresent(edge -> refCalculateNodePairs.add(NodeEdgePair.of(successor, edge)));
                    });
                });
            });

            if (!Boolean.TRUE.equals(objectData.isDeleted())) {
                calculateAndUpdateCalculateFields(user, Lists.newArrayList(objectData), selfCalculateFields, describe);
                stopWatch.lap("calculateAndUpdateSelfFields");
            }

            refCalculateNodePairs.stream().collect(Collectors.groupingBy(NodeEdgePair::getObjectApiName))
                    .forEach((refObjectApiName, nodePairs) -> {
                        List<NodeEdgePair> lookupNodePairs = nodePairs.stream()
                                .filter(NodeEdgePair::isCountField)
                                .collect(Collectors.toList());
                        if (CollectionUtils.notEmpty(lookupNodePairs)) {
                            calculateAndUpdateLookupFields(user, refObjectApiName, lookupNodePairs, objectData, beforeData);
                            stopWatch.lap("calculateAndUpdateLookupFields-" + refObjectApiName);
                        }

                        List<NodeEdgePair> relatedNodePairs = nodePairs.stream()
                                .filter(y -> !y.isCountField())
                                .collect(Collectors.toList());
                        if (CollectionUtils.notEmpty(relatedNodePairs)) {
                            calculateAndUpdateRelatedFields(user, dataId, refObjectApiName, relatedNodePairs);
                            stopWatch.lap("calculateAndUpdateRelatedFields-" + refObjectApiName);
                        }
                    });
        } finally {
            stopWatch.logSlow(100);
        }
    }

    private void calculateAndUpdateCalculateFields(User user, List<IObjectData> dataList, List<IFieldDescribe> calculateFields, IObjectDescribe describe) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(calculateFields)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("calculateAndUpdateCalculateFields-" + describe.getApiName());
        try {
            List<Quote> quotes = calculateFields.stream()
                    .filter(x -> FieldDescribeExt.of(x).isQuoteField() && x.isIndex()).map(x -> (Quote) x).collect(Collectors.toList());
            List<Count> counts = calculateFields.stream()
                    .filter(x -> FieldDescribeExt.of(x).isCountField()).map(x -> (Count) x).collect(Collectors.toList());
            List<IFieldDescribe> formulas = calculateFields.stream()
                    .filter(x -> !FieldDescribeExt.of(x).isQuoteField()
                            && !FieldDescribeExt.of(x).isCountField()
                            && (FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB() || FieldDescribeExt.of(x).hasFormulaDefaultValue()))
                    .collect(Collectors.toList());
            List<String> fieldApiNames = Lists.newArrayList();
            if (CollectionUtils.notEmpty(quotes)) {
                quoteValueService.fillQuoteFieldValue(User.systemUser(user.getTenantId()), dataList, describe,
                        null, true, quotes, null, true);
                fieldApiNames.addAll(quotes.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet()));
                stopWatch.lap("calculateQuote");
            }
            if (CollectionUtils.notEmpty(counts)) {
                calculateCountField(dataList, describe, counts);
                fieldApiNames.addAll(counts.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet()));
                stopWatch.lap("calculateCount");
            }
            if (CollectionUtils.notEmpty(formulas)) {
                expressionCalculateLogicService.bulkCalculate(describe, dataList, formulas);
                fieldApiNames.addAll(formulas.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet()));
                stopWatch.lap("calculateFormula");
            }
            if (CollectionUtils.notEmpty(fieldApiNames)) {
                metaDataActionService.batchUpdateWithFieldsForCalculateToPG(ActionContextExt.of(user).getContext(),
                        dataList, fieldApiNames);
                stopWatch.lap("updateFields");
            }
        } finally {
            stopWatch.logSlow(100);
        }
    }

    private void calculateAndUpdateLookupFields(User user, String refObjectApiName, List<NodeEdgePair> lookupNodePairs,
                                                IObjectData objectData, Map<String, Object> beforeData) {
        if (CollectionUtils.empty(lookupNodePairs)) {
            return;
        }
        Set<String> lookupIds = Sets.newHashSet();
        lookupNodePairs.forEach(lookupNodePair -> lookupNodePair.getRelateEdgeNodes().stream()
                .map(RelateEdge.RelateEdgeNode::getReferenceFieldName)
                .filter(lookupField -> !Strings.isNullOrEmpty(lookupField))
                .forEach(lookupField -> {
                    String lookupId = (String) objectData.get(lookupField);
                    if (!Strings.isNullOrEmpty(lookupId)) {
                        lookupIds.add(lookupId);
                    }
                    if (CollectionUtils.notEmpty(beforeData)) {
                        String oldLookupId = (String) beforeData.get(lookupField);
                        if (!Strings.isNullOrEmpty(oldLookupId)) {
                            lookupIds.add(oldLookupId);
                        }
                    }
                }));
        List<IObjectData> lookupDataList = metaDataFindService.findObjectDataByIdsExcludeInvalidIgnoreAll(user.getTenantId(),
                Lists.newArrayList(lookupIds), refObjectApiName);
        if (CollectionUtils.empty(lookupDataList)) {
            return;
        }
        IObjectDescribe lookupDescribe = describeLogicService.findObject(user.getTenantId(), refObjectApiName);
        List<IFieldDescribe> lookupFields = lookupNodePairs.stream().filter(n -> lookupDescribe.containsField(n.getFieldApiName()))
                .map(n -> lookupDescribe.getFieldDescribe(n.getFieldApiName()))
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
        calculateAndUpdateCalculateFields(user, lookupDataList, lookupFields, lookupDescribe);
    }

    private void calculateAndUpdateRelatedFields(User user, String lookupDataId, String detailApiName,
                                                 List<NodeEdgePair> relatedNodePairs) {
        if (CollectionUtils.empty(relatedNodePairs)) {
            return;
        }
        int DATA_BATCH_SIZE = 200;
        List<String> lookupFields = relatedNodePairs.stream()
                .flatMap(x -> x.getRelateEdgeNodes().stream().map(RelateEdge.RelateEdgeNode::getReferenceFieldName))
                .filter(lookupField -> !Strings.isNullOrEmpty(lookupField))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.empty(lookupFields)) {
            return;
        }
        String idOffset = null;
        int batchDataNum;

        IObjectDescribe detailDescribe = describeLogicService.findObject(user.getTenantId(), detailApiName);
        List<IFieldDescribe> calculateFields = relatedNodePairs.stream()
                .filter(x -> detailDescribe.containsField(x.getFieldApiName()))
                .map(x -> detailDescribe.getFieldDescribe(x.getFieldApiName()))
                .filter(IFieldDescribe::isActive)
                .filter(x -> FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(calculateFields)) {
            return;
        }
        do {
            // 构建 searchQuery
            SearchTemplateQuery searchQuery = buildSearchQuery(lookupDataId, lookupFields, idOffset, DATA_BATCH_SIZE);
            if (Objects.isNull(searchQuery)) {
                break;
            }

            // 查询数据
            QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQueryIgnoreAll(user, detailApiName, searchQuery);
            List<IObjectData> queryData = queryResult.getData();
            if (CollectionUtils.notEmpty(queryData)) {
                batchDataNum = queryData.size();
                idOffset = queryData.get(batchDataNum - 1).getId();
                calculateAndUpdateCalculateFields(user, queryData, calculateFields, detailDescribe);
            } else {
                batchDataNum = 0;
            }
        } while (batchDataNum >= DATA_BATCH_SIZE && StringUtils.isNotBlank(idOffset));
    }

    private SearchTemplateQuery buildSearchQuery(String lookupDataId, List<String> lookupFieldNames, String idOffset, int batchSize) {
        if (CollectionUtils.empty(lookupFieldNames)) {
            return null;
        }

        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        //不需要引用字段
        queryExt.setNeedReturnQuote(false);
        //不需要数据总数
        queryExt.setNeedReturnCountNum(false);
        queryExt.setOffset(0);
        queryExt.setLimit(batchSize);
        queryExt.resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.ID, false)));

        // 通过id设置条件来分页
        if (StringUtils.isNotBlank(idOffset)) {
            queryExt.addFilter(Operator.LT, IObjectData.ID, idOffset);
        }

        List<Wheres> wheres = lookupFieldNames.stream()
                .map(fieldApiName -> {
                    Wheres where = new Wheres();
                    IFilter filter = FilterExt.of(Operator.EQ, fieldApiName, lookupDataId).getFilter();
                    where.setFilters(Lists.newArrayList(filter));
                    return where;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(wheres)) {
            queryExt.setWheres(wheres);
        }
        return queryExt.toSearchTemplateQuery();
    }

    @Override
    public Map<String, Map<String, Object>> calculateAndUpdateFormulaFields(User user, String objectApiName, List<String> dataIds,
                                                                            List<String> fieldApiNames, boolean calculateRelateField) {
        if (CollectionUtils.empty(dataIds) || CollectionUtils.empty(fieldApiNames)) {
            return Maps.newHashMap();
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), objectApiName);
        List<IFieldDescribe> fieldList = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(fieldApiNames);
        fieldList.removeIf(x -> !FieldDescribeExt.of(x).isCalculateFieldsNeedStoreInDB());
        if (CollectionUtils.empty(fieldList)) {
            return Maps.newHashMap();
        }

        List<IFieldDescribe> fieldsForCalculate = Lists.newArrayList();
        Set<String> fieldApiNamesForCalculate = Sets.newHashSet();
        if (calculateRelateField) {
            FieldRelationGraph graph = fieldRelationGraphService.buildReverseDependencyGraph(Lists.newArrayList(objectDescribe),
                    true, true);
            fieldList.forEach(field -> {
                graph.getNode(objectDescribe.getApiName(), field.getApiName()).ifPresent(node -> {
                    fieldsForCalculate.add(field);
                    fieldApiNamesForCalculate.add(field.getApiName());
                    graph.getRelateNodes(node, Sets.newHashSet(objectDescribe.getApiName()), user.getTenantId()).forEach(relateNode -> {
                        if (Objects.equals(relateNode.getObjectApiName(), objectDescribe.getApiName())
                                && fieldApiNamesForCalculate.add(relateNode.getFieldApiName())) {
                            fieldsForCalculate.add(objectDescribe.getFieldDescribe(relateNode.getFieldApiName()));
                        }
                    });
                });
            });
        } else {
            fieldsForCalculate.addAll(fieldList);
            fieldApiNamesForCalculate.addAll(fieldList.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet()));
        }

        List<IObjectData> dataList = findObjectDataByIdsForCalculate(user.getTenantId(), dataIds, objectDescribe.getApiName());
        calculateAndUpdateCalculateFields(user, dataList, fieldsForCalculate, objectDescribe);

        Map<String, Map<String, Object>> result = Maps.newHashMap();
        dataList.forEach(data -> {
            Map<String, Object> calcResult = ObjectDataExt.of(data).toMap(Lists.newArrayList(fieldApiNamesForCalculate));
            result.put(data.getId(), calcResult);
        });
        return result;
    }

    @Override
    public Map<String, Object> calculateCountField(User user, String masterObjectApiName, String masterObjectDataId, List<Count> countFields) {
        if (CollectionUtils.empty(countFields) || Strings.isNullOrEmpty(masterObjectDataId)) {
            return Maps.newHashMap();
        }
        Map<String, Object> values = Maps.newHashMap();
        IObjectData masterData = ObjectDataExt.of(Maps.newHashMap()).getObjectData();
        masterData.setTenantId(user.getTenantId());
        masterData.setDescribeApiName(masterObjectApiName);
        masterData.setId(masterObjectDataId);
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        for (Count count : countFields) {
            SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(count.getWheres());
            IObjectDescribe detailDescribe = describeMap.computeIfAbsent(count.getSubObjectDescribeApiName(), k ->
                    describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), k));
            queryExt.handleCountFieldFilter(masterData, detailDescribe, count.getFieldApiName());
            Object calculateValue = getCountValue(user.getTenantId(), count, queryExt.getQuery(), null, false, detailDescribe);
            values.put(count.getApiName(), calculateValue);
        }

        return values;
    }

    @Override
    public void calculateCountField(List<IObjectData> objectDataList, IObjectDescribe objectDescribe, List<Count> countList) {
        Map<String, List<Count>> countGroups = countList.stream().collect(Collectors.groupingBy(Count::getSubObjectDescribeApiName));
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopyIfGray(objectDescribe.getTenantId(), countGroups.keySet());
        countGroups.keySet().stream().filter(describeMap::containsKey).forEach(x -> {
            List<Count> countGroup = countGroups.get(x);
            IObjectDescribe detailDescribe = describeMap.get(x);
            objectDataList.forEach(data -> calculateCountField(data, objectDescribe, detailDescribe, countGroup));
        });
    }

    @Override
    public void calculateCountField(IObjectData masterObjectData, IObjectDescribe masterDescribe,
                                    IObjectDescribe detailDescribe, List<Count> countFields) {
        IActionContext context = ActionContextExt.of(User.systemUser(masterDescribe.getTenantId())).getContext();
        calculateCountField(masterObjectData, masterDescribe, detailDescribe, countFields, context);
    }

    @Override
    public void calculateCountField(IObjectData masterObjectData, IObjectDescribe masterDescribe, IObjectDescribe detailDescribe,
                                    List<Count> countFields, IActionContext context) {
        calculateCountFieldWithContext(context, masterObjectData, masterDescribe, detailDescribe, countFields, false);
    }

    @Override
    public void calculateCountFieldFromDB(IObjectData masterObjectData, IObjectDescribe masterDescribe,
                                          IObjectDescribe detailDescribe, List<Count> countFields) {
        IActionContext context = ActionContextExt.of(User.systemUser(masterDescribe.getTenantId())).getContext();
        calculateCountFieldFromDB(masterObjectData, masterDescribe, detailDescribe, countFields, context);
    }

    @Override
    public void calculateCountFieldFromDB(IObjectData masterObjectData, IObjectDescribe masterDescribe, IObjectDescribe detailDescribe,
                                          List<Count> countFields, IActionContext context) {
        calculateCountFieldWithContext(context, masterObjectData, masterDescribe, detailDescribe, countFields, true);
    }

    private void calculateCountFieldWithContext(IActionContext context, IObjectData masterObjectData, IObjectDescribe masterDescribe,
                                                IObjectDescribe detailDescribe, List<Count> countFields, boolean fromDB) {
        if (Objects.isNull(masterObjectData) || CollectionUtils.empty(countFields)) {
            return;
        }

        for (Count count : countFields) {
            try {
                SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(count.getWheres());
                //指定走pg查询
                if (fromDB) {
                    queryExt.searchInDB();
                }
                queryExt.handleWheresFilter(masterObjectData);
                queryExt.handleCountFieldFilter(masterObjectData, detailDescribe, count.getFieldApiName());
                Object calculateValue = getCountValue(detailDescribe.getTenantId(), count, queryExt.getQuery(), context, false, detailDescribe);
                masterObjectData.set(count.getApiName(), calculateValue);
                if (RequestUtil.isDebugMode()) {
                    log.info("calcCount on:{},fn:{},di:{},r:{}", masterDescribe.getApiName(), count.getApiName(), masterObjectData.getId(), calculateValue);
                } else {
                    log.debug("calcCount on:{},fn:{},di:{},r:{}", masterDescribe.getApiName(), count.getApiName(), masterObjectData.getId(), calculateValue);
                }
            } catch (AppBusinessException e) {
                log.warn("calcCount failed,ei:{},on:{},fn:{},di:{}",
                        masterDescribe.getTenantId(), masterDescribe.getApiName(), count.getApiName(), masterObjectData.getId(), e);
                //如果是filter校验抛出的异常，不抛出以防止影响其他统计字段的计算
                if (!(e instanceof ValidateException)) {
                    throw e;
                }
            } catch (Exception e) {
                log.error("calcCount error,ei:{},on:{},fn:{},di:{}",
                        masterDescribe.getTenantId(), masterDescribe.getApiName(), count.getApiName(), masterObjectData.getId(), e);
                throw new MetaDataException(String.format("calculate count field failed:%s", masterDescribe.getApiName() + "." + count.getApiName()), e);
            }

        }

        if (RequestUtil.isCalculateLogEnable()) {
            List<String> countFieldNames = countFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            log.info("calcCount on:{},di:{},r:{}", masterDescribe.getApiName(), masterObjectData.getId(), ObjectDataExt.of(masterObjectData).toMap(countFieldNames));
        }
    }

    @Override
    public Object getCountValue(String tenantId, Count count, ISearchTemplateQuery query) {
        IActionContext context = ActionContextExt.of(User.systemUser(tenantId)).getContext();
        return getCountValue(tenantId, count, query, context, false, null);
    }

    private Object getCountValueWithoutFormat(String tenantId, Count count, ISearchTemplateQuery query,
                                              IActionContext context, boolean needDataAuth, IObjectDescribe describe) {
        SearchTemplateQueryExt.of(query).addIsDeletedFalseFilter();
        if (Objects.isNull(describe)) {
            describe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, count.getSubObjectDescribeApiName());
        }
        SearchTemplateQueryExt.of(query).validateWheresAndFilters(User.systemUser(tenantId), describe);
        IActionContext actionContext = Objects.isNull(context) ? new ActionContext() : context;
        Object calculateValue;
        try {
            if (needDataAuth) {
                calculateValue = dataProxy.getCountResultWithAuth(tenantId, count.getSubObjectDescribeApiName(),
                        (CountFieldDescribe) count, (SearchTemplateQuery) query, actionContext);
            } else {
                calculateValue = objectDataService.getCountResultWithContext(tenantId, count.getSubObjectDescribeApiName(),
                        (CountFieldDescribe) count, (SearchTemplateQuery) query, actionContext);
            }

            return calculateValue;
        } catch (MetadataServiceException e) {
            log.warn("getCountValue error,tenantId:{},objectApiName:{},fieldApiName:{},count:{},query:{}, context:{}, needAuth:{}",
                    tenantId, count.getDescribeApiName(), count.getApiName(), count, JSON.toJSONString(query), context, needDataAuth, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Object getCountValueWithoutFormat(String tenantId, Count count, ISearchTemplateQuery query) {
        return getCountValueWithoutFormat(tenantId, count, query, "");
    }

    @Override
    public Object getCountValueWithoutFormat(String tenantId, Count count, ISearchTemplateQuery query, String statOnEmptyResult) {
        ActionContextExt actionContextExt = ActionContextExt.of(User.systemUser(tenantId));
        IActionContext context = actionContextExt.getContext();
        if (StringUtils.isNotBlank(statOnEmptyResult)) {
            actionContextExt.setStatOnEmptyResult(statOnEmptyResult);
        }
        return getCountValueWithoutFormat(tenantId, count, query, context, false, null);
    }


    @Override
    public Object getCountValue(String tenantId, Count count, ISearchTemplateQuery query, IActionContext context,
                                boolean needDataAuth, IObjectDescribe describe) {
        Object calculateValue = getCountValueWithoutFormat(tenantId, count, query, context, needDataAuth, describe);
        return processCountValue(count, calculateValue);

    }

    @Override
    public Object getCountValueWithFunctionalCurrency(User user, Count count, ISearchTemplateQuery query) {
        IActionContext context = ActionContextExt.of(user).setCountWithFunctionalCurrency(true).getContext();
        return getCountValue(user.getTenantId(), count, query, context, true, null);
    }

    @Override
    public Object getCountValue(User user, Count count, ISearchTemplateQuery query) {
        IActionContext context = ActionContextExt.of(user).getContext();
        return getCountValue(user.getTenantId(), count, query, context, true, null);
    }

    @Override
    public BigDecimal getAggregateResult(User user, String describeApiName, String countFieldApiName, String countType,
                                         int decimalPlaces, ISearchTemplateQuery query) {
        SearchTemplateQueryExt.of(query).addIsDeletedFalseFilter();
        try {
            IActionContext context = ActionContextExt.of(user).getContext();
            return dataProxy.getAggregateResult(user.getTenantId(), describeApiName, countFieldApiName, countType,
                    decimalPlaces, (SearchTemplateQuery) query, context);
        } catch (MetadataServiceException e) {
            log.warn("getAggregateResult error,user:{},objectApiName:{},fieldApiName:{},count:{},query:{}",
                    user, describeApiName, countFieldApiName, countType, JSON.toJSONString(query), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private Object processCountValue(Count count, Object calculateValue) {
        return CountExt.of(count).formatResult(calculateValue);
    }

    private CountValues getCountValues(String tenantId, Count count, ISearchTemplateQuery query) {
        try {
            SearchTemplateQueryExt.of(query).addIsDeletedFalseFilter();
            CountFieldResult countFieldResult = dataProxy.getCountResultAll(tenantId, count.getSubObjectDescribeApiName(),
                    (CountFieldDescribe) count, (SearchTemplateQuery) query);
            return CountValues.of(countFieldResult, count);
        } catch (MetadataServiceException e) {
            log.warn("getCountValues error,tenantId:{},objectApiName:{},fieldApiName:{},count:{},query:{}",
                    tenantId, count.getDescribeApiName(), count.getApiName(), count, JSON.toJSONString(query), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectData calculateExpressionForCreateData(User user, String recordType, IObjectDescribe describe) {
        return calculateExpressionForCreateData(user, recordType, describe, null);
    }

    @Override
    public IObjectData calculateExpressionForCreateData(User user, String recordType, IObjectDescribe describe,
                                                        Consumer<IObjectData> modifyDataBeforeCalculate) {
        List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
        ObjectData objectData = new ObjectData();
        objectData.setId(IdUtil.generateId());
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(describe.getApiName());
        objectData.setRecordType(recordType);
        objectData.setCreatedBy(user.getUserId());
        handleDataOwnDepartment(user, objectData, describe);
        fillOutOwner(user, objectData, describe);
//        calculateEnterpriseRelation(user, objectData, describe);

        List<IFieldDescribe> calculateFieldList = Lists.newArrayList();
        List<Quote> quoteFields = Lists.newArrayList();
        fieldDescribes.stream().filter(IFieldDescribe::isActive).forEach(fieldDescribe -> {
            if (IFieldType.AUTO_NUMBER.equals(fieldDescribe.getType())) {
                return;
            }
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            if (fieldDescribeExt.isCalculateField()) {
                calculateFieldList.add(fieldDescribe);
            } else if (fieldDescribeExt.isQuoteField()) {
                quoteFields.add(fieldDescribeExt.getFieldDescribe());
            } else if (!ObjectDataExt.isValueEmpty(fieldDescribe.getDefaultValue())
                    && !ObjectDataExt.of(objectData).toMap().containsKey(fieldDescribe.getApiName())
                    && !ObjectDescribeExt.isSystemField(fieldDescribe.getApiName())) {
                Object defaultValue = fieldDescribe.getDefaultValue();
                if ((IFieldType.DEPARTMENT.equals(fieldDescribe.getType())
                        || IFieldType.EMPLOYEE.equals(fieldDescribe.getType()))
                        && defaultValue instanceof String) {
                    objectData.set(fieldDescribe.getApiName(), Lists.newArrayList(defaultValue));
                } else {
                    objectData.set(fieldDescribe.getApiName(), defaultValue);
                }
            }
        });

        fieldDescribes.stream()
                .filter(IFieldDescribe::isActive)
                .filter(x -> FieldDescribeExt.of(x).isCountField())
                .map(x -> (Count) x)
                .forEach(x -> objectData.set(x.getApiName(), CountExt.of(x).formatResult(null)));
        // 根据下游企业Id，补充『客户』『合作伙伴』id
        fillRelationOuterDataPrivilegeField(user, describe, objectData);

        // 执行计算之前修改数据，如：修改负责人字段的默认值等
        Optional.ofNullable(modifyDataBeforeCalculate)
                .ifPresent(it -> it.accept(objectData));

        List<IObjectData> dataList = Lists.newArrayList(objectData);
        expressionCalculateLogicService.bulkCalculateWithDependentData(describe, dataList,
                calculateFieldList, null, true);
        // 引用字段
        quoteValueService.fillQuoteFieldValue(user, dataList, describe, null, true, quoteFields, null);

        //lookup字段__r
        List<IFieldDescribe> lookupFields = ObjectDescribeExt.of(describe).filter(x -> FieldDescribeExt.of(x).isLookupField() && x.isActive());
        metaDataMiscService.fillObjectDataWithRefObject(describe, dataList, user, null, true, lookupFields);

        return ObjectDataExt.of(objectData).remove(Sets.newHashSet(IObjectData.ID, IObjectData.TENANT_ID,
                IObjectData.IS_DELETED)).getObjectData();
    }

    private void fillRelationOuterDataPrivilegeField(User user, IObjectDescribe describe, IObjectData data) {
        if (!user.isOutUser()) {
            return;
        }
        List<IObjectReferenceField> dataPrivilegeFields = ObjectDescribeExt.of(describe).getSupportRelationOuterOwnerFields();
        if (CollectionUtils.empty(dataPrivilegeFields)) {
            return;
        }
        // 根据下游企业Id，补充『客户』『合作伙伴』id
        for (IObjectReferenceField referenceField : dataPrivilegeFields) {
            String referenceId = data.get(referenceField.getApiName(), String.class);
            if (Strings.isNullOrEmpty(referenceId)) {
                String objectId = enterpriseRelationService.getUpstreamMapperObjectId(user, referenceField.getTargetApiName());
                data.set(referenceField.getApiName(), objectId);
            }
        }
    }

    private void handleDataOwnDepartment(User user, IObjectData objectData, IObjectDescribe describe) {
        // 从对象，新建页面不需要下发 负责人、归属部门字段、归属组织字段
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            return;
        }
        //负责人字段存在过滤条件，新建页面不需要下发 负责人、归属部门字段、归属组织字段
        Optional<IFieldDescribe> ownerFiled = ObjectDescribeExt.of(describe).getOwnerField();
        if (ownerFiled.isPresent() && CollectionUtils.notEmpty((Collection<?>) ownerFiled.get().get("wheres"))) {
            return;
        }
        //下游新建数据，并且负责人分配类型为手动的话，则不需要填充负责人集归属部门归属组织字段
        if (user.isOutUser() && ObjectDescribeExt.of(describe).getExpectEmployeeAllocateRuleByGray(user, false)) {
            return;
        }
        // 新建页面回填负责人和归属部门（组织）字段
        ObjectDataExt.of(objectData).setDataOwnerIfAbsent(user);
        // 灰度企业，下游新建页面不需要下发归属部门和归属组织
        if (user.isOutUser() && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_USER_FILL_DEPT_ORG, user.getTenantId())) {
            return;
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILL_DEPT_AND_ORG, user.getTenantId())) {
            fillDeptAndOrg(describe, objectData, user);
            return;
        }
        OrganizationInfo organizationInfo = orgService.findMainOrgAndDeptByUserId(user.getTenantId(),
                user.getUserId(), Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        fillDept(objectData, organizationInfo, user);
        fillOrg(objectData, organizationInfo, describe, user);
    }

    private void fillDeptAndOrg(IObjectDescribe describe, IObjectData objectData, User user) {
        fillDept(objectData, user);
        fillOrg(objectData, describe, user);
    }

    /**
     * 下游创建、补充外部负责人
     *
     * @param user
     * @param objectData
     */
    private void fillOutOwner(User user, IObjectData objectData, IObjectDescribe describe) {
        ObjectDataExt.OwnerPolicy policy = ObjectDataExt.OwnerPolicy.builder()
                .allowOutUserByArg(true)
                .outUserAssignOwner(ObjectDescribeExt.of(describe).getExpectEmployeeAllocateRuleByGray(user, false))
                .defaultDataOwnerId(() -> enterpriseRelationService.getDefaultDataOwnerByUser(user).orElse(null))
                .build();
        ObjectDataExt.of(objectData).setOutUser(user, policy);
    }

    private void fillOrg(IObjectData data, OrganizationInfo organizationInfo, IObjectDescribe describe, User user) {
        if (ObjectDescribeExt.of(describe).isOpenOrganization()) {
            if ((!user.isOutUser() && Strings.isNullOrEmpty(user.getUserId()))
                    || (user.isOutUser() && Strings.isNullOrEmpty(user.getUpstreamOwnerIdOrUserId()))
                    || User.SUPPER_ADMIN_USER_ID.equals(user.getUserId())) {
                ObjectDataExt.of(data).setDataOwnOrganization(Lists.newArrayList(User.COMPANY_ID));
                data.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_ORGANIZATION), I18NExt.text(I18NKey.WHOLE_GROUP));
            } else {
                String userId = user.getUpstreamOwnerIdOrUserId();
                ObjectDataExt.of(data).setDataOwnOrgByDeptInfo(organizationInfo.getMainOrg(userId));
                data.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_ORGANIZATION), organizationInfo.getMainOrgName(userId));
            }
        }
    }

    private void fillOrg(IObjectData objectData, IObjectDescribe describe, User user) {
        if (!ObjectDescribeExt.of(describe).isOpenOrganization()) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<String> orgIds = objectDataExt.getDataOwnOrganization();
        // 数据中没有归属部门,获取负责人或操作人的部门
        if (CollectionUtils.empty(orgIds)) {
            String userId = objectDataExt.getOwnerId().orElseGet(user::getUpstreamOwnerIdOrUserId);
            // 超级管理员使用 999999
            if (User.SUPPER_ADMIN_USER_ID.equals(userId) || Strings.isNullOrEmpty(userId)) {
                objectDataExt.setDataOwnOrganization(Lists.newArrayList(User.COMPANY_ID));
                objectDataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_ORGANIZATION), I18NExt.text(I18NKey.WHOLE_GROUP));
                return;
            }
            OrganizationInfo organizationInfo = orgService.findMainOrgAndDeptByUserId(user.getTenantId(), user.getUserId(), Lists.newArrayList(userId));
            objectDataExt.setDataOwnOrgByDeptInfo(organizationInfo.getMainOrg(userId));
            objectDataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_ORGANIZATION), organizationInfo.getMainOrgName(userId));
            return;
        }
        // 补充已有数据中部门的 __r
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), orgIds);
        Map<String, QueryDeptInfoByDeptIds.DeptInfo> deptInfoMap = deptInfos.stream().collect(Collectors.toMap(QueryDeptInfoByDeptIds.DeptInfo::getDeptId, Function.identity(), (x, y) -> x));

        String deptName = orgIds.stream()
                .map(deptInfoMap::get)
                .filter(Objects::nonNull)
                .map(QueryDeptInfoByDeptIds.DeptInfo::getDeptName)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        objectDataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_ORGANIZATION), deptName);
    }

    private void fillDept(IObjectData objectData, OrganizationInfo organizationInfo, User user) {
        if ((!user.isOutUser() && Strings.isNullOrEmpty(user.getUserId()))
                || (user.isOutUser() && Strings.isNullOrEmpty(user.getUpstreamOwnerIdOrUserId()))
                || User.SUPPER_ADMIN_USER_ID.equals(user.getUserId())) {
            ObjectDataExt.of(objectData).setDataOwnDepartmentId(User.COMPANY_ID);
            objectData.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_DEPARTMENT), I18NExt.text(I18NKey.COMPANY_WIDE));
        } else {
            String userId = user.getUpstreamOwnerIdOrUserId();
            ObjectDataExt.of(objectData).setDataOwnDepartmentId(organizationInfo.getMainDeptId(userId));
            objectData.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_DEPARTMENT), organizationInfo.getMainDeptName(userId));
        }
    }

    private void fillDept(IObjectData objectData, User user) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        String departmentId = objectDataExt.getDataOwnDepartmentId();
        // 数据中没有归属部门,获取负责人或操作人的部门
        if (Strings.isNullOrEmpty(departmentId)) {
            String userId = objectDataExt.getOwnerId().orElseGet(user::getUpstreamOwnerIdOrUserId);
            // 超级管理员使用 999999
            if (User.SUPPER_ADMIN_USER_ID.equals(userId) || Strings.isNullOrEmpty(userId)) {
                objectDataExt.setDataOwnDepartmentId(User.COMPANY_ID);
                objectDataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_DEPARTMENT), I18NExt.text(I18NKey.COMPANY_WIDE));
                return;
            }
            OrganizationInfo organizationInfo = orgService.findMainOrgAndDeptByUserId(user.getTenantId(), user.getUserId(), Lists.newArrayList(userId));
            String mainDeptId = organizationInfo.getMainDeptId(userId);
            String mainDeptName = organizationInfo.getMainDeptName(userId);
            objectDataExt.setDataOwnDepartmentId(mainDeptId);
            objectDataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_DEPARTMENT), mainDeptName);
            return;
        }

        // 补充已有数据中部门的 __r
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), Lists.newArrayList(departmentId));
        for (QueryDeptInfoByDeptIds.DeptInfo deptInfo : deptInfos) {
            if (Objects.equals(departmentId, deptInfo.getDeptId())) {
                objectDataExt.setDataOwnDepartmentId(deptInfo.getDeptId());
                objectDataExt.set(FieldDescribeExt.getLookupNameByFieldName(ObjectDataExt.DATA_OWN_DEPARTMENT), deptInfo.getDeptName());
                break;
            }
        }
    }

    @Override
    public void calculateAndUpdateCountFields(User user, List<String> masterDataIds, IObjectDescribe masterDescribe, List<Count> countFieldList) {
        if (CollectionUtils.empty(masterDataIds) || CollectionUtils.empty(countFieldList)) {
            return;
        }

        log.debug("calculateAndUpdateCountFields,user:{},masterDataIds:{},masterApiName:{},countFieldList:{}",
                user, masterDataIds, masterDescribe.getApiName(), countFieldList.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList()));

        List<String> relatedApiNames = countFieldList.stream().map(Count::getSubObjectDescribeApiName).distinct().collect(Collectors.toList());

        // 1. 找到这个对象下需要统计的从对象和关联对象。
        Map<String, IObjectDescribe> relatedDescribes = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), relatedApiNames);
        if (CollectionUtils.empty(relatedDescribes)) {
            log.warn("ignore calculateAndUpdateCountFields:no related describe,user:{},masterDataIds:{},masterApiName:{},countFieldList:{}",
                    user, masterDataIds, masterDescribe.getApiName(), countFieldList);
            return;
        }

        //2. 循环处理各个关联对象
        IActionContext context = ActionContextExt.of(user).getContext();
        relatedDescribes.forEach((k, v) -> {
            List<Count> toCalculateFields = countFieldList.stream().filter(x -> x.getSubObjectDescribeApiName().equals(k)).collect(Collectors.toList());
            calculateAndUpdateMasterCountFields(context, masterDataIds, masterDescribe, v, toCalculateFields);
        });
    }

    @Override
    public void calculateAndUpdateMasterCountFields(IActionContext context, List<String> masterDataIds, IObjectDescribe masterDescribe,
                                                    IObjectDescribe detailDescribe, List<Count> countFieldList) {
        if (CollectionUtils.empty(countFieldList) || CollectionUtils.empty(masterDataIds)) {
            return;
        }

        List<IObjectData> masterDataList = findObjectDataByIdsForCalculate(context.getEnterpriseId(), masterDataIds, masterDescribe.getApiName());
        calculateAndUpdateMasterCountFieldsWithData(context, masterDescribe, masterDataList, detailDescribe, countFieldList);
    }

    @Override
    public void calculateAndUpdateMasterCountFieldsWithData(IActionContext context, IObjectDescribe masterDescribe, List<IObjectData> masterDataList,
                                                            IObjectDescribe detailDescribe, List<Count> countFieldList) {
        if (CollectionUtils.empty(countFieldList) || CollectionUtils.empty(masterDataList)) {
            return;
        }
        masterDataList.forEach(masterData -> calculateCountFieldWithContext(context, masterData, masterDescribe, detailDescribe, countFieldList, false));
        List<String> countFieldNames = countFieldList.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        metaDataActionService.batchUpdateWithFieldsForCalculateToPG(context, masterDataList, countFieldNames);
    }

    @Override
    public void calculateCountFieldsInMemory(IObjectData masterData, Map<String, List<IObjectData>> detailDataMap, Map<String, IObjectDescribe> describeMap) {
        IObjectDescribe masterDescribe = describeMap.get(masterData.getDescribeApiName());
        calculateCountFieldsInMemory(masterData, detailDataMap, describeMap, ObjectDescribeExt.of(masterDescribe).getCountFields());
    }

    @Override
    public void calculateCountFieldsInMemory(IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                                             Map<String, IObjectDescribe> describeMap, List<Count> countFields) {
        if (CollectionUtils.empty(detailDataMap)) {
            return;
        }
        Map<String, Object> resultMap = Maps.newLinkedHashMap();
        detailDataMap.forEach((k, v) -> countFields.stream().filter(x -> k.equals(x.getSubObjectDescribeApiName())).forEach(c -> {
            CountFieldCalculatorFactory.getCountFieldCalculator(c.getCountType()).calculate(masterData, v, c, describeMap.get(k));
            resultMap.put(c.getApiName(), masterData.get(c.getApiName()));
        }));
        if (RequestUtil.isCalculateLogEnable()) {
            log.info("calcCount on:{},di:{},r:{}", masterData.getDescribeApiName(), masterData.getId(), resultMap);
        }
    }

    @Override
    public void calculateCountFieldsWithDetailDataAndDbValue(IObjectData masterData, List<IObjectData> detailDataList, IObjectDescribe masterDescribe,
                                                             IObjectDescribe detailDescribe, List<Count> countFieldList) {
        if (CollectionUtils.empty(countFieldList) || CollectionUtils.empty(detailDataList)) {
            return;
        }

        Map<String, Object> resultMap = Maps.newLinkedHashMap();
        List<String> detailDataIds = detailDataList.stream().filter(x -> ObjectDataExt.of(x).hasId()).map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> dbDetailDataList = metaDataFindService.findObjectDataByIdsIgnoreFormula(detailDescribe.getTenantId(),
                detailDataIds, detailDescribe.getApiName());

        countFieldList.forEach(x -> {
            SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(x.getWheres());
            queryExt.handleWheresFilter(masterData);
            queryExt.handleCountFieldFilter(masterData, detailDescribe, x.getFieldApiName());

            ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                    .describeExt(ObjectDescribeExt.of(detailDescribe))
                    .queryExt(queryExt)
                    .filterLabel(I18N.text(I18NKey.COUNT_FIELD, masterDescribe.getDisplayName()) + "." + x.getLabel())
                    .build();
            List<IObjectData> filterResult = dataFilter.doFilter(detailDataList);
            List<IObjectData> dbFilterResult = dataFilter.doFilter(dbDetailDataList);

            if (CollectionUtils.notEmpty(filterResult) || CollectionUtils.notEmpty(dbFilterResult)) {
                if (CollectionUtils.notEmpty(dbDetailDataList)) {
                    List<String> dbDetailDataIds = dbDetailDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
                    queryExt.addFilter(Operator.NIN, IObjectData.ID, dbDetailDataIds);
                }
                //max、min以及average的统计字段需要从数据库里取老数据出来重新计算
                CountValues countValues = new CountValues();
                if (Count.TYPE_AVERAGE.equals(x.getCountType())) {
                    countValues = getCountValues(detailDescribe.getTenantId(), x, queryExt.getQuery());
                } else if (CollectionUtils.notEmpty(dbDetailDataList)) {
                    IActionContext context = ActionContextExt.of(User.systemUser(detailDescribe.getTenantId())).getContext();
                    Object dbValue = getCountValue(detailDescribe.getTenantId(), x, queryExt.getQuery(), context, false, detailDescribe);
                    masterData.set(x.getApiName(), dbValue);
                }
                CountFieldCalculatorFactory.getCountFieldCalculator(x.getCountType()).calculateWithDbValue(masterData,
                        detailDataList, x, detailDescribe, countValues);
                resultMap.put(x.getApiName(), masterData.get(x.getApiName()));
            }
        });
        if (RequestUtil.isCalculateLogEnable()) {
            log.info("calcCount on:{},di:{},r:{}", masterDescribe.getApiName(), masterData.getId(), resultMap);
        }
    }

    @Override
    public void calculateCountFieldsWithDbDetailDataAndDbValue(IObjectData masterData, List<IObjectData> detailDataList, IObjectDescribe masterDescribe,
                                                               IObjectDescribe detailDescribe, List<Count> countFieldList, List<IObjectData> dbDetailDataList) {
        if (CollectionUtils.empty(countFieldList) || (CollectionUtils.empty(detailDataList) && CollectionUtils.empty(dbDetailDataList))) {
            return;
        }

        Map<String, Object> resultMap = Maps.newLinkedHashMap();
        List<String> detailDataIds;
        if (CollectionUtils.notEmpty(dbDetailDataList)) {
            detailDataIds = dbDetailDataList.stream().filter(x -> ObjectDataExt.of(x).hasId()).map(DBRecord::getId).collect(Collectors.toList());
        } else {
            detailDataIds = CollectionUtils.nullToEmpty(detailDataList).stream().filter(x -> ObjectDataExt.of(x).hasId()).map(DBRecord::getId).collect(Collectors.toList());
        }

        countFieldList.forEach(x -> {
            SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(x.getWheres());
            queryExt.handleWheresFilter(masterData);
            queryExt.handleCountFieldFilter(masterData, detailDescribe, x.getFieldApiName());

            if (CollectionUtils.notEmpty(detailDataIds)) {
                queryExt.addFilter(Operator.NIN, IObjectData.ID, detailDataIds);
            }
            //max、min以及average的统计字段需要从数据库里取老数据出来重新计算
            CountValues countValues = new CountValues();
            if (Count.TYPE_AVERAGE.equals(x.getCountType())) {
                countValues = getCountValues(detailDescribe.getTenantId(), x, queryExt.getQuery());
            } else if (CollectionUtils.notEmpty(dbDetailDataList)) {
                IActionContext context = ActionContextExt.of(User.systemUser(detailDescribe.getTenantId())).getContext();
                Object dbValue = getCountValue(detailDescribe.getTenantId(), x, queryExt.getQuery(), context, false, detailDescribe);
                masterData.set(x.getApiName(), dbValue);
            }
            CountFieldCalculatorFactory.getCountFieldCalculator(x.getCountType()).calculateWithDbValue(masterData,
                    detailDataList, x, detailDescribe, countValues);
            resultMap.put(x.getApiName(), masterData.get(x.getApiName()));
        });
        if (RequestUtil.isCalculateLogEnable()) {
            log.info("calcCount on:{},di:{},r:{}", masterDescribe.getApiName(), masterData.getId(), resultMap);
        }
    }

    @Override
    public List<String> batchCalculate(User user, List<IObjectData> dataList, IObjectDescribe describe, List<String> calculateFieldNames,
                                       boolean calculateFormulaOnly, boolean mergeData) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(calculateFieldNames)) {
            return Lists.newArrayList();
        }

        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".batchCalculateForInner");

        if (!calculateFormulaOnly && mergeData) {
            metaDataFindService.mergeWithDbData(describe.getTenantId(), describe.getApiName(), dataList);
            stopWatch.lap("mergeWithDbData");
        }

        Map<String, List<String>> calculateFieldNameMap = Maps.newHashMap();
        if (calculateFormulaOnly) {
            //详情页处理全部的计算字段
            calculateFieldNameMap.put(describe.getApiName(), calculateFieldNames);
            List<IFieldDescribe> fieldList = ObjectDescribeExt.of(describe).getFieldByApiNames(calculateFieldNames);
            expressionCalculateLogicService.bulkCalculate(describe, dataList, fieldList);
            stopWatch.lap("bulkCalculate");
        } else {
            //新建、编辑页面计算部分计算字段和默认值
            CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFields(calculateFieldNames, describe);
            calculateFields.getCalculateFieldMap().forEach((k, v) ->
                    calculateFieldNameMap.put(k, v.stream().map(x -> x.getFieldName()).collect(Collectors.toList())));
            stopWatch.lap("computeCalculateFields");

            batchCalculateBySortFields(user, dataList, calculateFields.getDescribeMap(), calculateFields.getCalculateFieldMap());
            stopWatch.lap("batchCalculateBySortFields");
        }

        stopWatch.logSlow(500);

        return calculateFieldNameMap.get(describe.getApiName());
    }

    @Override
    public void batchCalculateBySortFields(User user,
                                           List<IObjectData> dataList,
                                           Map<String, IObjectDescribe> describeMap,
                                           Map<String, List<RelateField>> calculateFieldMap) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(calculateFieldMap)) {
            return;
        }

        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".batchCalculateBySortFields");

        List<FieldNodeExt> calculateFieldList = Lists.newArrayList();
        calculateFieldMap.forEach((k, v) -> calculateFieldList.addAll(v.stream()
                .filter(x -> describeMap.get(k).containsField(x.getFieldName())).map(x -> {
                    IFieldDescribe fieldDescribe = describeMap.get(k).getFieldDescribe(x.getFieldName());
                    return FieldNodeExt.of(k, fieldDescribe, x.getOrder());
                }).collect(Collectors.toList())));
        IObjectDescribe describe = describeMap.get(dataList.get(0).getDescribeApiName());

        List<FieldNodeExt> lookupFieldList = calculateFieldList.stream().filter(FieldNodeExt::isLookupField).collect(Collectors.toList());
        calculateFieldList.removeAll(lookupFieldList);
        List<FieldNodeExt> quoteFieldList = calculateFieldList.stream().filter(FieldNodeExt::isQuoteField).collect(Collectors.toList());
        calculateFieldList.removeAll(quoteFieldList);
        List<List<FieldNodeExt>> fieldGroups = FieldNodeExt.sortAndClassifyNodeExtList(calculateFieldList);
        List<List<FieldNodeExt>> quoteFieldGroups = FieldNodeExt.sortAndClassifyNodeExtList(quoteFieldList);
        List<List<FieldNodeExt>> lookupFieldGroups = FieldNodeExt.sortAndClassifyNodeExtList(lookupFieldList);
        stopWatch.lap("sortAndClassifyNodeExtList");

        //先计算lookup字段的默认值，因为其他默认值或计算字段可能依赖了这些lookup字段
        lookupFieldGroups.forEach(fieldNodes -> {
            List<IFieldDescribe> fields = fieldNodes.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
            expressionCalculateLogicService.bulkCalculate(describe, dataList, fields);
            stopWatch.lap("calculateLookupFields");
        });

        IObjectDescribe masterDescribe = null;
        String masterApiName = null;
        MasterDetailFieldDescribe mdField = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().orElse(null);
        Map<String, IObjectData> masterDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> masterId2DetailDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> dependentDataMap = Maps.newHashMap();
        List<String> excludeLookupApiNames = Lists.newArrayList(describe.getApiName());

        if (mdField != null) {
            masterApiName = mdField.getTargetApiName();
            excludeLookupApiNames.add(masterApiName);
            if (calculateFieldMap.containsKey(masterApiName)) {
                masterDescribe = describeMap.get(masterApiName);
                List<String> masterDataIdList = dataList.stream().map(x -> x.get(mdField.getApiName(), String.class))
                        .filter(x -> !Strings.isNullOrEmpty(x)).distinct().collect(Collectors.toList());
                List<IObjectData> masterDataList = findObjectDataByIdsForCalculate(describe.getTenantId(),
                        masterDataIdList, masterApiName);
                masterDataMap = masterDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x));
                masterId2DetailDataMap = dataList.stream()
                        .collect(Collectors.groupingBy(x -> StringUtils.trimToEmpty(x.get(mdField.getApiName(), String.class))));

                if (CollectionUtils.notEmpty(masterDataList)) {
                    dependentDataMap.put(masterApiName, masterDataList);
                }

                stopWatch.lap("findMasterObjectData");
            }
        }

        //查询lookup对象，用于计算lookup对象统计字段  （原有实时计算只包括主从对象）
        String apiName = describe.getApiName();
        List<FieldNodeExt> lookupCountField = calculateFieldList.stream()
                .filter(x -> !excludeLookupApiNames.contains(x.getObjectApiName()))
                .filter(FieldNodeExt::isCountField)
                .collect(Collectors.toList());
        Map<String, Set<String>> lookupDataIds = Maps.newHashMap();
        Map<FieldNodeExt, Map<String, List<IObjectData>>> relateDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> lookupDataMap = Maps.newHashMap();

        if (CollectionUtils.notEmpty(lookupCountField)) {
            lookupCountField.forEach(x -> {
                IObjectDescribe objectDescribe = describeMap.get(x.getObjectApiName());
                Count count = (Count) objectDescribe.getFieldDescribe(x.getFieldApiName());
                if (StringUtils.equals(apiName, count.getSubObjectDescribeApiName())) {
                    IFieldDescribe refField = describeMap.get(count.getSubObjectDescribeApiName()).getFieldDescribe(count.getFieldApiName());
                    lookupDataIds.putIfAbsent(x.getObjectApiName(), Sets.newHashSet());
                    lookupDataIds.get(x.getObjectApiName()).addAll(dataList.stream()
                            .filter(y -> !ObjectDataExt.isValueEmpty(y.get(refField.getApiName())))
                            .map(y -> (String) y.get(refField.getApiName()))
                            .collect(Collectors.toSet()));

                    Map<String, List<IObjectData>> relateMap = dataList.stream().filter(y -> !ObjectDataExt.isValueEmpty(y.get(refField.getApiName())))
                            .collect(Collectors.groupingBy(y -> y.get(refField.getApiName(), String.class)));
                    relateDataMap.put(x, relateMap);
                }
            });

            lookupDataIds.forEach((k, v) -> {
                List<IObjectData> lookupDataList = findObjectDataByIdsForCalculate(describe.getTenantId(), v, k);
                lookupDataMap.put(k, lookupDataList);
                if (CollectionUtils.notEmpty(lookupDataList)) {
                    dependentDataMap.put(k, lookupDataList);
                }
            });

            stopWatch.lap("findLookupObjectData");
        }

        //计算统计字段、计算字段、默认值
        for (List<FieldNodeExt> fieldList : fieldGroups) {
            FieldNodeExt fieldNodeExt = fieldList.get(0);
            if (fieldNodeExt.isCountField()) {
                if (StringUtils.equals(masterApiName, fieldNodeExt.getObjectApiName())) {
                    //原有计算统计字段逻辑
                    List<Count> countList = fieldList.stream().map(x -> (Count) x.getFieldDescribe()).collect(Collectors.toList());
                    for (IObjectData data : masterDataMap.values()) {
                        calculateCountFieldsWithDetailDataAndDbValue(data, masterId2DetailDataMap.get(data.getId()), masterDescribe, describe, countList);
                    }
                    stopWatch.lap("calculateMasterCountFields");
                } else if (CollectionUtils.notEmpty(lookupDataMap) && CollectionUtils.notEmpty(lookupDataMap.get(fieldNodeExt.getObjectApiName()))) {
                    List<Count> countList = fieldList.stream().map(x -> (Count) x.getFieldDescribe()).collect(Collectors.toList());
                    List<IObjectData> objectData = lookupDataMap.get(fieldNodeExt.getObjectApiName());
                    Map<String, List<IObjectData>> relateData = relateDataMap.get(fieldNodeExt);
                    if (CollectionUtils.empty(relateData)) {
                        log.warn("relateData is empty, apiName:{}, fieldNode:{}", apiName, fieldNodeExt.getFieldNode());
                        continue;
                    }
                    countList.forEach(count -> objectData.forEach(x ->
                            calculateCountFieldsWithDetailDataAndDbValue(x, relateData.get(x.getId()), describeMap.get(fieldNodeExt.getObjectApiName()),
                                    describeMap.get(count.getSubObjectDescribeApiName()), Lists.newArrayList(count))));
                    stopWatch.lap("calculateLookupCountFields");
                }
            } else {
                List<IFieldDescribe> fields = fieldList.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
                if (fieldNodeExt.getObjectApiName().equals(masterApiName)) {
                    expressionCalculateLogicService.bulkCalculateWithDependentData(masterDescribe, Lists.newArrayList(masterDataMap.values()),
                            fields, dependentDataMap, false);
                    stopWatch.lap("bulkCalculateMasterFormula");
                } else {
                    String describeApiName = fields.get(0).getDescribeApiName();
                    IObjectDescribe calculateDescribe = describeMap.get(describeApiName);
                    if (StringUtils.equals(dataList.get(0).getDescribeApiName(), describeApiName)) {
                        expressionCalculateLogicService.bulkCalculateWithDependentData(calculateDescribe, dataList, fields,
                                dependentDataMap, false);
                        stopWatch.lap("bulkCalculateSelfFormula");
                    } else if (CollectionUtils.notEmpty(lookupDataMap) && lookupDataMap.containsKey(describeApiName)) {
                        List<IObjectData> toCalculateDataList = dependentDataMap.get(describeApiName);
                        expressionCalculateLogicService.bulkCalculateWithDependentData(calculateDescribe, toCalculateDataList,
                                fields, dependentDataMap, false);
                        stopWatch.lap("bulkCalculateLookupFormula");
                    }
                }
            }
        }

        //计算lookup默认值的__r字段
        lookupFieldGroups.forEach(fieldList -> {
            List<IFieldDescribe> lookupFields = fieldList.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
            metaDataMiscService.fillObjectDataWithRefObject(describe, dataList, user, dependentDataMap, true, lookupFields);
            stopWatch.lap("fillObjectDataWithRefObject");
        });

        //计算引用字段
        quoteFieldGroups.forEach(fieldList -> {
            List<Quote> quoteFields = fieldList.stream().map(x -> (Quote) x.getFieldDescribe()).collect(Collectors.toList());
            quoteValueService.fillQuoteFieldValue(user, dataList, describe, null, true, quoteFields,
                    null, false, FillQuoteFieldValueArg.builder().isFillMultiRegion(false).build());
            stopWatch.lap("fillQuoteFieldValue");
        });

        fillDimensionFieldValue(user, calculateFieldList, masterApiName, dataList);
        stopWatch.lap("fillDimensionFieldValue");

        stopWatch.logSlow(500);

    }

    private List<IObjectData> findObjectDataByIdsForCalculate(String tenantId, Collection<String> dataId, String describeApiName) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CALCULATE_IGNORE_ALL_AND_EXTRA_INFO, tenantId)) {
            return metaDataFindService.findObjectDataByIdsIgnoreAllAndExtraInfo(tenantId, Lists.newArrayList(dataId), describeApiName);
        }
        return metaDataFindService.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(dataId), describeApiName);
    }

    //按照构图排序计算
    @Override
    public void batchCalculateBySortFieldsWithDetailData(User user,
                                                         IObjectData masterData,
                                                         Map<String, List<CalculateObjectData>> detailCalculateDataMap,
                                                         Map<String, IObjectDescribe> describeMap,
                                                         Map<String, List<RelateField>> calculateFieldMap) {
        CalculateObjectData masterCalculateData = CalculateObjectData.of(masterData.getId(), masterData);
        batchCalculateBySortFieldsWithDetailData(user, masterCalculateData, detailCalculateDataMap, describeMap, calculateFieldMap, false);
    }

    @Override
    public void batchCalculateBySortFieldsWithDetailData(User user,
                                                         CalculateObjectData masterCalculateData,
                                                         Map<String, List<CalculateObjectData>> detailCalculateMap,
                                                         Map<String, IObjectDescribe> describeMap,
                                                         Map<String, List<RelateField>> calculateFieldMap,
                                                         boolean calculateSeparately) {
        if (CollectionUtils.empty(calculateFieldMap)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + ".batchCalculateBySortFieldsWithDetailData");
        IObjectData masterData = masterCalculateData.getObjectData();
        try {
            Map<String, List<CalculateObjectData>> detailCalculateDataMap = CollectionUtils.nullToEmpty(detailCalculateMap);
            //设置主对象标识
            if (CollectionUtils.notEmpty(detailCalculateDataMap)) {
                ObjectDataExt.of(masterData).setMasterDataFlag();
            }
            boolean isEditAction = ObjectDataExt.of(masterData).hasId();
            String masterApiName = masterData.getDescribeApiName();
            IObjectDescribe masterDescribe = describeMap.get(masterApiName);

            List<FieldNodeExt> calculateFieldList = Lists.newArrayList();
            calculateFieldMap.forEach((k, v) -> calculateFieldList.addAll(v.stream()
                    .filter(x -> describeMap.get(k).containsField(x.getFieldName())).map(x -> {
                        IFieldDescribe fieldDescribe = describeMap.get(k).getFieldDescribe(x.getFieldName());
                        return FieldNodeExt.of(k, fieldDescribe, x.getOrder(), x.isCalculateAllData());
                    }).collect(Collectors.toList())));

            List<FieldNodeExt> lookupFieldList = calculateFieldList.stream().filter(FieldNodeExt::isLookupField).collect(Collectors.toList());
            calculateFieldList.removeAll(lookupFieldList);
            List<List<FieldNodeExt>> fieldGroups = FieldNodeExt.sortAndClassifyNodeExtList(calculateFieldList);
            List<List<FieldNodeExt>> lookupFieldGroups = FieldNodeExt.sortAndClassifyNodeExtList(lookupFieldList);
            stopWatch.lap("sortAndClassifyNodeExtList");

            //先计算lookup字段的默认值，因为其他默认值或计算字段可能依赖了这些lookup字段
            for (List<FieldNodeExt> fieldNodes : lookupFieldGroups) {
                String objectApiName = fieldNodes.get(0).getObjectApiName();
                IObjectDescribe calculateDescribe = describeMap.get(objectApiName);
                List<IFieldDescribe> fields = fieldNodes.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
                if (objectApiName.equals(masterApiName)) {
                    expressionCalculateLogicService.bulkCalculate(masterDescribe, Lists.newArrayList(masterData), fields);
                    stopWatch.lap("bulkCalculateMasterLookupFields");
                } else if (detailCalculateDataMap.containsKey(objectApiName)) {
                    List<CalculateObjectData> detailCalculateDataList = detailCalculateDataMap.getOrDefault(objectApiName, Lists.newArrayList());
                    if (CollectionUtils.empty(detailCalculateDataList)) {
                        continue;
                    }
                    if (calculateSeparately) {
                        fieldNodes.forEach(formulaNode -> {
                            List<IObjectData> toCalculateDataList = CalculateObjectData.getMatchedCalculateDataList(detailCalculateDataList, formulaNode.getFieldNode());
                            expressionCalculateLogicService.bulkCalculateWithMasterData(calculateDescribe, toCalculateDataList,
                                    Lists.newArrayList(formulaNode.getFieldDescribe()), masterData);
                        });
                    } else {
                        Map<Boolean, List<FieldNodeExt>> groups = fieldNodes.stream().collect(Collectors.groupingBy(FieldNodeExt::isCalculateAllData));
                        groups.forEach((calculateAllData, group) -> {
                            List<IObjectData> toCalculateDataList = calculateAllData ? CalculateObjectData.toList(detailCalculateDataList) :
                                    CalculateObjectData.getModifiedDataList(detailCalculateDataList);
                            expressionCalculateLogicService.bulkCalculateWithMasterData(calculateDescribe, toCalculateDataList,
                                    fields, masterData);
                        });
                    }
                    stopWatch.lap("bulkCalculateDetailLookupFields");
                }
            }

            //查询lookup对象，用于计算lookup对象统计字段  （原有实时计算只包括主从对象）
            List<FieldNodeExt> lookupCountFields = calculateFieldList.stream()
                    .filter(FieldNodeExt::isCountField)
                    .filter(x -> !StringUtils.equals(x.getObjectApiName(), masterApiName))
                    .collect(Collectors.toList());

            Map<String, Set<String>> lookupDataIdMap = Maps.newHashMap();
            Map<FieldNodeExt, Map<String, List<IObjectData>>> relateDataMap = Maps.newHashMap();
            Map<String, List<IObjectData>> lookupDataMap = Maps.newHashMap();
            Map<String, List<IObjectData>> dependentDataMap = Maps.newHashMap();
            dependentDataMap.put(masterApiName, Lists.newArrayList(masterData));
            //编辑页面将带id的从对象数据放入dependentDataMap
            if (isEditAction && CollectionUtils.notEmpty(detailCalculateDataMap)) {
                detailCalculateDataMap.forEach((detailApiName, detailCalculateDataList) -> {
                    List<IObjectData> detailDataListWithId = CalculateObjectData.toList(detailCalculateDataList).stream()
                            .filter(x -> ObjectDataExt.of(x).hasId()).collect(Collectors.toList());
                    if (CollectionUtils.notEmpty(detailDataListWithId)) {
                        dependentDataMap.put(detailApiName, detailDataListWithId);
                    }
                });
            }
            Set<String> foundDbDataDetails = Sets.newHashSet();
            Map<String, List<IObjectData>> dbDetailDataMap = Maps.newHashMap();

            if (CollectionUtils.notEmpty(lookupCountFields)) {
                for (FieldNodeExt fieldNodeExt : lookupCountFields) {
                    IObjectDescribe objectDescribe = describeMap.get(fieldNodeExt.getObjectApiName());
                    Count count = (Count) objectDescribe.getFieldDescribe(fieldNodeExt.getFieldApiName());
                    String subObjectDescribeApiName = count.getSubObjectDescribeApiName();
                    IObjectDescribe subDescribe = describeMap.get(subObjectDescribeApiName);
                    if (subDescribe == null) {
                        log.warn("masterApiName:{} countApiName:{}.{} keys:{} describeMap not find .", masterApiName, fieldNodeExt.getObjectApiName(),
                                count.getApiName(), describeMap.keySet());
                        continue;
                    }
                    lookupDataIdMap.putIfAbsent(fieldNodeExt.getObjectApiName(), Sets.newHashSet());
                    if (StringUtils.equals(masterApiName, subObjectDescribeApiName)) {
                        Map<String, List<IObjectData>> relateMap = Maps.newHashMap();
                        relateDataMap.put(fieldNodeExt, relateMap);
                        ObjectDescribeExt.of(masterDescribe).getActiveSingleReferenceFieldDescribes().stream()
                                .filter(lookupField -> fieldNodeExt.getObjectApiName().equals(lookupField.getTargetApiName()))
                                .map(IFieldDescribe::getApiName)
                                .forEach(lookupFieldName -> {
                                    String lookupDataId = (String) masterData.get(lookupFieldName);
                                    if (!Strings.isNullOrEmpty(lookupDataId)) {
                                        lookupDataIdMap.get(fieldNodeExt.getObjectApiName()).add(lookupDataId);
                                    }

                                    if (lookupFieldName.equals(count.getFieldApiName())) {
                                        if (!Strings.isNullOrEmpty(lookupDataId)) {
                                            relateMap.put(lookupDataId, Lists.newArrayList(masterData));
                                        }

                                        String oldLookupDataId = (String) ObjectDataExt.of(masterData).getOldFieldValue(lookupFieldName);
                                        if (!Strings.isNullOrEmpty(oldLookupDataId)) {
                                            lookupDataIdMap.get(fieldNodeExt.getObjectApiName()).add(oldLookupDataId);
                                        }
                                    }
                                });
                        //从对象关联的数据的统计字段也要计算，防止从对象使用了这个统计字段
                        detailCalculateDataMap.forEach((detailApiName, detailCalculateDataList) -> {
                            List<IObjectData> matchedCalculateDataList = calculateSeparately ?
                                    CalculateObjectData.getMatchedRelateDataList(detailCalculateDataList, fieldNodeExt.getFieldNode()) :
                                    CalculateObjectData.toList(detailCalculateDataList);
                            if (CollectionUtils.empty(matchedCalculateDataList)) {
                                return;
                            }
                            IObjectDescribe detailDescribe = describeMap.get(detailApiName);
                            ObjectDescribeExt.of(detailDescribe).getActiveSingleReferenceFieldDescribes().stream()
                                    .filter(lookupField -> fieldNodeExt.getObjectApiName().equals(lookupField.getTargetApiName()))
                                    .forEach(lookupField -> {
                                        matchedCalculateDataList.forEach(detailData -> {
                                            String lookupDataId = (String) detailData.get(lookupField.getApiName());
                                            if (!Strings.isNullOrEmpty(lookupDataId)) {
                                                lookupDataIdMap.get(fieldNodeExt.getObjectApiName()).add(lookupDataId);
                                            }
                                        });
                                    });
                        });
                    } else if (detailCalculateDataMap.containsKey(subObjectDescribeApiName)) {
                        //主对象和其他从对象关联的数据的统计字段也要计算，防止主对象和其他从对象使用了这个统计字段
                        if (!calculateSeparately || masterCalculateData.matchRelateField(fieldNodeExt.getFieldNode())) {
                            ObjectDescribeExt.of(masterDescribe).getActiveSingleReferenceFieldDescribes().stream()
                                    .filter(lookupField -> fieldNodeExt.getObjectApiName().equals(lookupField.getTargetApiName()))
                                    .forEach(lookupField -> {
                                        String refLookupDataId = (String) masterData.get(lookupField.getApiName());
                                        if (!Strings.isNullOrEmpty(refLookupDataId)) {
                                            lookupDataIdMap.get(fieldNodeExt.getObjectApiName()).add(refLookupDataId);
                                        }
                                    });
                        }

                        Map<String, List<IObjectData>> relateMap = Maps.newHashMap();
                        CalculateObjectData.toList(detailCalculateDataMap.get(subObjectDescribeApiName)).forEach(detailData -> {
                            String lookupDataId = (String) detailData.get(count.getFieldApiName());
                            if (!Strings.isNullOrEmpty(lookupDataId)) {
                                relateMap.computeIfAbsent(lookupDataId, k -> Lists.newArrayList()).add(detailData);
                            }
                        });
                        relateDataMap.put(fieldNodeExt, relateMap);
                        detailCalculateDataMap.forEach((detailApiName, detailCalculateDataList) -> {
                            List<IObjectData> matchedCalculateDataList = calculateSeparately ?
                                    CalculateObjectData.getMatchedRelateDataList(detailCalculateDataList, fieldNodeExt.getFieldNode()) :
                                    CalculateObjectData.toList(detailCalculateDataList);
                            if (CollectionUtils.empty(matchedCalculateDataList)) {
                                return;
                            }
                            IObjectDescribe detailDescribe = describeMap.get(detailApiName);
                            ObjectDescribeExt.of(detailDescribe).getActiveSingleReferenceFieldDescribes().stream()
                                    .filter(lookupField -> fieldNodeExt.getObjectApiName().equals(lookupField.getTargetApiName()))
                                    .map(IFieldDescribe::getApiName)
                                    .forEach(lookupFieldName -> {
                                        matchedCalculateDataList.forEach(detailData -> {
                                            String lookupDataId = (String) detailData.get(lookupFieldName);
                                            if (!Strings.isNullOrEmpty(lookupDataId)) {
                                                lookupDataIdMap.get(fieldNodeExt.getObjectApiName()).add(lookupDataId);
                                            }
                                            if (detailApiName.equals(subObjectDescribeApiName) && lookupFieldName.equals(count.getFieldApiName())) {
                                                String oldLookupDataId = (String) ObjectDataExt.of(detailData).getOldFieldValue(lookupFieldName);
                                                if (!Strings.isNullOrEmpty(oldLookupDataId)) {
                                                    lookupDataIdMap.get(fieldNodeExt.getObjectApiName()).add(oldLookupDataId);
                                                }
                                            }
                                        });
                                    });
                        });

                        //查询从对象数据，计算lookup统计字段需要用
                        if (isEditAction && foundDbDataDetails.add(subObjectDescribeApiName)) {
                            IObjectDescribe detailDescribe = describeMap.get(subObjectDescribeApiName);
                            List<IObjectData> dbDetailData = metaDataFindService.findDetailObjectDataListIgnoreFormula(detailDescribe, masterData, user);
                            dbDetailDataMap.put(subObjectDescribeApiName, dbDetailData);
                            stopWatch.lap("findDbDetailData-" + subObjectDescribeApiName);
                        }
                    }
                }

                lookupDataIdMap.forEach((lookupObjectApiName, dataIdList) -> {
                    List<IObjectData> lookupDataList;
                    if (detailCalculateDataMap.containsKey(lookupObjectApiName)) {
                        //先从页面上的从对象数据里查找
                        lookupDataList = CalculateObjectData.getDataListByIds(detailCalculateDataMap.get(lookupObjectApiName), dataIdList);
                        List<String> needFindDBIds = Lists.newArrayList(dataIdList);
                        needFindDBIds.removeAll(ObjectDataExt.getDataId(lookupDataList));
                        //页面上没有的从对象数据，从数据库里查找
                        List<IObjectData> dbLookupDataList = findObjectDataByIdsForCalculate(masterDescribe.getTenantId(),
                                needFindDBIds, lookupObjectApiName);
                        lookupDataList.addAll(dbLookupDataList);
                    } else {
                        lookupDataList = findObjectDataByIdsForCalculate(masterDescribe.getTenantId(),
                                Lists.newArrayList(dataIdList), lookupObjectApiName);
                    }
                    if (CollectionUtils.notEmpty(lookupDataList)) {
                        lookupDataMap.put(lookupObjectApiName, lookupDataList);
                        //之前没缓存过的数据直接放入缓存
                        if (!dependentDataMap.containsKey(lookupObjectApiName)) {
                            dependentDataMap.put(lookupObjectApiName, Lists.newArrayList(lookupDataList));
                        } else {
                            //之前缓存过的数据，需要合并新查出来的数据
                            List<IObjectData> existsLookupDataList = dependentDataMap.get(lookupObjectApiName);
                            List<String> existsLookupDataIds = ObjectDataExt.getDataId(existsLookupDataList);
                            lookupDataList.forEach(x -> {
                                if (!existsLookupDataIds.contains(x.getId())) {
                                    existsLookupDataList.add(x);
                                }
                            });
                        }
                    }
                });
                stopWatch.lap("findLookupDataList");
            }

            //计算统计字段、计算字段、默认值
            for (List<FieldNodeExt> fieldList : fieldGroups) {
                FieldNodeExt fieldNodeExt = fieldList.get(0);
                String objectApiName = fieldNodeExt.getObjectApiName();
                IObjectDescribe calculateDescribe = describeMap.get(objectApiName);

                if (fieldNodeExt.isCountField()) {
                    List<Count> countList = fieldList.stream().map(x -> (Count) x.getFieldDescribe()).collect(Collectors.toList());
                    if (StringUtils.equals(masterApiName, objectApiName)) {
                        //原有计算统计字段逻辑
                        calculateCountFieldsInMemory(masterData, CalculateObjectData.toListMap(detailCalculateDataMap), describeMap, countList);
                        stopWatch.lap("calculateMasterCountFields");
                    } else if (lookupDataMap.containsKey(objectApiName)) {
                        List<IObjectData> toCalculateDataList = lookupDataMap.get(objectApiName);
                        for (FieldNodeExt countNode : fieldList) {
                            Map<String, List<IObjectData>> relateData = relateDataMap.getOrDefault(countNode, Collections.emptyMap());
                            Count count = (Count) countNode.getFieldDescribe();
                            if (masterApiName.equals(count.getSubObjectDescribeApiName())) {
                                for (IObjectData x : toCalculateDataList) {
                                    calculateCountFieldsWithDbDetailDataAndDbValue(x, relateData.get(x.getId()), describeMap.get(objectApiName),
                                            describeMap.get(count.getSubObjectDescribeApiName()), Lists.newArrayList(count), Lists.newArrayList(masterData));
                                }
                            } else if (foundDbDataDetails.contains(count.getSubObjectDescribeApiName())) {
                                toCalculateDataList.forEach(x -> calculateCountFieldsWithDbDetailDataAndDbValue(x, relateData.get(x.getId()), describeMap.get(objectApiName),
                                        describeMap.get(count.getSubObjectDescribeApiName()), Lists.newArrayList(count), dbDetailDataMap.get(count.getSubObjectDescribeApiName())));
                            } else {
                                toCalculateDataList.forEach(x -> calculateCountFieldsWithDetailDataAndDbValue(x, relateData.get(x.getId()), describeMap.get(objectApiName),
                                        describeMap.get(count.getSubObjectDescribeApiName()), Lists.newArrayList(count)));
                            }
                        }

                        stopWatch.lap("calculateLookupCountFields");
                    } else if (detailCalculateDataMap.containsKey(objectApiName)) {
                        //只处理新建的从对象，计数类型的统计字段设置为0，其他统计字段设置为null
                        List<IObjectData> detailDataList = CalculateObjectData.getModifiedDataList(detailCalculateDataMap.getOrDefault(objectApiName,
                                Collections.emptyList()));
                        countList.forEach(count -> detailDataList.forEach(data -> {
                            Object value = CountExt.of(count).formatResult(null);
                            data.set(count.getApiName(), value);
                        }));
                        stopWatch.lap("calculateDetailCountFields");
                    }

                } else if (fieldNodeExt.isQuoteField()) {
                    //计算引用字段
                    if (objectApiName.equals(masterApiName)) {
                        List<Quote> quoteFields = fieldList.stream().map(x -> (Quote) x.getFieldDescribe()).collect(Collectors.toList());
                        quoteValueService.fillQuoteFieldValue(user, Lists.newArrayList(masterData), masterDescribe,
                                null, true, quoteFields, null, false,
                                FillQuoteFieldValueArg.builder().isFillMultiRegion(false).build());

                        stopWatch.lap("fillMasterQuoteFieldValue");
                    } else if (detailCalculateDataMap.containsKey(objectApiName)) {
                        IObjectDescribe detailDescribe = describeMap.get(objectApiName);
                        List<CalculateObjectData> detailDataList = detailCalculateDataMap.get(objectApiName);
                        if (calculateSeparately) {
                            fieldList.forEach(quoteNode -> {
                                Quote quote = (Quote) quoteNode.getFieldDescribe();
                                List<IObjectData> toCalculateDataList = CalculateObjectData.getMatchedCalculateDataList(detailDataList, quoteNode.getFieldNode());
                                quoteValueService.fillQuoteFieldValue(user, toCalculateDataList, detailDescribe, null,
                                        true, Lists.newArrayList(quote), masterData, false,
                                        FillQuoteFieldValueArg.builder().isFillMultiRegion(false).build());
                            });
                        } else {
                            Map<Boolean, List<FieldNodeExt>> groups = fieldList.stream().collect(Collectors.groupingBy(FieldNodeExt::isCalculateAllData));
                            groups.forEach((calculateAllData, group) -> {
                                List<Quote> groupFields = group.stream().map(x -> (Quote) x.getFieldDescribe()).collect(Collectors.toList());
                                List<IObjectData> toCalculateDataList = calculateAllData ? CalculateObjectData.toList(detailDataList) :
                                        CalculateObjectData.getModifiedDataList(detailDataList);
                                quoteValueService.fillQuoteFieldValue(user, toCalculateDataList, detailDescribe, null,
                                        true, groupFields, masterData, false, FillQuoteFieldValueArg.builder().isFillMultiRegion(false).build());
                            });
                        }

                        stopWatch.lap("fillDetailQuoteFieldValue");
                    }

                } else {
                    List<IFieldDescribe> fields = fieldList.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
                    if (ObjectDescribeExt.of(masterDescribe).multiCurrencyFieldsDisabled()) {
                        fields.removeIf(field -> FieldDescribeExt.of(field).isMultiCurrencyFields());
                    }
                    if (objectApiName.equals(masterApiName)) {
                        expressionCalculateLogicService.bulkCalculateWithDependentData(masterDescribe, Lists.newArrayList(masterData), fields, dependentDataMap, false);
                        stopWatch.lap("bulkCalculateMasterFormula");
                    } else if (detailCalculateDataMap.containsKey(objectApiName)) {
                        List<CalculateObjectData> detailCalculateDataList = detailCalculateDataMap.getOrDefault(objectApiName, Lists.newArrayList());
                        if (CollectionUtils.empty(detailCalculateDataList)) {
                            continue;
                        }
                        if (calculateSeparately) {
                            fieldList.forEach(formulaNode -> {
                                List<IObjectData> toCalculateDataList = CalculateObjectData.getMatchedCalculateDataList(detailCalculateDataList, formulaNode.getFieldNode());
                                expressionCalculateLogicService.bulkCalculateWithDependentData(calculateDescribe, toCalculateDataList,
                                        Lists.newArrayList(formulaNode.getFieldDescribe()), dependentDataMap, false);
                            });
                        } else {
                            Map<Boolean, List<FieldNodeExt>> groups = fieldList.stream().collect(Collectors.groupingBy(FieldNodeExt::isCalculateAllData));
                            groups.forEach((calculateAllData, group) -> {
                                List<IObjectData> toCalculateDataList = calculateAllData ? CalculateObjectData.toList(detailCalculateDataList) :
                                        CalculateObjectData.getModifiedDataList(detailCalculateDataList);
                                expressionCalculateLogicService.bulkCalculateWithDependentData(calculateDescribe, toCalculateDataList,
                                        fields, dependentDataMap, false);
                            });
                        }
                        stopWatch.lap("bulkCalculateDetailFormula");
                    } else if (lookupDataMap.containsKey(objectApiName)) {
                        List<IObjectData> toCalculateDataList = lookupDataMap.get(objectApiName);
                        expressionCalculateLogicService.bulkCalculateWithDependentData(calculateDescribe, toCalculateDataList,
                                fields, dependentDataMap, false);
                        stopWatch.lap("bulkCalculateLookupFormula");
                    }
                }
            }

            //计算lookup默认值的__r字段
            lookupFieldGroups.forEach(fieldList -> {
                String objectApiName = fieldList.get(0).getObjectApiName();
                if (objectApiName.equals(masterApiName)) {
                    List<IFieldDescribe> lookupFields = fieldList.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
                    metaDataMiscService.fillObjectDataWithRefObject(masterDescribe, Lists.newArrayList(masterData), user,
                            null, true, lookupFields);
                    stopWatch.lap("fillMasterDataWithRefObject");
                } else if (detailCalculateDataMap.containsKey(objectApiName)) {
                    IObjectDescribe detailDescribe = describeMap.get(objectApiName);
                    List<CalculateObjectData> detailDataList = detailCalculateDataMap.get(objectApiName);
                    if (CollectionUtils.empty(detailDataList)) {
                        return;
                    }
                    if (calculateSeparately) {
                        fieldList.forEach(lookupNode -> {
                            List<IFieldDescribe> lookupFields = Lists.newArrayList(lookupNode.getFieldDescribe());
                            List<IObjectData> toCalculateDataList = CalculateObjectData.getMatchedCalculateDataList(detailDataList, lookupNode.getFieldNode());
                            metaDataMiscService.fillObjectDataWithRefObject(detailDescribe, toCalculateDataList, user,
                                    dependentDataMap, true, lookupFields);
                        });
                    } else {
                        Map<Boolean, List<FieldNodeExt>> groups = fieldList.stream().collect(Collectors.groupingBy(FieldNodeExt::isCalculateAllData));
                        groups.forEach((calculateAllData, group) -> {
                            List<IFieldDescribe> lookupFields = group.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
                            List<IObjectData> toCalculateDataList = calculateAllData ? CalculateObjectData.toList(detailDataList) :
                                    CalculateObjectData.getModifiedDataList(detailDataList);
                            metaDataMiscService.fillObjectDataWithRefObject(detailDescribe, toCalculateDataList, user,
                                    dependentDataMap, true, lookupFields);
                        });
                    }
                    stopWatch.lap("fillDetailDataWithRefObject");
                }
            });
            fillDimensionFieldValue(user, calculateFieldList, masterApiName, Lists.newArrayList(masterData));
        } finally {
            //清除主对象标识
            ObjectDataExt.of(masterData).clearMasterDataFlag();
            stopWatch.logSlow(500);
        }
    }

    private void fillDimensionFieldValue(User user, List<FieldNodeExt> calculateFieldList, String masterApiName, List<IObjectData> masterDataList) {
        List<IFieldDescribe> dimensionFields = calculateFieldList.stream()
                .filter(x -> FieldDescribeExt.of(x.getFieldDescribe()).isDimension())
                .filter(x -> Objects.equals(masterApiName, x.getObjectApiName()))
                .map(FieldNodeExt::getFieldDescribe)
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(dimensionFields)) {
            metaDataMiscService.fillDimensionFieldValue(user, dimensionFields, masterDataList);
        }
    }

    @Override
    public void batchCalculateBySortFields(User user,
                                           IObjectData masterData,
                                           Map<String, List<IObjectData>> detailDataMap,
                                           CalculateFields calculateFields) {
        if (CollectionUtils.notEmpty(calculateFields.getCalculateDataMap())) {
            CalculateObjectData masterCalculateData = CalculateObjectData.of(masterData.getId(), masterData,
                    null, calculateFields.getRelateFields(masterData.getDescribeApiName()));
            batchCalculateBySortFieldsWithDetailData(user, masterCalculateData, calculateFields.getCalculateDataMap(),
                    calculateFields.getDescribeMap(), calculateFields.getCalculateFieldMap(), true);
        } else if (CollectionUtils.notEmpty(detailDataMap)) {
            Map<String, List<CalculateObjectData>> calculateDetailDataMap = Maps.newHashMap();
            Map<String, List<RelateField>> calculateFieldMap = calculateFields.getCalculateFieldMap();
            detailDataMap.forEach((k, v) -> {
                calculateDetailDataMap.put(k, CalculateObjectData.ofList(v));
                calculateFieldMap.getOrDefault(k, Collections.emptyList()).forEach(x -> x.setCalculateAllData(true));
            });
            batchCalculateBySortFieldsWithDetailData(user, masterData, calculateDetailDataMap,
                    calculateFields.getDescribeMap(), calculateFields.getCalculateFieldMap());
        } else {
            batchCalculateBySortFields(user, Lists.newArrayList(masterData),
                    calculateFields.getDescribeMap(), calculateFields.getCalculateFieldMap());
        }
    }

    private Set<FieldNodeExt> needCalculate(FieldRelationGraph graph, IObjectDescribe objectDescribe, IObjectData objectData,
                                            Map<String, IObjectDescribe> detailDescribeMap, Map<String, List<IObjectData>> detailDataMap) {
        Set<FieldNodeExt> calculateFieldList = Sets.newHashSet();
        //清空统计字段
        ObjectDescribeExt.of(objectDescribe).getCountFields().forEach(count -> objectData.set(count.getApiName(), CountExt.of(count).formatResult(null)));
        detailDataMap.forEach((detailApiName, detailDataList) ->
                ObjectDescribeExt.of(detailDescribeMap.get(detailApiName)).getCountFields().forEach(count ->
                        detailDataList.forEach(detailData ->
                                detailData.set(count.getApiName(), CountExt.of(count).formatResult(null)))));

        //主对象统计字段、计算字段、引用字段、默认值
        //使用默认值的字段不支持复制才需要重新设置为默认值
        Set<FieldNodeExt> masterNodes = graph.nodes().stream()
                .filter(x -> StringUtils.equals(x.getObjectApiName(), objectDescribe.getApiName()))
                .filter(x -> x.isCalculateField() || x.isQuoteField())
                .filter(x -> Objects.nonNull(objectDescribe.getFieldDescribe(x.getFieldApiName())))
                .map(x -> FieldNodeExt.of(x, objectDescribe.getFieldDescribe(x.getFieldApiName())))
                .collect(Collectors.toSet());
        calculateFieldList.addAll(masterNodes);

        //从对象计算字段、引用字段、默认值
        if (CollectionUtils.notEmpty(detailDataMap)) {
            Set<FieldNodeExt> detailNodes = graph.nodes().stream()
                    .filter(x -> detailDescribeMap.containsKey(x.getObjectApiName()))
                    .filter(x -> x.isFormula() || x.isQuoteField() || x.isDefaultValue() || x.isCalculateValue())
                    .filter(x -> Objects.nonNull(detailDescribeMap.get(x.getObjectApiName()).getFieldDescribe(x.getFieldApiName())))
                    .map(x -> FieldNodeExt.of(x, detailDescribeMap.get(x.getObjectApiName()).getFieldDescribe(x.getFieldApiName())))
                    .collect(Collectors.toSet());
            calculateFieldList.addAll(detailNodes);
        }
        return calculateFieldList;
    }

    @Override
    public void calculateForClone(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap) {
        Set<FieldNodeExt> calculateFieldList = Sets.newHashSet();
        Map<String, IObjectDescribe> detailDescribeMap = detailDescribes.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        List<IObjectDescribe> describeListForGraph = Lists.newArrayList(objectDescribe);
        describeListForGraph.addAll(detailDescribes);
        FieldRelationGraph graph = fieldRelationGraphService.buildReverseDependencyGraph(describeListForGraph, false, true, true);

        if (FormulaGrayConfig.isInCloneWhiteList(user.getTenantId())) {

            calculateFieldList.addAll(needCalculate(graph, objectDescribe, objectData, detailDescribeMap, detailDataMap));

        } else if (CollectionUtils.notEmpty(AppFrameworkConfig.getFieldEnableCloneFieldTypes(user.getTenantId()))) {
            Set<FieldNodeExt> fieldNodes = needCalculate(graph, objectDescribe, objectData, detailDescribeMap, detailDataMap);
            List<FieldNodeExt> finalFieldNodes = fieldNodes.stream()
                    .filter(x -> !x.isDefaultValue() || (x.isDefaultValue() && !x.getFieldDescribe().getEnableClone()))
                    .collect(Collectors.toList());
            calculateFieldList.addAll(finalFieldNodes);
        } else {
            List<Count> masterCountFields = objectDescribe.getFieldDescribes().stream().map(FieldDescribeExt::of).filter(FieldDescribeExt::isCountField).map(x -> (Count) x.getFieldDescribe()).collect(Collectors.toList());
            List<String> masterCountApiNames = masterCountFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
            String masterApiName = objectDescribe.getApiName();
            masterCountApiNames.forEach(a -> {
                Optional<IFieldDescribe> field = objectDescribeExt.getFieldDescribeSilently(a);
                field.ifPresent(f -> objectData.set(a, null));
            });

            Set<FieldNode> masterNodes = Sets.newHashSet();
            if (CollectionUtils.notEmpty(detailDataMap)) {
                detailDescribes.forEach(x -> {
                    ObjectDescribeExt describeExt = ObjectDescribeExt.of(x);
                    List<FieldDescribeExt> fields = x.getFieldDescribes().stream().map(FieldDescribeExt::of)
                            .filter(FieldDescribeExt::isCountField)
                            .filter(y -> !detailDescribeMap.containsKey(((Count) (y.getFieldDescribe())).getSubObjectDescribeApiName()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.notEmpty(fields)) {
                        List<IObjectData> dataList = detailDataMap.getOrDefault(x.getApiName(), Collections.emptyList());
                        for (IObjectData data : dataList) {
                            ObjectDataExt dataExt = ObjectDataExt.of(data);
                            fields.forEach(a -> {
                                Optional<IFieldDescribe> field = describeExt.getFieldDescribeSilently(a.getApiName());
                                field.ifPresent(f -> dataExt.set(a.getApiName(), null));
                            });
                        }
                    }
                });

                Set<FieldNode> detailNodes = graph.nodes().stream()
                        .filter(x -> detailDescribeMap.containsKey(x.getObjectApiName()))
                        .filter(FieldNode::isFormula)
                        .collect(Collectors.toSet());
                List<String> cleanCountApiNames = Lists.newArrayList(masterCountApiNames);
                detailNodes.forEach(x -> {
                    Set<FieldNode> nodes = graph.reachableNodes(x).stream()
                            .filter(y -> StringUtils.equals(masterApiName, y.getObjectApiName()))
                            .filter(y -> y.isCountField() || y.isFormula())
                            .collect(Collectors.toSet());
                    nodes.stream().filter(FieldNode::isCountField)
                            .filter(y -> cleanCountApiNames.contains(y.getFieldApiName()))
                            .findFirst().ifPresent(y -> masterNodes.addAll(nodes));
                });
                List<FieldNodeExt> dNodes = detailNodes.stream()
                        .map(x -> FieldNodeExt.of(x, detailDescribeMap.get(x.getObjectApiName()).getFieldDescribe(x.getFieldApiName())))
                        .collect(Collectors.toList());
                calculateFieldList.addAll(dNodes);

                masterCountFields.stream()
                        .filter(x -> detailDescribeMap.containsKey(x.getSubObjectDescribeApiName()))
                        .forEach(x -> graph.getNode(masterApiName, x.getApiName()).ifPresent(masterNodes::add));
            }

            ObjectDescribeExt.of(objectDescribe).getFormulaFields()
                    .forEach(x -> {
                        Optional<FieldNode> fieldNodeOptional = graph.getNode(x.getDescribeApiName(), x.getApiName());
                        if (fieldNodeOptional.isPresent()) {
                            FieldNodeExt fieldNodeExt = FieldNodeExt.of(fieldNodeOptional.get(), x);
                            calculateFieldList.add(fieldNodeExt);
                        }
                    });
            List<FieldNodeExt> mNodes = masterNodes.stream()
                    .map(x -> FieldNodeExt.of(x, objectDescribe.getFieldDescribe(x.getFieldApiName())))
                    .collect(Collectors.toList());
            calculateFieldList.addAll(mNodes);
        }
        //多货币字段
        ObjectDescribeExt.of(objectDescribe).getMultiCurrencyCalculateFields()
                .forEach(x -> graph.getNode(objectDescribe.getApiName(), x.getApiName()).ifPresent(node ->
                        calculateFieldList.add(FieldNodeExt.of(node, x))));
        if (CollectionUtils.notEmpty(detailDataMap)) {
            detailDescribes.forEach(detailDescribe ->
                    ObjectDescribeExt.of(detailDescribe).getMultiCurrencyCalculateFields()
                            .forEach(x -> graph.getNode(detailDescribe.getApiName(), x.getApiName()).ifPresent(node ->
                                    calculateFieldList.add(FieldNodeExt.of(node, x)))));
        }

        doCalculateFields(user, objectDescribe, objectData, detailDataMap, detailDescribeMap, Lists.newArrayList(calculateFieldList));
    }

    private void doCalculateFields(User user, IObjectDescribe objectDescribe, IObjectData objectData, Map<String, List<IObjectData>> detailDataMap, Map<String, IObjectDescribe> detailDescribeMap, List<FieldNodeExt> calculateFieldList) {
        String masterApiName = objectDescribe.getApiName();
        List<FieldNodeExt> quoteFieldList = calculateFieldList.stream().filter(FieldNodeExt::isQuoteField).collect(Collectors.toList());
        calculateFieldList.removeAll(quoteFieldList);
        List<List<FieldNodeExt>> fieldGroups = FieldNodeExt.sortAndClassifyNodeExtList(calculateFieldList);
        List<List<FieldNodeExt>> quoteFieldGroups = FieldNodeExt.sortAndClassifyNodeExtList(quoteFieldList);
        //计算统计字段、计算字段、默认值
        Map<String, List<IObjectData>> dependentDataMap = Maps.newHashMap();
        dependentDataMap.put(masterApiName, Lists.newArrayList(objectData));
        fieldGroups.forEach(fieldList -> {
            FieldNodeExt fieldNodeExt = fieldList.get(0);
            if (fieldNodeExt.isCountField()) {
                List<Count> countList = fieldList.stream().map(x -> (Count) x.getFieldDescribe()).collect(Collectors.toList());
                calculateCountFieldsInMemory(objectData, detailDataMap, detailDescribeMap, countList);
            } else {
                List<IFieldDescribe> fields = fieldList.stream().map(FieldNodeExt::getFieldDescribe).collect(Collectors.toList());
                if (fieldNodeExt.getObjectApiName().equals(masterApiName)) {
                    expressionCalculateLogicService.bulkCalculateWithDependentData(objectDescribe, Lists.newArrayList(objectData), fields, dependentDataMap, false);
                } else {
                    IObjectDescribe detailDescribe = detailDescribeMap.get(fieldNodeExt.getObjectApiName());
                    expressionCalculateLogicService.bulkCalculateWithDependentData(detailDescribe, detailDataMap.get(fieldNodeExt.getObjectApiName()), fields, dependentDataMap, false);
                }
            }
        });
        //计算引用字段
        quoteFieldGroups.forEach(fieldList -> {
            String objectApiName = fieldList.get(0).getObjectApiName();
            if (objectApiName.equals(masterApiName)) {
                List<Quote> quoteFields = fieldList.stream().map(x -> (Quote) x.getFieldDescribe()).collect(Collectors.toList());
                quoteValueService.fillQuoteFieldValue(user, Lists.newArrayList(objectData), objectDescribe,
                        null, true, quoteFields, null);
            } else if (detailDataMap.containsKey(objectApiName)) {
                IObjectDescribe detailDescribe = detailDescribeMap.get(objectApiName);
                List<IObjectData> detailDataList = detailDataMap.get(objectApiName);

                List<Quote> groupFields = fieldList.stream().map(x -> (Quote) x.getFieldDescribe()).collect(Collectors.toList());
                quoteValueService.fillQuoteFieldValue(user, detailDataList, detailDescribe, null,
                        true, groupFields, objectData);
            }

        });
    }

    @Override
    public List<String> calculateForUIEvent(User user,
                                            IObjectData masterData,
                                            Map<String, List<IObjectData>> detailDataMap,
                                            List<String> masterChangedFields,
                                            Map<String, List<String>> detailChangedFieldMap,
                                            List<String> detailApiNameHasAddOrDeleteData) {
        String masterApiName = masterData.getDescribeApiName();
        IObjectDescribe masterDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), masterApiName);
        Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(detailDataMap)) {
            detailDescribeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), Lists.newArrayList(detailDataMap.keySet()));
        }

        List<IObjectDescribe> describeListForGraph = Lists.newArrayList(masterDescribe);
        describeListForGraph.addAll(detailDescribeMap.values());
        FieldRelationGraph graph = fieldRelationGraphService.buildReverseDependencyGraph(describeListForGraph,
                false, true, true);

        Set<RelateField> calculateFields = Sets.newHashSet();
        if (CollectionUtils.notEmpty(masterChangedFields)) {
            masterChangedFields.forEach(field ->
                    findCalculateFieldsWithField(calculateFields, graph, masterApiName, field, masterApiName));
        }
        if (CollectionUtils.notEmpty(detailChangedFieldMap)) {
            detailChangedFieldMap.forEach((detailApiName, fields) -> fields.forEach(field -> {
                findCalculateFieldsWithField(calculateFields, graph, detailApiName, field, masterApiName);
            }));
        }
        if (CollectionUtils.notEmpty(detailApiNameHasAddOrDeleteData)) {
            detailApiNameHasAddOrDeleteData.forEach(detailApiName ->
                    ObjectDescribeExt.of(masterDescribe).getCountFields(detailApiName).forEach(count -> {
                        calculateFields.add(graph.getNode(masterApiName, count.getApiName()).get().toRelateField());
                        findCalculateFieldsWithField(calculateFields, graph, masterApiName, count.getApiName(), masterApiName);
                    }));
        }

        if (CollectionUtils.empty(calculateFields)) {
            return Lists.newArrayList();
        }

        Map<String, List<RelateField>> calculateFieldMap = ImmutableMap.of(masterApiName, Lists.newArrayList(calculateFields));
        batchCalculateBySortFields(user, masterData, detailDataMap, CalculateFields.of(graph, calculateFieldMap));

        return calculateFields.stream().map(RelateField::getFieldName).collect(Collectors.toList());
    }

    @Override
    public void calculateForDrafts(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap) {
        calculateForDrafts(user, objectDescribe, objectData, detailDescribes, detailDataMap, null);
    }

    @Override
    public void calculateForDrafts(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes,
                                   Map<String, List<IObjectData>> detailDataMap, Map<String, List<String>> skipCalculateFields) {
        calculateForDrafts(user, objectDescribe, objectData, detailDescribes, detailDataMap, skipCalculateFields, null);
    }

    @Override
    public void calculateForDrafts(User user, IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes,
                                   Map<String, List<IObjectData>> detailDataMap, Map<String, List<String>> skipCalculateFields, Boolean skipCalculateDVField) {
        Map<String, IObjectDescribe> detailDescribeMap = CollectionUtils.nullToEmpty(detailDescribes).stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        if (!user.isSupperAdmin()) {
            filterInvalidOrDeleteFieldValue(user, objectDescribe, Lists.newArrayList(objectData));

            if (CollectionUtils.notEmpty(detailDataMap)) {
                Map<String, List<IRecordTypeOption>> recordTypeListMap = recordTypeLogicService.findValidRecordTypeListMap(Lists.newArrayList(detailDataMap.keySet()), user);
                Map<String, List<IRecordTypeOption>> validRecordTypeListMap = recordTypeLogicService.filterUnMatchRecordTypes(user.getTenantId(),
                        recordTypeListMap, objectDescribe.getApiName(), objectData.getRecordType());
                for (Map.Entry<String, List<IObjectData>> entry : detailDataMap.entrySet()) {
                    String detailApiName = entry.getKey();
                    List<IObjectData> objectDataList = entry.getValue();
                    filterInvalidOrDeleteFieldValue(user, detailDescribeMap.get(detailApiName), objectDataList);

                    List<String> validRecordType = validRecordTypeListMap.getOrDefault(detailApiName, Lists.newArrayList()).stream().map(IRecordTypeOption::getApiName).collect(Collectors.toList());
                    objectDataList.removeIf(x -> !validRecordType.contains(x.getRecordType()));
                    detailDataMap.put(detailApiName, objectDataList);
                }
            }
        }

        List<IObjectDescribe> describeListForGraph = Lists.newArrayList(objectDescribe);
        describeListForGraph.addAll(detailDescribeMap.values());
        FieldRelation fieldRelation = fieldRelationCalculateService.computeCalculateRelation(objectDescribe, detailDescribes);
        Map<String, Set<RelateField>> relateFieldMap = fieldRelation.getAllRelateFields(describeListForGraph);


        //过滤出带需要计算默认值的字段放入到skipCalculateFields，不再需要计算
        if (BooleanUtils.isTrue(skipCalculateDVField)) {
            describeListForGraph.forEach(oDescribe -> {
                String oApiName = oDescribe.getApiName();
                List<IFieldDescribe> fDescribes = ObjectDescribeExt.of(oDescribe).getDefaultValueFields();
                //包含本对象且计算默认值的字段List不为空
                if (relateFieldMap.containsKey(oApiName) && CollectionUtils.notEmpty(fDescribes)) {
                    List<String> collect = fDescribes.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
                    fDescribes.forEach(fDesc -> {
                        relateFieldMap.get(oApiName).removeIf(rf -> collect.contains(rf.getFieldName()));
                    });
                }
            });
        }

        //过滤不需要计算的字段
        if (CollectionUtils.notEmpty(skipCalculateFields)) {
            relateFieldMap.forEach((k, v) -> {
                if (skipCalculateFields.containsKey(k)) {
                    v.removeIf(x -> skipCalculateFields.getOrDefault(k, Collections.emptyList()).contains(x.getFieldName()));
                }
            });
        }

        CalculateFields calculateFields = CalculateFields.ofSet(fieldRelation.getGraph(), relateFieldMap);
        batchCalculateBySortFields(user, objectData, detailDataMap, calculateFields);

        ObjectDescribeExt.of(objectDescribe).getQuoteFieldDescribes().forEach(x -> {
            if (objectData.get(x.getApiName()) == null) {
                objectData.set(x.getApiName() + "__r", null);
            }
        });
        detailDescribeMap.forEach((detailApiName, detailDescribe) -> {
            ObjectDescribeExt detailDescribeExt = ObjectDescribeExt.of(detailDescribe);
            detailDescribeExt.filter(x -> FieldDescribeExt.of(x).isQuoteField() || FieldDescribeExt.of(x).isLookupField()).forEach(x -> {
                detailDataMap.getOrDefault(detailApiName, Collections.emptyList()).forEach(data -> {
                    if (objectData.get(x.getApiName()) == null) {
                        objectData.set(x.getApiName() + "__r", null);
                    }
                });
            });
        });
    }

    private void filterInvalidOrDeleteFieldValue(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        if (objectDescribe == null || CollectionUtils.empty(dataList)) {
            return;
        }
        objectDescribe.getFieldDescribes().stream()
                .filter(x -> StringUtils.equals(IFieldType.OBJECT_REFERENCE, x.getType()) || StringUtils.equals(IFieldType.OBJECT_REFERENCE_MANY, x.getType()))
                .map(ObjectReferenceWrapper::of)
                .filter(Objects::nonNull)
                .forEach(x -> {
                    String targetApiName = x.getTargetApiName();
                    String fieldApiName = x.getApiName();
                    removeMDOrLRFieldsValue(user, dataList, targetApiName, fieldApiName);
                });
        ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().ifPresent(x -> {
            String targetApiName = x.getTargetApiName();
            String fieldApiName = x.getApiName();
            removeMDOrLRFieldsValue(user, dataList, targetApiName, fieldApiName);
        });
    }

    private void removeMDOrLRFieldsValue(User user, List<IObjectData> dataList, String targetApiName, String fieldApiName) {
        List<String> dataIds = dataList.stream().map(data -> data.get(fieldApiName)).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());

        if (CollectionUtils.empty(dataIds)) {
            return;
        }

        List<String> dbDataIds = findDataIdsInDB(user, targetApiName, dataIds);
        dataIds.removeAll(dbDataIds);
        if (CollectionUtils.notEmpty(dataIds)) {
            dataList.stream().filter(data -> data.get(fieldApiName) != null)
                    .filter(data -> dataIds.contains(data.get(fieldApiName)))
                    .forEach(data -> {
                        data.set(fieldApiName, null);
                        data.set(fieldApiName + "__r", null);
                    });
        }
    }

    private List<String> findDataIdsInDB(User user, String targetApiName, List<String> dataIds) {
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), Lists.newArrayList(targetApiName));
        if (!describeMap.containsKey(targetApiName)) {
            return Collections.emptyList();
        }

        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setLimit(dataIds.size());
        queryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, targetApiName);
        queryExt.addFilter(Operator.IN, IObjectData.ID, dataIds);
        queryExt.addFilter(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue()));
        queryExt.setNeedReturnCountNum(false);
        queryExt.setNeedReturnQuote(false);
        List<IObjectData> dbDataList = metaDataFindService.findBySearchQuery(user, targetApiName, (SearchTemplateQuery) queryExt.getQuery()).getData();

        return dbDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private void findCalculateFieldsWithField(Set<RelateField> calculateFields, FieldRelationGraph graph,
                                              String objectApiName, String fieldName, String calculateObjectApiName) {
        graph.getNode(objectApiName, fieldName).ifPresent(node -> {
            Set<RelateField> relateFields = graph.reachableNodes(node).stream()
                    .filter(x -> x.getObjectApiName().equals(calculateObjectApiName))
                    .map(FieldNode::toRelateField)
                    .collect(Collectors.toSet());
            calculateFields.addAll(relateFields);
        });
    }

    @Override
    public IObjectData calculateForSnapshot(User user, IObjectDescribe describe, IObjectData dbData, ObjectDataSnapshot snapshot) {
        IObjectData snapshotData = ObjectDataExt.of(snapshot.getMasterSnapshot()).copy();
        Map<String, Map<String, Object>> detailSnapshot = snapshot.getDetailSnapshot();

        Set<String> detailObjectApiNames = CollectionUtils.nullToEmpty(detailSnapshot).keySet();
        Map<String, IObjectDescribe> detailDescribes = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), detailObjectApiNames);

        CalculateFields calculateFields = fieldRelationCalculateService.getCalculateFieldsByChanges(describe, Lists.newArrayList(detailDescribes.values()),
                snapshot.getMasterSnapshot(), detailSnapshot);
        //过滤掉从对象中没有被主对象使用的计算字段
        calculateFields.getCalculateFieldMap().forEach((objectApiName, relateFields) -> {
            if (objectApiName.equals(describe.getApiName())) {
                return;
            }
            relateFields.removeIf(relateField -> {
                FieldNode node = calculateFields.getGraph().getNode(objectApiName, relateField.getFieldName()).get();
                return calculateFields.getGraph().reachableNodes(node).stream().noneMatch(x -> describe.getApiName().equals(x.getObjectApiName()));
            });
        });
        detailObjectApiNames.forEach(x -> {
            if (CollectionUtils.empty(calculateFields.getCalculateFieldMap().get(x))) {
                calculateFields.getCalculateFieldMap().remove(x);
            }
        });
        if (CollectionUtils.empty(calculateFields.getCalculateFieldMap())) {
            return snapshotData;
        }

        Map<String, List<IObjectData>> detailDataMap = metaDataFindService.findDetailObjectDataList(Lists.newArrayList(detailDescribes.values()),
                dbData, user);
        detailDataMap.forEach((k, v) -> {
            Map<String, Object> changes = detailSnapshot.getOrDefault(k, Collections.emptyMap());
            List<Map<String, Object>> addList = (List<Map<String, Object>>) changes.getOrDefault(ObjectAction.CREATE.getActionCode(),
                    Collections.emptyList());
            Map<String, Map<String, Object>> updateList = (Map<String, Map<String, Object>>) changes.getOrDefault(ObjectAction.UPDATE.getActionCode(),
                    Collections.emptyMap());
            List<String> invalidIds = (List<String>) changes.getOrDefault(ObjectAction.INVALID.getActionCode(), Collections.emptyList());
            List<String> deleteIds = (List<String>) changes.getOrDefault(ObjectAction.DELETE.getActionCode(), Collections.emptyList());
            v.removeIf(x -> invalidIds.contains(x.getId()) || deleteIds.contains(x.getId()));
            v.stream().filter(d -> updateList.containsKey(d.getId())).forEach(d -> ObjectDataExt.of(d).putAll(updateList.get(d.getId())));
            v.addAll(addList.stream().map(x -> ObjectDataExt.of(x).getObjectData()).collect(Collectors.toList()));
        });
        ObjectDataExt.of(snapshotData).merge(dbData);
        batchCalculateBySortFields(user, snapshotData, detailDataMap, calculateFields);

        return snapshotData;
    }

    @Override
    public IObjectData calculateDefaultValueWithConstant(User user, IObjectData objectData, IObjectDescribe objectDescribe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        List<IFieldDescribe> fieldNeedToCalc = describeExt.getNeedCalculateConstantDefaultValue();
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<IFieldDescribe> toCalculateFields = fieldNeedToCalc.stream()
                .filter(field -> !objectDataExt.containsField(field.getApiName()))
                .collect(Collectors.toList());

        toCalculateFields.forEach(field -> objectDataExt.set(field.getApiName(), field.getDefaultValue()));

        // 数据中已经包含了负责人，不需能覆盖
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId())) {
            fillOutOwner(user, objectData, objectDescribe);
        }
        handleDataOwnDepartment(user, objectData, objectDescribe);
        return objectData;
    }

    @Override
    public void calculateForBatchCreate(User user, List<IObjectData> dataList, IObjectDescribe describe, boolean excludeDefaultValue) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        //补充常量默认值
        fillConstantDefaultValue(describe, dataList);
        //格式化统计字段
        formatCountValue(describe, dataList, null);
        //计算默认值和计算字段
        CalculateFields calculateFields = fieldRelationCalculateService.getCalculateFieldsForBatchCreate(describe, excludeDefaultValue);
        if (CollectionUtils.empty(calculateFields.getCalculateFieldMap())) {
            return;
        }
        Map<CalculateFields, List<IObjectData>> calculateDataGroups = Maps.newHashMap();
        dataList.forEach(data -> {
            CalculateFields calculateFieldsForData = calculateFields.copy();
            //过滤数据中已经赋值的默认值字段
            calculateFieldsForData.getCalculateFieldMap().forEach((objectApiName, relateFields) -> {
                relateFields.removeIf(relateField -> relateField.typeIsDefaultValue() && data.containsField(relateField.getFieldName()));
            });
            calculateDataGroups.computeIfAbsent(calculateFieldsForData, k -> Lists.newArrayList()).add(data);
        });
        calculateDataGroups.forEach((calculateFieldsForData, dataGroup) -> batchCalculateBySortFields(user, dataGroup,
                calculateFieldsForData.getDescribeMap(), calculateFieldsForData.getCalculateFieldMap()));
    }

    @Override
    public void calculateForAddAction(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                                      Map<String, IObjectDescribe> describeMap, boolean excludeDefaultValue, boolean excludeLookupRelateField) {
        Map<String, List<IObjectData>> detailObjectData = CollectionUtils.nullToEmpty(detailDataMap);
        IObjectDescribe masterDescribe = describeMap.get(masterData.getDescribeApiName());
        List<IObjectDescribe> detailDescribes = describeMap.values().stream()
                .filter(x -> detailObjectData.containsKey(x.getApiName()))
                .collect(Collectors.toList());
        if (excludeDefaultValue) {
            CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFieldsForAddAction(masterDescribe,
                    detailDescribes, true);
            batchCalculateBySortFields(user, masterData, detailObjectData, calculateFields);
            return;
        }

        //补充常量默认值
        fillConstantDefaultValue(masterDescribe, Lists.newArrayList(masterData));
        //格式化统计字段
        formatCountValue(masterDescribe, Lists.newArrayList(masterData), detailObjectData.keySet());

        detailObjectData.forEach((detailApiName, detailDataList) -> {
            IObjectDescribe detailDescribe = describeMap.get(detailApiName);
            //补充常量默认值
            fillConstantDefaultValue(detailDescribe, detailDataList);
            //格式化统计字段
            formatCountValue(detailDescribe, detailDataList, null);
        });

        //计算默认值和计算字段、统计字段
        CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFieldsForAddAction(masterDescribe,
                detailDescribes, false, true, excludeLookupRelateField);
        if (CollectionUtils.empty(calculateFields.getCalculateFieldMap())) {
            return;
        }
        //过滤数据中已经赋值的默认值字段
        List<RelateField> masterRelateFields = calculateFields.getCalculateFieldMap().get(masterDescribe.getApiName());
        if (CollectionUtils.notEmpty(masterRelateFields)) {
            masterRelateFields.removeIf(x -> x.typeIsDefaultValue() && masterData.containsField(x.getFieldName()));
            if (CollectionUtils.empty(masterRelateFields)) {
                calculateFields.getCalculateFieldMap().remove(masterDescribe.getApiName());
            }
        }
        Map<String, List<CalculateObjectData>> detailCalculateDataMap = Maps.newHashMap();
        detailObjectData.forEach((detailApiName, detailDataList) -> {
            List<CalculateObjectData> detailCalculateDataList = Lists.newArrayList();
            detailDataList.forEach(detailData -> {
                //过滤数据中已经赋值的默认值字段
                CalculateFields cpCalculateFields = calculateFields.copy();
                //本数据需要计算的字段
                List<RelateField> detailCalculateFields = cpCalculateFields.getCalculateFieldMap().getOrDefault(detailApiName, Lists.newArrayList());
                detailCalculateFields.removeIf(x -> x.typeIsDefaultValue() && detailData.containsField(x.getFieldName()));
                //从本数据出发找到的需要计算的字段
                Map<String, Set<RelateField>> detailRelateFields = cpCalculateFields.getRelateFields(detailApiName);
                CalculateObjectData detailCalculateData = CalculateObjectData.of(ObjectDataExt.of(detailData).getTemporaryId(),
                        detailData, Sets.newHashSet(detailCalculateFields), detailRelateFields);
                detailCalculateDataList.add(detailCalculateData);
            });
            detailCalculateDataMap.put(detailApiName, detailCalculateDataList);
        });
        calculateFields.setCalculateDataMap(detailCalculateDataMap);
        batchCalculateBySortFields(user, masterData, detailDataMap, calculateFields);
    }

    private void fillConstantDefaultValue(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        ObjectDescribeExt.of(describe).getNeedCalculateConstantDefaultValue().forEach(field -> {
            dataList.forEach(data -> {
                if (!data.containsField(field.getApiName())) {
                    data.set(field.getApiName(), field.getDefaultValue());
                }
            });
        });
    }

    private void formatCountValue(IObjectDescribe describe, List<IObjectData> dataList, Set<String> filterSubApiNames) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        ObjectDescribeExt.of(describe).getCountFields().forEach(count -> {
            if (CollectionUtils.notEmpty(filterSubApiNames) && filterSubApiNames.contains(count.getSubObjectDescribeApiName())) {
                return;
            }
            Object countValue = CountExt.of(count).formatResult(null);
            dataList.forEach(data -> data.set(count.getApiName(), countValue));
        });
    }

    @Override
    public void calculateForEditData(User user, EditCalculateParam editCalculateParam) {
        // 不能更新计算和统计字段，灰度对象（流程的对象）可以更新
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ALLOW_UPDATE_FORMULA_GRAY_OBJECTS, editCalculateParam.getMasterDescribe().getApiName())) {
            return;
        }
        Map<String, IObjectDescribe> detailDescribeMap = editCalculateParam.getDetailDescribeMap();
        if (editCalculateParam.isFillNewDataBeforeCalculate() && CollectionUtils.notEmpty(editCalculateParam.getDetailAddDataMap())) {
            editCalculateParam.getDetailAddDataMap().forEach((detailApiName, addDataList) -> {
                IObjectDescribe detailDescribe = detailDescribeMap.get(detailApiName);
                //补充常量默认值
                fillConstantDefaultValue(detailDescribe, addDataList);
                //格式化统计字段
                formatCountValue(detailDescribe, addDataList, null);
            });
        }
        //计算字段依赖关系
        CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFieldsForEditData(editCalculateParam);
        //计算默认值和计算字段、统计字段
        batchCalculateBySortFields(user, editCalculateParam.getMasterData(), editCalculateParam.getDetailDataMap(), calculateFields);
    }

    @Override
    public Map<String, Map<String, Object>> calculateForBatchEditData(User user, List<IObjectData> objectDataList, List<IObjectData> dbObjectDataList, IObjectDescribe objectDescribe) {
        if (!UdobjGrayConfig.isAllow(CALCULATE_FOR_UPDATE, user.getTenantId())) {
            return Maps.newHashMap();
        }
        // 不能更新计算和统计字段，灰度对象（流程的对象）可以更新
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ALLOW_UPDATE_FORMULA_GRAY_OBJECTS, objectDescribe.getApiName())) {
            return Maps.newHashMap();
        }
        if (CollectionUtils.empty(objectDataList)) {
            return Maps.newHashMap();
        }
        if (CollectionUtils.empty(dbObjectDataList)) {
            dbObjectDataList = metaDataFindService.findObjectDataByIds(user.getTenantId(), objectDataList.stream().map(DBRecord::getId).collect(Collectors.toList()), objectDescribe.getApiName());
        }
        if (CollectionUtils.empty(dbObjectDataList)) {
            return Maps.newHashMap();
        }
        BatchEditCalculateParam batchParam = buildBatchEditCalculateParam(objectDataList, dbObjectDataList, objectDescribe);
        return calculateForBatchEditData(user, batchParam);
    }

    private Map<String, Map<String, Object>> calculateForBatchEditData(User user, BatchEditCalculateParam batchEditCalculateParam) {
        if (CollectionUtils.empty(batchEditCalculateParam.getMasterModifyData())) {
            return Maps.newHashMap();
        }
        List<IObjectData> copyObjectDataList = batchEditCalculateParam.getCopyObjectDataList();
        List<IObjectData> objectDataList = batchEditCalculateParam.getObjectDataList();
        Map<String, IObjectData> objectDataMap = objectDataList.stream().collect(Collectors.toMap(DBRecord::getId, it -> it, (x1, x2) -> x1));

        //计算字段依赖关系
        CalculateFields calculateFields = fieldRelationCalculateService.computeCalculateFieldsForBatchEditData(batchEditCalculateParam);
        if (CollectionUtils.empty(calculateFields.getCalculateFieldMap())) {
            return Maps.newHashMap();
        }

        batchCalculateBySortFields(user, copyObjectDataList, calculateFields.getDescribeMap(), calculateFields.getCalculateFieldMap());
        Map<String, Map<String, Object>> result = Maps.newHashMap();

        List<String> calculateFieldApiNames = calculateFields.getCalculateFieldMap().get(batchEditCalculateParam.getObjectDescribe().getApiName())
                .stream().map(RelateField::getFieldName).collect(Collectors.toList());

        for (IObjectData copyObjectData : copyObjectDataList) {
            IObjectData objectData = objectDataMap.get(copyObjectData.getId());
            Map<String, Object> updateMap = ObjectDataExt.of(copyObjectData).toMap(calculateFieldApiNames);
            if (CollectionUtils.notEmpty(updateMap)) {
                ObjectDataExt.of(objectData).putAll(updateMap);
                result.put(copyObjectData.getId(), updateMap);
            }
        }
        return result;
    }


    private BatchEditCalculateParam buildBatchEditCalculateParam(List<IObjectData> objectDataList, List<IObjectData> oldMasterDataList, IObjectDescribe objectDescribe) {
        return BatchEditCalculateParam.builder()
                .objectDataList(objectDataList)
                .dbObjectDataList(oldMasterDataList)
                .objectDescribe(objectDescribe)
                .excludeDefaultValue(true)
                .includeQuoteField(false)
                .build()
                .init();
    }

}

