package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectImportConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ObjectImportInitManagerTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DefaultObjectImportProvider defaultObjectImportProvider;

    @Mock
    private ImportObjectProviderProxy proxy;

    @Mock
    private ObjectImportInitProvider testProvider1;

    @Mock
    private ObjectImportInitProvider testProvider2;

    @Mock
    private ObjectImportInitProvider emptyCodeProvider;

    @Mock
    private RequestContext requestContext;

    @InjectMocks
    private ObjectImportInitManager objectImportInitManager;

    private String testTenantId;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        when(requestContext.getTenantId()).thenReturn(testTenantId);

        // 配置Mock行为
        when(testProvider1.getObjectCode()).thenReturn("TEST_API_1");
        when(testProvider2.getObjectCode()).thenReturn("TEST_API_2");
        when(emptyCodeProvider.getObjectCode()).thenReturn("");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext方法正确初始化providerMap
     */
    @Test
    @DisplayName("setApplicationContext - 正确初始化providerMap")
    void testSetApplicationContext_正确初始化providerMap() {
        // 准备测试数据
        Map<String, ObjectImportInitProvider> beanMap = new HashMap<>();
        beanMap.put("testProvider1", testProvider1);
        beanMap.put("testProvider2", testProvider2);
        beanMap.put("emptyCodeProvider", emptyCodeProvider);

        // 配置Mock行为
        when(applicationContext.getBeansOfType(ObjectImportInitProvider.class)).thenReturn(beanMap);

        // 执行被测试方法
        objectImportInitManager.setApplicationContext(applicationContext);

        // 验证结果 - 通过getLocalProvider验证providerMap是否正确初始化
        ObjectImportInitProvider provider1 = objectImportInitManager.getLocalProvider("TEST_API_1");
        ObjectImportInitProvider provider2 = objectImportInitManager.getLocalProvider("TEST_API_2");

        assertEquals(testProvider1, provider1);
        assertEquals(testProvider2, provider2);

        // 验证Mock交互
        verify(applicationContext).getBeansOfType(ObjectImportInitProvider.class);
        verify(testProvider1).getObjectCode();
        verify(testProvider2).getObjectCode();
        verify(emptyCodeProvider).getObjectCode();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalProvider方法当存在provider时返回对应provider
     */
    @Test
    @DisplayName("getLocalProvider - 存在provider时返回对应provider")
    void testGetLocalProvider_存在provider时返回对应provider() {
        // 准备测试数据 - 先初始化providerMap
        Map<String, ObjectImportInitProvider> beanMap = new HashMap<>();
        beanMap.put("testProvider1", testProvider1);
        when(applicationContext.getBeansOfType(ObjectImportInitProvider.class)).thenReturn(beanMap);
        objectImportInitManager.setApplicationContext(applicationContext);

        // 执行被测试方法
        ObjectImportInitProvider result = objectImportInitManager.getLocalProvider("TEST_API_1");

        // 验证结果
        assertEquals(testProvider1, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalProvider方法当不存在provider时返回默认provider
     */
    @Test
    @DisplayName("getLocalProvider - 不存在provider时返回默认provider")
    void testGetLocalProvider_不存在provider时返回默认provider() {
        // 执行被测试方法
        ObjectImportInitProvider result = objectImportInitManager.getLocalProvider("UNKNOWN_API");

        // 验证结果
        assertEquals(defaultObjectImportProvider, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当是自定义对象时返回默认provider
     */
    @Test
    @DisplayName("getProvider - 自定义对象时返回默认provider")
    void testGetProvider_自定义对象时返回默认provider() {
        try (MockedStatic<ObjectImportEnum> mockedObjectImportEnum = mockStatic(ObjectImportEnum.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {

            // 配置Mock行为
            mockedObjectImportEnum.when(() -> ObjectImportEnum.getApiNameByObjectCode("CUSTOM_OBJ"))
                    .thenReturn("CustomObj");
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("CustomObj"))
                    .thenReturn(true);

            // 执行被测试方法
            ObjectImportInitProvider result = objectImportInitManager.getProvider("CUSTOM_OBJ");

            // 验证结果
            assertEquals(defaultObjectImportProvider, result);

            // 验证Mock交互
            mockedObjectImportEnum.verify(() -> ObjectImportEnum.getApiNameByObjectCode("CUSTOM_OBJ"));
            mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.isCustomObject("CustomObj"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当RequestContext为null时抛出异常
     */
    @Test
    @DisplayName("getProvider - RequestContext为null时抛出异常")
    void testGetProviderThrowsException_RequestContext为null时抛出异常() {
        try (MockedStatic<ObjectImportEnum> mockedObjectImportEnum = mockStatic(ObjectImportEnum.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectImportConfig> mockedObjectImportConfig = mockStatic(ObjectImportConfig.class);
             MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {

            // 配置Mock行为
            mockedObjectImportEnum.when(() -> ObjectImportEnum.getApiNameByObjectCode("TEST_OBJ"))
                    .thenReturn("TestObj");
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("TestObj"))
                    .thenReturn(false);
            mockedObjectImportConfig.when(() -> ObjectImportConfig.isGrayLocationImportProvider("TestObj"))
                    .thenReturn(false);
            mockedRequestContextManager.when(RequestContextManager::getContext)
                    .thenReturn(null);

            // 执行并验证异常
            Exception exception = assertThrows(ValidateException.class, () -> {
                objectImportInitManager.getProvider("TEST_OBJ");
            });

            // 验证异常信息
            assertTrue(exception.getMessage().contains("tenantId is empty!"));

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当是灰度位置导入provider且存在本地provider时返回本地provider
     */
    @Test
    @DisplayName("getProvider - 灰度位置导入provider且存在本地provider时返回本地provider")
    void testGetProvider_灰度位置导入provider且存在本地provider时返回本地provider() {
        try (MockedStatic<ObjectImportEnum> mockedObjectImportEnum = mockStatic(ObjectImportEnum.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectImportConfig> mockedObjectImportConfig = mockStatic(ObjectImportConfig.class)) {

            // 准备测试数据 - 先初始化providerMap
            Map<String, ObjectImportInitProvider> beanMap = new HashMap<>();
            beanMap.put("testProvider1", testProvider1);
            when(applicationContext.getBeansOfType(ObjectImportInitProvider.class)).thenReturn(beanMap);
            objectImportInitManager.setApplicationContext(applicationContext);

            // 配置Mock行为
            mockedObjectImportEnum.when(() -> ObjectImportEnum.getApiNameByObjectCode("TEST_OBJ"))
                    .thenReturn("TEST_API_1");
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("TEST_API_1"))
                    .thenReturn(false);
            mockedObjectImportConfig.when(() -> ObjectImportConfig.isGrayLocationImportProvider("TEST_API_1"))
                    .thenReturn(true);

            // 执行被测试方法
            ObjectImportInitProvider result = objectImportInitManager.getProvider("TEST_OBJ");

            // 验证结果
            assertEquals(testProvider1, result);

            // 验证Mock交互
            mockedObjectImportEnum.verify(() -> ObjectImportEnum.getApiNameByObjectCode("TEST_OBJ"));
            mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.isCustomObject("TEST_API_1"));
            mockedObjectImportConfig.verify(() -> ObjectImportConfig.isGrayLocationImportProvider("TEST_API_1"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当是灰度位置导入provider但不存在本地provider时返回远程provider
     */
    @Test
    @DisplayName("getProvider - 灰度位置导入provider但不存在本地provider时返回远程provider")
    void testGetProvider_灰度位置导入provider但不存在本地provider时返回远程provider() {
        try (MockedStatic<ObjectImportEnum> mockedObjectImportEnum = mockStatic(ObjectImportEnum.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectImportConfig> mockedObjectImportConfig = mockStatic(ObjectImportConfig.class)) {

            // 配置Mock行为
            mockedObjectImportEnum.when(() -> ObjectImportEnum.getApiNameByObjectCode("TEST_OBJ"))
                    .thenReturn("UNKNOWN_API");
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("UNKNOWN_API"))
                    .thenReturn(false);
            mockedObjectImportConfig.when(() -> ObjectImportConfig.isGrayLocationImportProvider("UNKNOWN_API"))
                    .thenReturn(true);

            // 执行被测试方法
            ObjectImportInitProvider result = objectImportInitManager.getProvider("TEST_OBJ");

            // 验证结果
            assertNotNull(result);
            assertTrue(result instanceof RemoteObjectImportInitProvider);

            // 验证Mock交互
            mockedObjectImportEnum.verify(() -> ObjectImportEnum.getApiNameByObjectCode("TEST_OBJ"));
            mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.isCustomObject("UNKNOWN_API"));
            mockedObjectImportConfig.verify(() -> ObjectImportConfig.isGrayLocationImportProvider("UNKNOWN_API"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当模块为UDOBJ时返回远程provider
     */
    @Test
    @DisplayName("getProvider - 模块为UDOBJ时返回远程provider")
    void testGetProvider_模块为UDOBJ时返回远程provider() {
        try (MockedStatic<ObjectImportEnum> mockedObjectImportEnum = mockStatic(ObjectImportEnum.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectImportConfig> mockedObjectImportConfig = mockStatic(ObjectImportConfig.class);
             MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {

            // 配置Mock行为
            mockedObjectImportEnum.when(() -> ObjectImportEnum.getApiNameByObjectCode("TEST_OBJ"))
                    .thenReturn("TestObj");
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.isCustomObject("TestObj"))
                    .thenReturn(false);
            mockedObjectImportConfig.when(() -> ObjectImportConfig.isGrayLocationImportProvider("TestObj"))
                    .thenReturn(false);
            mockedRequestContextManager.when(RequestContextManager::getContext)
                    .thenReturn(requestContext);
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.getImportModuleNameByObjectApiName(testTenantId, "TestObj"))
                    .thenReturn(DefObjConstants.UDOBJ);

            // 执行被测试方法
            ObjectImportInitProvider result = objectImportInitManager.getProvider("TEST_OBJ");

            // 验证结果
            assertNotNull(result);
            assertTrue(result instanceof RemoteObjectImportInitProvider);

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
            mockedAppFrameworkConfig.verify(() -> AppFrameworkConfig.getImportModuleNameByObjectApiName(testTenantId, "TestObj"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当apiName为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - apiName为null时的处理")
    void test各方法_apiName为null时的处理() {
        // 测试getLocalProvider
        ObjectImportInitProvider localProvider = objectImportInitManager.getLocalProvider(null);
        assertEquals(defaultObjectImportProvider, localProvider);
    }
}
