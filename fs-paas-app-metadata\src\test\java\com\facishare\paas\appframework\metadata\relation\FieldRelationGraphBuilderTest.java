package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphBuilder.GraphLayer;
import com.facishare.paas.appframework.common.graph.MutableValueGraph;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FieldRelationGraphBuilderTest {

    @Mock(lenient = true)
    private DescribeLogicService describeLogicService;

    @Mock(lenient = true)
    private FieldRelationGraph existingGraph;

    private List<GraphLayer> graphLayers;
    private IObjectDescribe objectDescribe;

    @BeforeEach
    void setUp() {
        objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("TestObject");
        objectDescribe.setTenantId("74255");

        GraphLayer layer = GraphLayer.of(Lists.newArrayList(objectDescribe));
        graphLayers = Lists.newArrayList(layer);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldRelationGraphBuilder.builder()方法创建构建器
     */
    @Test
    @DisplayName("创建构建器 - 使用builder方法")
    void testBuilder_创建构建器() {
        // 执行被测试方法
        FieldRelationGraphBuilder.FieldRelationGraphBuilderBuilder builder = FieldRelationGraphBuilder.builder();

        // 验证结果
        assertNotNull(builder);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用Builder模式构建FieldRelationGraphBuilder的正常场景
     */
    @Test
    @DisplayName("构建器模式 - 正常场景")
    void testBuilder_正常场景() {
        // 配置existingGraph Mock
        MutableValueGraph<FieldNode, RelateEdge> mockGraph = mock(MutableValueGraph.class);
        when(existingGraph.getGraph()).thenReturn(mockGraph);
        when(existingGraph.getDescribeMap()).thenReturn(Maps.newHashMap());

        // 执行被测试方法
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(true)
                .includeQuoteField(false)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(true)
                .includeWhatFields(false)
                .graph(existingGraph)
                .build();

        // 验证结果
        assertNotNull(builder);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用Builder模式构建FieldRelationGraphBuilder时传入null graphLayers应该抛出异常
     */
    @Test
    @DisplayName("构建器模式 - null graphLayers应该抛出异常")
    void testBuilder_null_graphLayers() {
        // 执行被测试方法并验证抛出异常
        assertThrows(NullPointerException.class, () -> {
            FieldRelationGraphBuilder.builder()
                    .describeLogicService(describeLogicService)
                    .graphLayers(null)
                    .excludeDefaultValue(false)
                    .includeQuoteField(true)
                    .fillFieldType(false)
                    .ignoreInvalidVariable(true)
                    .excludeMultiCurrencyFields(false)
                    .includeWhatFields(true)
                    .build();
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法构建图的正常场景
     */
    @Test
    @DisplayName("构建图 - 正常场景")
    void testGetGraph_正常场景() {
        // 创建构建器
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 执行被测试方法
        FieldRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
        assertTrue(result.getDescribeMap().containsKey("TestObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法在graphLayers为空时的场景
     */
    @Test
    @DisplayName("构建图 - graphLayers为空")
    void testGetGraph_graphLayers为空() {
        // 创建构建器
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(Lists.newArrayList())
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 执行被测试方法
        FieldRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
        assertTrue(result.getDescribeMap().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法使用现有图的场景
     */
    @Test
    @DisplayName("构建图 - 使用现有图")
    void testGetGraph_使用现有图() {
        // 配置existingGraph Mock
        MutableValueGraph<FieldNode, RelateEdge> mockGraph = mock(MutableValueGraph.class);
        when(mockGraph.nodes()).thenReturn(Sets.newHashSet());
        when(existingGraph.getGraph()).thenReturn(mockGraph);
        when(existingGraph.getDescribeMap()).thenReturn(Maps.newHashMap());

        // 创建构建器
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .graph(existingGraph)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 执行被测试方法
        FieldRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer.of静态工厂方法创建图层
     */
    @Test
    @DisplayName("图层创建 - 使用of静态工厂方法")
    void testGraphLayer_of_静态工厂方法() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(objectDescribe);

        // 执行被测试方法
        GraphLayer layer = GraphLayer.of(describes);

        // 验证结果
        assertNotNull(layer);
        assertEquals(describes, layer.getDescribeList());
        assertFalse(layer.isExcludeDefaultValue()); // 默认值应该为false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer.of静态工厂方法创建图层并指定excludeDefaultValue
     */
    @Test
    @DisplayName("图层创建 - 指定excludeDefaultValue")
    void testGraphLayer_of_指定excludeDefaultValue() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(objectDescribe);

        // 执行被测试方法
        GraphLayer layer = GraphLayer.of(describes, true);

        // 验证结果
        assertNotNull(layer);
        assertEquals(describes, layer.getDescribeList());
        assertTrue(layer.isExcludeDefaultValue());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer创建时传入空列表的场景
     */
    @Test
    @DisplayName("图层创建 - 空描述列表")
    void testGraphLayer_of_空描述列表() {
        // 准备测试数据
        List<IObjectDescribe> emptyDescribes = Lists.newArrayList();

        // 执行被测试方法
        GraphLayer layer = GraphLayer.of(emptyDescribes);

        // 验证结果
        assertNotNull(layer);
        assertEquals(emptyDescribes, layer.getDescribeList());
        assertTrue(layer.getDescribeList().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer创建时传入null列表的场景
     */
    @Test
    @DisplayName("图层创建 - null描述列表")
    void testGraphLayer_of_null描述列表() {
        // 执行被测试方法
        GraphLayer layer = GraphLayer.of(null);

        // 验证结果
        assertNotNull(layer);
        assertNull(layer.getDescribeList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建器的所有布尔参数设置
     */
    @Test
    @DisplayName("构建器参数 - 所有布尔参数设置")
    void testBuilder_所有布尔参数设置() {
        // 执行被测试方法
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(true)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(true)
                .excludeMultiCurrencyFields(true)
                .includeWhatFields(true)
                .build();

        // 验证结果
        assertNotNull(builder);

        // 构建图验证参数生效
        FieldRelationGraph result = builder.getGraph();
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建器在没有设置describeLogicService时的场景
     */
    @Test
    @DisplayName("构建器参数 - 未设置describeLogicService")
    void testBuilder_未设置describeLogicService() {
        // 执行被测试方法
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .graphLayers(graphLayers)
                .excludeDefaultValue(false)
                .includeQuoteField(false)
                .fillFieldType(false)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 验证结果
        assertNotNull(builder);

        // 构建图应该仍然可以工作
        FieldRelationGraph result = builder.getGraph();
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建器的链式调用
     */
    @Test
    @DisplayName("构建器模式 - 链式调用")
    void testBuilder_链式调用() {
        // 执行被测试方法 - 测试链式调用
        FieldRelationGraph result = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(true)
                .includeQuoteField(false)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(true)
                .includeWhatFields(false)
                .build()
                .getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer的getter方法
     */
    @Test
    @DisplayName("图层方法 - getter方法测试")
    void testGraphLayer_getter方法() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(objectDescribe);
        GraphLayer layer = GraphLayer.of(describes, true);

        // 验证getter方法
        assertEquals(describes, layer.getDescribeList());
        assertTrue(layer.isExcludeDefaultValue());

        // 测试另一个构造方式
        GraphLayer layer2 = GraphLayer.of(describes, false);
        assertEquals(describes, layer2.getDescribeList());
        assertFalse(layer2.isExcludeDefaultValue());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式的各种配置选项
     */
    @Test
    @DisplayName("构建器模式 - 各种配置选项")
    void testBuilder_各种配置选项() {
        // 执行被测试方法 - 测试不同的配置组合
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(true)
                .includeQuoteField(true)
                .fillFieldType(false)
                .ignoreInvalidVariable(true)
                .excludeMultiCurrencyFields(true)
                .includeWhatFields(false)
                .build();

        // 验证结果
        assertNotNull(builder);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法的基本场景
     */
    @Test
    @DisplayName("构建图 - 基本场景")
    void testGetGraph_基本场景() {
        // 创建构建器
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(false)
                .includeQuoteField(false)
                .fillFieldType(false)
                .ignoreInvalidVariable(true)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(true)
                .build();

        // 执行被测试方法
        FieldRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer的空列表场景
     */
    @Test
    @DisplayName("GraphLayer创建 - 空列表场景")
    void testGraphLayer_空列表场景() {
        // 准备测试数据
        List<IObjectDescribe> emptyDescribes = Lists.newArrayList();

        // 执行被测试方法
        GraphLayer result = GraphLayer.of(emptyDescribes);

        // 验证结果
        assertNotNull(result);
        assertEquals(emptyDescribes, result.getDescribeList());
        assertTrue(result.getDescribeList().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式的所有参数设置
     */
    @Test
    @DisplayName("构建器 - 完整参数设置")
    void testBuilder_完整参数设置() {
        // 执行被测试方法
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(true)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(true)
                .includeWhatFields(false)
                .build();

        // 验证结果
        assertNotNull(builder);

        // 测试构建图
        FieldRelationGraph result = builder.getGraph();
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式的必需参数验证
     */
    @Test
    @DisplayName("构建器 - 参数验证和容错性")
    void testBuilder_参数验证和容错性() {
        // 测试缺少describeLogicService时的构建行为
        FieldRelationGraphBuilder builder1 = FieldRelationGraphBuilder.builder()
                .graphLayers(graphLayers)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 验证builder能正常构建，但getGraph()可能会有问题
        assertNotNull(builder1);

        // 测试使用空graphLayers时的构建行为
        List<GraphLayer> emptyGraphLayers = Lists.newArrayList();
        FieldRelationGraphBuilder builder2 = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(emptyGraphLayers)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 验证builder能正常构建
        assertNotNull(builder2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法的多次调用一致性
     */
    @Test
    @DisplayName("构建图 - 多次调用一致性")
    void testGetGraph_多次调用一致性() {
        // 创建构建器
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 执行被测试方法 - 多次调用
        FieldRelationGraph result1 = builder.getGraph();
        FieldRelationGraph result2 = builder.getGraph();

        // 验证结果
        assertNotNull(result1);
        assertNotNull(result2);
        // 注意：根据实现，可能返回相同实例或不同实例，这里主要验证都不为null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer的属性访问方法
     */
    @Test
    @DisplayName("GraphLayer - 属性访问方法")
    void testGraphLayer_属性访问方法() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(objectDescribe);

        // 执行被测试方法
        GraphLayer result = GraphLayer.of(describes);

        // 验证结果
        assertNotNull(result);
        assertEquals(describes, result.getDescribeList());
        assertSame(describes, result.getDescribeList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试不同配置组合的构建场景
     */
    @Test
    @DisplayName("构建图 - 不同配置组合")
    void testGetGraph_不同配置组合() {
        // 测试配置组合1：排除默认值，包含引用字段
        FieldRelationGraphBuilder builder1 = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(true)
                .includeQuoteField(true)
                .fillFieldType(false)
                .ignoreInvalidVariable(true)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        FieldRelationGraph result1 = builder1.getGraph();
        assertNotNull(result1);

        // 测试配置组合2：包含默认值，排除引用字段
        FieldRelationGraphBuilder builder2 = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(graphLayers)
                .excludeDefaultValue(false)
                .includeQuoteField(false)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(true)
                .includeWhatFields(true)
                .build();

        FieldRelationGraph result2 = builder2.getGraph();
        assertNotNull(result2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - 单个GraphLayer
     */
    @Test
    @DisplayName("构建图 - 单个GraphLayer")
    void testGetGraph_单个GraphLayer() {
        // 准备测试数据 - 只有一个GraphLayer
        List<GraphLayer> singleGraphLayer = Lists.newArrayList(
                GraphLayer.of(Lists.newArrayList(objectDescribe))
        );

        // 创建构建器
        FieldRelationGraphBuilder builder = FieldRelationGraphBuilder.builder()
                .describeLogicService(describeLogicService)
                .graphLayers(singleGraphLayer)
                .excludeDefaultValue(false)
                .includeQuoteField(true)
                .fillFieldType(true)
                .ignoreInvalidVariable(false)
                .excludeMultiCurrencyFields(false)
                .includeWhatFields(false)
                .build();

        // 执行被测试方法
        FieldRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }
}
