package com.facishare.paas.appframework.metadata.domain;

import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService.FieldConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;
import java.util.*;
import com.google.common.collect.Sets;

/**
 * Unit tests for DomainPluginLogicService.FieldConfig
 * GenerateByAI: Comprehensive tests for FieldConfig inner class
 */
public class DomainPluginLogicServiceFieldConfigTest {

    private FieldConfig fieldConfig;

    @BeforeEach
    public void setUp() {
        fieldConfig = new FieldConfig();
    }

    @Test
    public void testDefaultConstructor() {
        // Test default constructor
        FieldConfig config = new FieldConfig();
        
        assertNotNull(config);
        assertNull(config.getReadOnlyFieldMap());
        assertNull(config.getHiddenFieldMap());
    }

    @Test
    public void testBuilderPattern() {
        // Test builder pattern
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        Map<String, Set<String>> hiddenMap = new HashMap<>();
        
        FieldConfig config = FieldConfig.builder()
                .readOnlyFieldMap(readOnlyMap)
                .hiddenFieldMap(hiddenMap)
                .build();
        
        assertNotNull(config);
        assertEquals(readOnlyMap, config.getReadOnlyFieldMap());
        assertEquals(hiddenMap, config.getHiddenFieldMap());
    }

    @Test
    public void testAllArgsConstructor() {
        // Test all args constructor
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        Map<String, Set<String>> hiddenMap = new HashMap<>();
        
        FieldConfig config = new FieldConfig(readOnlyMap, hiddenMap);
        
        assertNotNull(config);
        assertEquals(readOnlyMap, config.getReadOnlyFieldMap());
        assertEquals(hiddenMap, config.getHiddenFieldMap());
    }

    @Test
    public void testReadOnlyFieldMapGetterAndSetter() {
        // Test readOnlyFieldMap getter and setter
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        readOnlyMap.put("object1", Sets.newHashSet("field1", "field2"));
        
        fieldConfig.setReadOnlyFieldMap(readOnlyMap);
        
        assertEquals(readOnlyMap, fieldConfig.getReadOnlyFieldMap());
    }

    @Test
    public void testHiddenFieldMapGetterAndSetter() {
        // Test hiddenFieldMap getter and setter
        Map<String, Set<String>> hiddenMap = new HashMap<>();
        hiddenMap.put("object1", Sets.newHashSet("field3", "field4"));
        
        fieldConfig.setHiddenFieldMap(hiddenMap);
        
        assertEquals(hiddenMap, fieldConfig.getHiddenFieldMap());
    }

    @Test
    public void testGetReadOnlyFieldsWithValidObjectApiName() {
        // Test getReadOnlyFields with valid object API name
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        Set<String> fields = Sets.newHashSet("field1", "field2");
        readOnlyMap.put("testObject", fields);
        
        fieldConfig.setReadOnlyFieldMap(readOnlyMap);
        
        Set<String> result = fieldConfig.getReadOnlyFields("testObject");
        assertEquals(fields, result);
    }

    @Test
    public void testGetReadOnlyFieldsWithNonExistentObjectApiName() {
        // Test getReadOnlyFields with non-existent object API name
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        readOnlyMap.put("testObject", Sets.newHashSet("field1"));
        
        fieldConfig.setReadOnlyFieldMap(readOnlyMap);
        
        Set<String> result = fieldConfig.getReadOnlyFields("nonExistentObject");
        assertNull(result);
    }

    @Test
    public void testGetReadOnlyFieldsWithEmptyMap() {
        // Test getReadOnlyFields with empty map
        fieldConfig.setReadOnlyFieldMap(new HashMap<>());
        
        Set<String> result = fieldConfig.getReadOnlyFields("testObject");
        assertNull(result);
    }

    @Test
    public void testGetReadOnlyFieldsWithNullMap() {
        // Test getReadOnlyFields with null map
        fieldConfig.setReadOnlyFieldMap(null);
        
        Set<String> result = fieldConfig.getReadOnlyFields("testObject");
        assertNull(result);
    }

    @Test
    public void testGetHiddenFieldsWithValidObjectApiName() {
        // Test getHiddenFields with valid object API name
        Map<String, Set<String>> hiddenMap = new HashMap<>();
        Set<String> fields = Sets.newHashSet("field3", "field4");
        hiddenMap.put("testObject", fields);
        
        fieldConfig.setHiddenFieldMap(hiddenMap);
        
        Set<String> result = fieldConfig.getHiddenFields("testObject");
        assertEquals(fields, result);
    }

    @Test
    public void testGetHiddenFieldsWithNonExistentObjectApiName() {
        // Test getHiddenFields with non-existent object API name
        Map<String, Set<String>> hiddenMap = new HashMap<>();
        hiddenMap.put("testObject", Sets.newHashSet("field1"));
        
        fieldConfig.setHiddenFieldMap(hiddenMap);
        
        Set<String> result = fieldConfig.getHiddenFields("nonExistentObject");
        assertNull(result);
    }

    @Test
    public void testGetHiddenFieldsWithEmptyMap() {
        // Test getHiddenFields with empty map
        fieldConfig.setHiddenFieldMap(new HashMap<>());
        
        Set<String> result = fieldConfig.getHiddenFields("testObject");
        assertNull(result);
    }

    @Test
    public void testGetHiddenFieldsWithNullMap() {
        // Test getHiddenFields with null map
        fieldConfig.setHiddenFieldMap(null);
        
        Set<String> result = fieldConfig.getHiddenFields("testObject");
        assertNull(result);
    }

    @Test
    public void testComplexFieldConfiguration() {
        // Test complex field configuration
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        readOnlyMap.put("object1", Sets.newHashSet("field1", "field2"));
        readOnlyMap.put("object2", Sets.newHashSet("field3"));

        Map<String, Set<String>> hiddenMap = new HashMap<>();
        hiddenMap.put("object1", Sets.newHashSet("field4", "field5"));
        hiddenMap.put("object3", Sets.newHashSet("field6"));
        
        fieldConfig.setReadOnlyFieldMap(readOnlyMap);
        fieldConfig.setHiddenFieldMap(hiddenMap);
        
        // Test object1 - has both readonly and hidden fields
        assertEquals(Sets.newHashSet("field1", "field2"), fieldConfig.getReadOnlyFields("object1"));
        assertEquals(Sets.newHashSet("field4", "field5"), fieldConfig.getHiddenFields("object1"));

        // Test object2 - has only readonly fields
        assertEquals(Sets.newHashSet("field3"), fieldConfig.getReadOnlyFields("object2"));
        assertNull(fieldConfig.getHiddenFields("object2"));

        // Test object3 - has only hidden fields
        assertNull(fieldConfig.getReadOnlyFields("object3"));
        assertEquals(Sets.newHashSet("field6"), fieldConfig.getHiddenFields("object3"));
    }

    @Test
    public void testFieldConfigWithEmptyFieldSets() {
        // Test field config with empty field sets
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        readOnlyMap.put("object1", new HashSet<>());
        
        Map<String, Set<String>> hiddenMap = new HashMap<>();
        hiddenMap.put("object1", new HashSet<>());
        
        fieldConfig.setReadOnlyFieldMap(readOnlyMap);
        fieldConfig.setHiddenFieldMap(hiddenMap);
        
        Set<String> readOnlyFields = fieldConfig.getReadOnlyFields("object1");
        Set<String> hiddenFields = fieldConfig.getHiddenFields("object1");
        
        assertNotNull(readOnlyFields);
        assertNotNull(hiddenFields);
        assertTrue(readOnlyFields.isEmpty());
        assertTrue(hiddenFields.isEmpty());
    }

    @Test
    public void testFieldConfigWithNullFieldSets() {
        // Test field config with null field sets
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        readOnlyMap.put("object1", null);
        
        Map<String, Set<String>> hiddenMap = new HashMap<>();
        hiddenMap.put("object1", null);
        
        fieldConfig.setReadOnlyFieldMap(readOnlyMap);
        fieldConfig.setHiddenFieldMap(hiddenMap);
        
        Set<String> readOnlyFields = fieldConfig.getReadOnlyFields("object1");
        Set<String> hiddenFields = fieldConfig.getHiddenFields("object1");
        
        assertNull(readOnlyFields);
        assertNull(hiddenFields);
    }

    @Test
    public void testFieldConfigEquality() {
        // Test field config equality
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        readOnlyMap.put("object1", Sets.newHashSet("field1"));

        Map<String, Set<String>> hiddenMap = new HashMap<>();
        hiddenMap.put("object1", Sets.newHashSet("field2"));
        
        FieldConfig config1 = new FieldConfig(readOnlyMap, hiddenMap);
        FieldConfig config2 = new FieldConfig(readOnlyMap, hiddenMap);
        
        assertEquals(config1.getReadOnlyFieldMap(), config2.getReadOnlyFieldMap());
        assertEquals(config1.getHiddenFieldMap(), config2.getHiddenFieldMap());
    }

    @Test
    public void testFieldConfigWithSpecialCharacters() {
        // Test field config with special characters in object and field names
        Map<String, Set<String>> readOnlyMap = new HashMap<>();
        readOnlyMap.put("object_with_underscores", Sets.newHashSet("field-with-dashes", "field.with.dots"));

        Map<String, Set<String>> hiddenMap = new HashMap<>();
        hiddenMap.put("object@special", Sets.newHashSet("field#special", "field$special"));

        fieldConfig.setReadOnlyFieldMap(readOnlyMap);
        fieldConfig.setHiddenFieldMap(hiddenMap);

        assertEquals(Sets.newHashSet("field-with-dashes", "field.with.dots"),
                fieldConfig.getReadOnlyFields("object_with_underscores"));
        assertEquals(Sets.newHashSet("field#special", "field$special"),
                fieldConfig.getHiddenFields("object@special"));
    }
}
