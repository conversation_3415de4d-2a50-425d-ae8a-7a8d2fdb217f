package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SelectOneExt的JUnit 5测试类
 * 测试单选字段扩展类功能
 * <p>
 * GenerateByAI
 * 测试内容描述：测试单选字段扩展的创建、选项处理和标签获取功能
 */
class SelectOneExtJUnit5Test {

    private SelectOne mockSelectOne;
    private SelectOneExt selectOneExt;
    private ISelectOption mockOption1;
    private ISelectOption mockOption2;
    private ISelectOption mockOption3;

    @BeforeEach
    void setUp() {
        mockSelectOne = mock(SelectOne.class);
        selectOneExt = SelectOneExt.of(mockSelectOne);

        // 创建模拟选项
        mockOption1 = mock(ISelectOption.class);
        when(mockOption1.getValue()).thenReturn("value1");
        when(mockOption1.getLabel()).thenReturn("Label 1");

        mockOption2 = mock(ISelectOption.class);
        when(mockOption2.getValue()).thenReturn("value2");
        when(mockOption2.getLabel()).thenReturn("Label 2");

        mockOption3 = mock(ISelectOption.class);
        when(mockOption3.getValue()).thenReturn("value3");
        when(mockOption3.getLabel()).thenReturn("Label 3");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of工厂方法
     */
    @Test
    @DisplayName("工厂方法 - of方法创建实例")
    void testOf() {
        // Act: 使用of方法创建实例
        SelectOneExt result = SelectOneExt.of(mockSelectOne);

        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(mockSelectOne, result.getSelectOne());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelByValue方法 - 基本功能
     */
    @Test
    @DisplayName("基本功能 - getLabelByValue方法")
    void testGetLabelByValue_BasicFunction() {
        // Arrange: 设置选项返回
        when(mockSelectOne.getOption("value1")).thenReturn(Optional.of(mockOption1));

        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue("value1");

        // Assert: 验证结果
        assertEquals("Label 1", result);
        verify(mockSelectOne).getOption("value1");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelByValue方法 - 不存在的值
     */
    @Test
    @DisplayName("基本功能 - getLabelByValue方法不存在的值")
    void testGetLabelByValue_NonExistentValue() {
        // Arrange: 设置选项不存在
        when(mockSelectOne.getOption("non_existent")).thenReturn(Optional.empty());

        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue("non_existent");

        // Assert: 验证结果
        assertEquals("", result);
        verify(mockSelectOne).getOption("non_existent");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelByValue方法 - null值
     */
    @Test
    @DisplayName("边界条件 - getLabelByValue方法null值")
    void testGetLabelByValue_NullValue() {
        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue(null);

        // Assert: 验证结果
        assertNull(result);
        verify(mockSelectOne, never()).getOption(any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLabelByValue方法 - 空字符串值
     */
    @Test
    @DisplayName("边界条件 - getLabelByValue方法空字符串值")
    void testGetLabelByValue_EmptyStringValue() {
        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue("");

        // Assert: 验证结果
        assertNull(result);
        verify(mockSelectOne, never()).getOption(any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOptionType方法
     */
    @Test
    @DisplayName("选项类型 - getOptionType方法")
    void testGetOptionType() {
        // Arrange: 设置选项类型
        SelectOneExt selectOne = SelectOneExt.of(new SelectOneFieldDescribe());
        selectOne.setOptionalType("custom");

        // Act: 执行getOptionType方法
        String result = selectOne.getOptionType();

        // Assert: 验证结果
        assertEquals("custom", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOptionApiName方法
     */
    @Test
    @DisplayName("选项API名称 - getOptionApiName方法")
    void testGetOptionApiName() {
        // Arrange: 设置选项API名称
        when(mockSelectOne.get(SelectOne.OPTION_API_NAME, String.class)).thenReturn("custom_option");

        // Act: 执行getOptionApiName方法
        String result = selectOneExt.getOptionApiName();

        // Assert: 验证结果
        assertEquals("custom_option", result);
        verify(mockSelectOne).get(SelectOne.OPTION_API_NAME, String.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setOptionApiName方法
     */
    @Test
    @DisplayName("选项API名称 - setOptionApiName方法")
    void testSetOptionApiName() {
        // Act: 执行setOptionApiName方法
        selectOneExt.setOptionApiName("new_option");

        // Assert: 验证设置
        verify(mockSelectOne).set(SelectOne.OPTION_API_NAME, "new_option");
        verify(mockSelectOne).set(SelectOne.OPTION_TYPE, SelectOne.GENERAL_OPTION_TYPE);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化值查找
     */
    @ParameterizedTest
    @CsvSource({
            "value1, Label 1",
            "value2, Label 2",
            "value3, Label 3"
    })
    @DisplayName("参数化测试 - getLabelByValue多种值")
    void testGetLabelByValue_ParameterizedValues(String value, String expectedLabel) {
        // Arrange: 设置对应的选项
        ISelectOption mockOption = mock(ISelectOption.class);
        when(mockOption.getLabel()).thenReturn(expectedLabel);
        when(mockSelectOne.getOption(value)).thenReturn(Optional.of(mockOption));

        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue(value);

        // Assert: 验证结果
        assertEquals(expectedLabel, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数化不存在的值
     */
    @ParameterizedTest
    @ValueSource(strings = {"invalid1", "invalid2", "invalid3", "non_existent"})
    @DisplayName("参数化测试 - getLabelByValue不存在的值")
    void testGetLabelByValue_ParameterizedNonExistentValues(String value) {
        // Arrange: 设置选项不存在
        when(mockSelectOne.getOption(value)).thenReturn(Optional.empty());

        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue(value);

        // Assert: 验证结果
        assertEquals("", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托功能
     */
    @Test
    @DisplayName("委托功能 - 验证委托方法调用")
    void testDelegateFeatures() {
        // Arrange: 设置委托方法的返回值
        when(mockSelectOne.getApiName()).thenReturn("test_select_one");
        when(mockSelectOne.getLabel()).thenReturn("Test Select One");
        when(mockSelectOne.isRequired()).thenReturn(true);

        // Act & Assert: 验证委托的方法
        assertEquals("test_select_one", selectOneExt.getApiName());
        assertEquals("Test Select One", selectOneExt.getLabel());
        assertTrue(selectOneExt.isRequired());

        // 验证委托调用
        verify(mockSelectOne).getApiName();
        verify(mockSelectOne).getLabel();
        verify(mockSelectOne).isRequired();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试选项类型常量
     */
    @Test
    @DisplayName("常量验证 - 选项类型常量")
    void testOptionTypeConstants() {
        // Assert: 验证常量存在
        assertNotNull(SelectOne.OPTION_TYPE);
        assertNotNull(SelectOne.OPTION_API_NAME);
        assertNotNull(SelectOne.GENERAL_OPTION_TYPE);

        // 验证常量值
        assertEquals("option_type", SelectOne.OPTION_TYPE);
        assertEquals("option_api_name", SelectOne.OPTION_API_NAME);
        assertEquals("general", SelectOne.GENERAL_OPTION_TYPE);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setOptionApiName方法的完整性
     */
    @Test
    @DisplayName("完整性验证 - setOptionApiName方法完整性")
    void testSetOptionApiName_Completeness() {
        // Arrange: 准备测试数据
        String optionApiName = "test_option_api";

        // Act: 执行setOptionApiName方法
        selectOneExt.setOptionApiName(optionApiName);

        // Assert: 验证两个设置都被调用
        verify(mockSelectOne).set(SelectOne.OPTION_API_NAME, optionApiName);
        verify(mockSelectOne).set(SelectOne.OPTION_TYPE, SelectOne.GENERAL_OPTION_TYPE);

        // 验证调用次数
        verify(mockSelectOne, times(2)).set(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - 特殊字符值
     */
    @ParameterizedTest
    @ValueSource(strings = {"value with space", "value-with-dash", "value_with_underscore", "value.with.dot"})
    @DisplayName("边界条件 - 特殊字符值处理")
    void testGetLabelByValue_SpecialCharacters(String value) {
        // Arrange: 设置特殊字符选项
        ISelectOption mockOption = mock(ISelectOption.class);
        when(mockOption.getLabel()).thenReturn("Special Label");
        when(mockSelectOne.getOption(value)).thenReturn(Optional.of(mockOption));

        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue(value);

        // Assert: 验证结果
        assertEquals("Special Label", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // Arrange: 设置toString返回值
        when(mockSelectOne.toString()).thenReturn("SelectOne[apiName=test_select_one]");

        // Act: 调用toString方法
        String result = selectOneExt.toString();

        // Assert: 验证toString结果
        assertNotNull(result);
        assertTrue(result.contains("SelectOne"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试equals和hashCode方法
     */
    @Test
    @DisplayName("对象比较 - equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Arrange: 创建两个使用相同SelectOne的实例
        SelectOneExt ext1 = SelectOneExt.of(mockSelectOne);
        SelectOneExt ext2 = SelectOneExt.of(mockSelectOne);

        // Assert: 验证equals和hashCode
        assertEquals(ext1.getSelectOne(), ext2.getSelectOne());

        // 创建使用不同SelectOne的实例
        SelectOne anotherSelectOne = mock(SelectOne.class);
        SelectOneExt ext3 = SelectOneExt.of(anotherSelectOne);

        assertNotEquals(ext1.getSelectOne(), ext3.getSelectOne());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Arrange: 设置选项
        when(mockSelectOne.getOption("test_value")).thenReturn(Optional.of(mockOption1));

        // Act: 多次获取相同的数据
        String label1 = selectOneExt.getLabelByValue("test_value");
        String label2 = selectOneExt.getLabelByValue("test_value");

        // Assert: 验证数据一致性
        assertEquals(label1, label2);
        assertSame(selectOneExt.getSelectOne(), selectOneExt.getSelectOne());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null选项处理
     */
    @Test
    @DisplayName("边界条件 - null选项处理")
    void testNullOptionHandling() {
        // Arrange: 设置选项为null（虽然Optional.empty()更常见）
        when(mockSelectOne.getOption("test_value")).thenReturn(Optional.empty());

        // Act: 执行getLabelByValue方法
        String result = selectOneExt.getLabelByValue("test_value");

        // Assert: 验证结果
        assertEquals("", result);
    }
}
