package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.config.ConfigException;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.dto.CurrencyConfig;
import com.facishare.paas.appframework.metadata.dto.QueryExchangeRate;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.multicurrency.CurrencyService;
import com.facishare.paas.appframework.metadata.multicurrency.ExchangeRateService;
import com.facishare.paas.appframework.metadata.multicurrency.RefreshInternational;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtCurrencyExchange;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.para.MultiCurrencyOpen;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IMetadataMultiCurrencyService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.social.personnel.PersonnelObjService;
import com.facishare.social.personnel.model.FindByUserId;
import com.facishare.social.personnel.model.PersonnelDto;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.MultiCurrencyStatus.userCurrencyTransform;

@Slf4j
@Service("multiCurrencyLogicService")
public class MultiCurrencyLogicServiceImpl implements MultiCurrencyLogicService {

    @Autowired
    MultiCurrencyLogicService multiCurrencyLogicService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CurrencyService currencyService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Resource
    IMetadataMultiCurrencyService metadataMultiCurrencyService;

    @Autowired
    private IRepository<MtCurrencyExchange> currencyExchangeRepository;

    @Autowired
    PersonnelObjService personnelObjService;

    public static final String MULTI_CURRENCY_KEY = "multi_currency_config";
    public static final String USER_CURRENCY_TRANSFORM_KEY = "user_currency_transform";

    @Override
    public void openMultiCurrency(String functionalCurrency, User user) {
        if (StringUtils.isBlank(functionalCurrency)) {
            log.warn("openMultiCurrency error,functionalCurrency empty,user:{}", user);
            throw new ValidateException(I18nMessage.of(I18NKey.MULTI_CURRENCY_PARAM_ERROR, I18N.text(I18NKey.MULTI_CURRENCY_PARAM_ERROR)));
        }
        List<String> initCurrencyList = initCurrencyList();
        if (initCurrencyList.stream().noneMatch(x -> x.equals(functionalCurrency))) {
            log.warn("openMultiCurrency error,functionalCurrency not exist,user:{},functionalCurrency:{}", user, functionalCurrency);
            throw new ValidateException(I18nMessage.of(I18NKey.MULTI_CURRENCY_PARAM_ERROR, I18N.text(I18NKey.MULTI_CURRENCY_PARAM_ERROR)));
        }
        String status = configService.findTenantConfig(user, MULTI_CURRENCY_KEY);
        if (StringUtils.isNotBlank(status) && (MultiCurrencyStatus.OPEN_SUCCESS.getStatus().equals(Integer.parseInt(status)) ||
                MultiCurrencyStatus.OPENING.getStatus().equals(Integer.parseInt(status)))) {
            return;
        }
        List<String> objectApiNames = findCurrencyObjectApiNames(user);
        processMultiCurrency(functionalCurrency, objectApiNames, user);
    }

    @Override
    public void changeUserCurrencyTransStatus(MultiCurrencyStatus status, User user) {
        if (Objects.isNull(status) || !userCurrencyTransform.contains(status)) {
            log.warn("changeUserCurrencyTransStatus failed {}", status);
            throw new ValidateException(I18nMessage.ofI8nKey(I18NKey.USER_CURRENCY_TRANSFORM_FAILED));
        }
        try {
            configService.upsertTenantConfig(user, USER_CURRENCY_TRANSFORM_KEY,
                    String.valueOf(status.getStatus()), ConfigValueType.STRING);
            log.info("changeUserCurrencyTransStatus {}", status.getLabel());
        } catch (ConfigException e) {
            log.warn("changeUserCurrencyTransStatus failed {}", status.getLabel(), e);
            throw new ValidateException(I18nMessage.ofI8nKey(I18NKey.USER_CURRENCY_TRANSFORM_FAILED));
        }
    }

    @Override
    public void refreshMultiCurrency(RefreshInternational.Arg arg, User user) {
        String status = configService.findTenantConfig(user, MULTI_CURRENCY_KEY);
        //未开启的不能补刷
        if (!(StringUtils.isNotBlank(status) && MultiCurrencyStatus.OPEN_SUCCESS.getStatus().equals(Integer.parseInt(status)))) {
            return;
        }
        String functionalCurrency = arg.getFunctionalCurrency();
        if (StringUtils.isBlank(functionalCurrency)) {
            functionalCurrency = findFunctionalCurrency(user).getCurrencyCode();
        }
        List<String> objectApiNames;
        if (arg.getOnlySpecialObjects()) {
            objectApiNames = arg.getObjectApiNames().stream()
                    .filter(x -> !AppFrameworkConfig.getCurrencyBlackObjectList().contains(x))
                    .collect(Collectors.toList());
        } else {
            objectApiNames = findCurrencyObjectApiNames(user);
        }
        if (CollectionUtils.empty(objectApiNames)) {
            return;
        }
        reOpenMultiCurrency(functionalCurrency, objectApiNames, user, Boolean.TRUE.equals(arg.getReplaceOldField()), false);
        refreshCurrencyOptionsSkipValidate(user, objectApiNames, false);
    }

    private void processMultiCurrency(String functionalCurrency, List<String> objectApiNames, User user) {
        if (CollectionUtils.empty(objectApiNames)) {
            log.warn("openMultiCurrency objectApiNames is null,user:{}", user);
            return;
        }
        String exchangeRateVersion = String.valueOf(System.currentTimeMillis());
        MtCurrency mtCurrency = MtCurrency.getFuntionalMtCurrency(functionalCurrency, user, exchangeRateVersion);// 本位币
        MtExchangeRate mtExchangeRate = MtExchangeRate.getFuncionalMtExchangeRate(functionalCurrency, user, exchangeRateVersion);
        try {   // 不应该是Lists.newArrayList(mtCurrency), 而是企业全币种
            MultiCurrencyOpen.Arg arg = buildMultiCurrencyOpenArg(user, Lists.newArrayList(mtCurrency), objectApiNames, functionalCurrency, false);
            configService.upsertTenantConfig(user, MULTI_CURRENCY_KEY, String.valueOf(MultiCurrencyStatus.OPENING.getStatus()), ConfigValueType.STRING);
            ParallelUtils.createBackgroundTask().submit(() -> {
                log.info("openMultiCurrency user:{},arg:{}", user, JSON.toJSONString(arg));
                MultiCurrencyOpen.Result result = new MultiCurrencyOpen.Result();
                try {
                    result = metadataMultiCurrencyService.openMultiCurrency(arg);
                } catch (Exception e) {
                    log.error("openMultiCurrency error,user:{}", user, e);
                }
                if (result.isSuccess()) {
                    log.info("openMultiCurrency success,user:{},result:{}", user, result);
                    configService.updateTenantConfig(user, MULTI_CURRENCY_KEY, String.valueOf(MultiCurrencyStatus.OPEN_SUCCESS.getStatus()), ConfigValueType.STRING);
                    multiCurrencyLogicService.upsertMultiCurrency(user, Lists.newArrayList(mtCurrency), Lists.newArrayList(mtExchangeRate));
                } else {
                    log.warn("openMultiCurrency failed,user:{},msg:{}", user, result.getMsg());
                    configService.updateTenantConfig(user, MULTI_CURRENCY_KEY, String.valueOf(MultiCurrencyStatus.OPEN_FAIL.getStatus()), ConfigValueType.STRING);
                }
            }).run();
        } catch (Exception e) {
            configService.deleteTenantConfig(user, MULTI_CURRENCY_KEY);
            log.error("openMultiCurrency failed,user:{}", user, e);
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.MULTI_CURRENCY_OPEN_EXCEPTION, I18N.text(I18NKey.MULTI_CURRENCY_OPEN_EXCEPTION)));
        }
    }

    //多币种黑名单
    private List<String> findCurrencyObjectApiNames(User user) {
        List<IObjectDescribe> describeList = describeLogicService.findAllObjectsByTenantId(user.getTenantId(),
                "", false, false,
                false, false, "");
        List<String> objectApiNames = describeList.stream().map(IObjectDescribe::getApiName)
                .filter(x -> !AppFrameworkConfig.getCurrencyBlackObjectList().contains(x)).collect(Collectors.toList());
        List<IObjectDescribe> describes = describeLogicService.findObjectList(user.getTenantId(), Lists.newArrayList(objectApiNames));

        // 过滤只包含币种字段的对象
        List<String> filteredObjectApiNames = describes.stream()
                .filter(this::containsCurrencyFields)
                .map(IObjectDescribe::getApiName)
                .collect(Collectors.toList());

        log.info("findCurrencyObjectApiNames,user:{},originalCount:{},filteredCount:{},filteredObjectApiNames:{}",
                user, objectApiNames.size(), filteredObjectApiNames.size(), filteredObjectApiNames);
        return filteredObjectApiNames;
    }

    /**
     * 检查对象是否包含币种字段
     * 条件1：检查对象中是否直接包含币种字段包含则不刷多币种
     * 条件2：检查计算统计引用字段的返回值是否为币种字段类型
     */
    private boolean containsCurrencyFields(IObjectDescribe objectDescribe) {
        return objectDescribe.getFieldDescribes().stream()
                .anyMatch(field -> {
                    FieldDescribeExt fieldExt = FieldDescribeExt.of(field);
                    // 条件1：检查是否直接为币种字段存在多币种不需要重复刷
                    if (fieldExt.isMultiCurrencyFields()) {
                        return false;
                    }
                    // 条件2：检查计算统计引用字段的返回值是否为币种字段类型
                    return IFieldType.CURRENCY.equals(fieldExt.getTypeOrReturnType());
                });
    }

    private void reOpenMultiCurrency(String functionalCurrency, List<String> objectApiNames, User user, boolean replaceOldField, boolean sendMq) {
        if (CollectionUtils.empty(objectApiNames)) {
            log.warn("reOpenMultiCurrency objectApiNames empty,user:{}", user);
            return;
        }
        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                MtCurrency mtCurrency = MtCurrency.getFuntionalMtCurrency(functionalCurrency, user, String.valueOf(System.currentTimeMillis()));
                MultiCurrencyOpen.Arg arg = buildMultiCurrencyOpenArg(user, Lists.newArrayList(mtCurrency), objectApiNames, functionalCurrency, false);
                arg.setReplace(replaceOldField);
                arg.setSendMq(sendMq);
                log.info("reOpenMultiCurrency arg:{}", JSON.toJSONString(arg));
                MultiCurrencyOpen.Result result = metadataMultiCurrencyService.openMultiCurrency(arg);
                if (result.isSuccess()) {
                    log.info("reOpenMultiCurrency success,user:{},result:{}", user, result);
                } else {
                    log.warn("reOpenMultiCurrency failed,user:{},msg:{}", user, result.getMsg());
                }
            } catch (Exception e) {
                log.error("reOpenMultiCurrency error,user:{}", user, e);
            }
        }).run();
    }

    @Transactional
    @Override
    public void upsertMultiCurrency(User user, List<MtCurrency> mtCurrencyList, List<MtExchangeRate> mtExchangeRateList) {
        currencyService.batchUpsertCurrency(user, mtCurrencyList);
        exchangeRateService.batchInsertExchangeRate(user, mtExchangeRateList);
    }


    @Override
    public void deleteMultiCurrencyByCodes(User user, List<String> currencyCodes) {
        if (CollectionUtils.empty(currencyCodes)) {
            return;
        }
        List<MtCurrency> mtCurrencyList = findCurrencyByCodes(user, currencyCodes);
        currencyService.deleteCurrency(user, mtCurrencyList);
    }

    /**
     * 构造刷币种参数
     *
     * @param user               用户信息
     * @param currencyList       企业现存的所有币种
     * @param objectApiNames     所有要币种信息的对象
     * @param functionalCurrency 本位币信息
     * @param isRefreshOption    是否刷选项值
     * @return 开启币种接口入参
     */
    public MultiCurrencyOpen.Arg buildMultiCurrencyOpenArg(User user, List<MtCurrency> currencyList, List<String> objectApiNames,
                                                           String functionalCurrency, boolean isRefreshOption) {

        if (CollectionUtils.empty(currencyList)) {
            log.warn("buildMultiCurrencyOpenArg empty!,user:{}", user);
            throw new ValidateException(I18nMessage.of(I18NKey.MULTI_CURRENCY_PARAM_ERROR, I18N.text(I18NKey.MULTI_CURRENCY_PARAM_ERROR)));
        }
        MultiCurrencyOpen.Arg arg = new MultiCurrencyOpen.Arg();
        Map<String, String> fieldApiNameOption = Maps.newHashMap();
        List<Map<String, Object>> optionList = Lists.newArrayList();
        currencyList.forEach(currency -> {
            Map<String, Object> optionMap = Maps.newHashMap();
            optionMap.put(ISelectOption.OPTION_LABEL, currency.getI18NCurrencyLabel());
            optionMap.put(ISelectOption.NOT_USABLE, DELETE_STATUS.INVALID.getValue() == currency.getStatus());
            optionMap.put(ISelectOption.OPTION_VALUE, currency.getCurrencyCode());
            optionList.add(optionMap);
        });
        String option = JSON.toJSONString(optionList);
        if (isRefreshOption) {
            fieldApiNameOption.put(FieldDescribeExt.CURRENCY_FIELD, option);
        }
        arg.setTenantId(user.getTenantId());
        arg.setDescribeApiName(objectApiNames);
        arg.setCurrencyOptions(option); // 币种字段的选项
        arg.setSendMq(true);
        arg.setLang(RequestContextManager.getContext().getLang().getValue());
        if (StringUtils.isNotBlank(functionalCurrency)) {   // 本位币(隐藏字段)的选项
            Map<String, Object> optionMap = Maps.newHashMap();
            optionMap.put(ISelectOption.OPTION_LABEL, MtCurrency.getI18NLabel(functionalCurrency));
            optionMap.put(ISelectOption.NOT_USABLE, false);
            optionMap.put(ISelectOption.OPTION_VALUE, functionalCurrency);
            String functionalOption = JSON.toJSONString(Lists.newArrayList(optionMap));
            arg.setFunctionalCurrencyOptions(functionalOption);
            if (isRefreshOption) {
                fieldApiNameOption.put(FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD, functionalOption);
            }
        }
        if (CollectionUtils.notEmpty(fieldApiNameOption)) {
            arg.setFieldApiNameOptionMap(fieldApiNameOption);
        }
        return arg;
    }

    @Override
    public Integer findMultiCurrencyStatus(User user) {
        String status = configService.findTenantConfig(user, MULTI_CURRENCY_KEY);
        if (StringUtils.isBlank(status)) {
            return MultiCurrencyStatus.UNOPENED.getStatus();
        }
        return Integer.parseInt(status);
    }

    @Override
    public CurrencyConfig findCurrencyTransStatus(User user) {
        return CurrencyConfig.of(configService.queryTenantConfigs(user, Lists.newArrayList(MULTI_CURRENCY_KEY, USER_CURRENCY_TRANSFORM_KEY)));
    }


    @Override
    public boolean isOpenMultiCurrency(User user) {
        Integer multiCurrencyStatus = findMultiCurrencyStatus(user);
        if (Objects.equals(MultiCurrencyStatus.OPEN_SUCCESS.getStatus(), multiCurrencyStatus)) {
            return true;
        }
        return false;
    }

    @Override
    public List<MtCurrency> findCurrencyList(User user) {
        List<MtCurrency> result = currencyService.queryCurrencyData(user);
        if (CollectionUtils.empty(result)) {
            return Lists.newArrayList();
        }
        result.forEach(x -> x.setCurrencyLabel(x.getI18NCurrencyLabel()));
        return result;
    }


    @Override
    public List<MtCurrency> findCurrencyBySearchQuery(User user, SearchTemplateQuery searchTemplateQuery) {
        List<MtCurrency> result = currencyService.findCurrencyBySearchQuery(user, searchTemplateQuery);
        if (CollectionUtils.empty(result)) {
            return Lists.newArrayList();
        }
        result.forEach(x -> x.setCurrencyLabel(x.getI18NCurrencyLabel()));
        return result;
    }


    @Override
    public List<MtCurrency> findUnusedCurrencyList(User user) {
        List<MtCurrency> unusedCurrencyList = Lists.newArrayList();
        List<String> initCurrencyList = initCurrencyList();
        List<MtCurrency> usedCurrencyList = currencyService.queryCurrencyData(user);
        List<String> usedCode = usedCurrencyList.stream().map(MtCurrency::getCurrencyCode).collect(Collectors.toList());
        for (String code : initCurrencyList) {
            if (usedCode.contains(code)) {
                continue;
            }
            String codeLabel = MtCurrency.getI18NLabel(code);
            MtCurrency currency = new MtCurrency();
            currency.setCurrencyCode(code);
            currency.setCurrencyLabel(codeLabel);
            unusedCurrencyList.add(currency);
        }
        return unusedCurrencyList;
    }

    @Override
    public List<MtCurrency> findAllCurrency(User user) {
        List<String> initCurrencyList = initCurrencyList();
        List<MtCurrency> allCurrency = Lists.newArrayList();
        initCurrencyList.forEach(code -> {
            String codeLabel = MtCurrency.getI18NLabel(code);
            MtCurrency currency = new MtCurrency();
            currency.setCurrencyCode(code);
            currency.setCurrencyLabel(codeLabel);
            allCurrency.add(currency);
        });
        return allCurrency;
    }

    @Override
    public Map<String, String> findCurrencySymbolByCurrencyCodes(List<String> currencyCodes) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.empty(currencyCodes)) {
            return result;
        }
        Map<String, String> currencyCodeAndSymbol = initCurrencyCodeAndSymbol();
        result = currencyCodeAndSymbol.entrySet().stream()
                .filter(x -> currencyCodes.contains(x.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return result;
    }

    @Override
    public String findPersonCurrencyCode(User user) {
        try {
            FindByUserId.Argument argument = new FindByUserId.Argument();
            argument.setTenantId(user.getTenantId());
            argument.setUserId(user.getUserId());
            Optional<PersonnelDto> personnelInfo = personnelObjService.findByUserIdUseCacheChain(argument);
            return personnelInfo.map(PersonnelDto::getMcCurrency).orElse(null);
        } catch (Exception e) {
            log.warn("findPersonCurrencyCode failed");
            return "";
        }
    }

    public static List<String> initCurrencyList() {
        List<Map<String, String>> currencyList = AppFrameworkConfig.getCurrencyList();
        if (CollectionUtils.empty(currencyList)) {
            return Lists.newArrayList();
        }
        return currencyList.stream().map(x -> x.get("currencyCode")).collect(Collectors.toList());
    }

    public static Map<String, String> initCurrencyCodeAndSymbol() {
        List<Map<String, String>> currencyList = AppFrameworkConfig.getCurrencyList();
        if (CollectionUtils.empty(currencyList)) {
            return Maps.newHashMap();
        }
        return currencyList.stream().collect(Collectors.toMap(x -> x.get("currencyCode"), x -> x.get("currencySymbol")));
    }

    @Override
    @Transactional
    public void addCurrency(MtCurrency mtCurrency, User user) {
        String currencyCode = mtCurrency.getCurrencyCode();
        if (StringUtils.isBlank(currencyCode)) {
            log.warn("addCurrency error,currency param does not empty,user={}", user);
            throw new ValidateException(I18nMessage.of(I18NKey.MULTI_CURRENCY_PARAM_ERROR, I18N.text(I18NKey.MULTI_CURRENCY_PARAM_ERROR)));
        }
        List<String> initCurrencyList = initCurrencyList();
        if (!Objects.equals(mtCurrency.getCurrencyType(), MtCurrency.CURRENCY_CUSTOM)) {
            if (initCurrencyList.stream().noneMatch(x -> x.equals(currencyCode))) {
                log.warn("addCurrency error,not exist currency,user={},currencyCode={}", user, currencyCode);
                throw new ValidateException(I18nMessage.of(I18NKey.CURRENCY_SYSTEM_NOT_EXIST, I18NExt.getOrDefault(I18NKey.CURRENCY_SYSTEM_NOT_EXIST, "标准币种不存在")));// ignoreI18n
            }
        } else {
            if (initCurrencyList.stream().anyMatch(x -> x.equals(currencyCode))) {
                log.warn("addCurrency error,exist currency,user={},currencyCode={}", user, currencyCode);
                throw new ValidateException(I18nMessage.of(I18NKey.CURRENCY_SYSTEM_ALREADY_EXIST, I18NExt.getOrDefault(I18NKey.CURRENCY_SYSTEM_ALREADY_EXIST, "已存在标准币种，请选择标准币种")));// ignoreI18n
            }
        }
        MtCurrency functionalCurrency = findFunctionalCurrency(user);
        if (functionalCurrency.getCurrencyCode().equals(currencyCode)) {
            log.warn("addCurrency error,functional currency can not be add,user={}", user);
            throw new ValidateException(I18nMessage.of(I18NKey.FUNCTIONAL_CURRENCY_CANNOT_BE_OPERATOR, I18N.text(I18NKey.FUNCTIONAL_CURRENCY_CANNOT_BE_OPERATOR)));
        }
        Integer count = currencyService.findCurrencyCountByCode(user, currencyCode);
        if (count >= 1) {
            throw new ValidateException(I18nMessage.of(I18NKey.CANNOT_ADD_DUPLICATE_CURRENCY, I18N.text(I18NKey.CANNOT_ADD_DUPLICATE_CURRENCY)));
        }
        if (!Objects.equals(MtCurrency.CURRENCY_CUSTOM, mtCurrency.getCurrencyType())) {
            mtCurrency.setCurrencyLabel(MtCurrency.getI18NLabel(mtCurrency.getCurrencyCode()));
        }
        long time = System.currentTimeMillis();
        mtCurrency.setObjectDescribeApiName(MtCurrency.MT_CURRENCY_OBJ);
        mtCurrency.setTenantId(user.getTenantId());
        mtCurrency.setExchangeRateVersion(String.valueOf(time));
        mtCurrency.setIsFunctional(false);
        mtCurrency.setStatus(DELETE_STATUS.NORMAL.getValue());

        MtExchangeRate mtExchangeRate = MtExchangeRate.builder()
                .objectDescribeApiName(MtExchangeRate.MT_EXCHANGE_RATE_OBJ)
                .tenantId(user.getTenantId())
                .currencyCode(currencyCode)
                .exchangeRate(mtCurrency.getExchangeRate())
                .exchangeRateVersion(String.valueOf(time))
                .functionalCurrency(functionalCurrency.getCurrencyCode())
                .build();

        currencyService.batchUpsertCurrency(user, Lists.newArrayList(mtCurrency));
        List<MtCurrencyExchange> currencyExchanges = buildCurrencyExchange(Lists.newArrayList(mtExchangeRate), user, time, functionalCurrency.getCurrencyCode());
        updateCurrencyExchanges(user, currencyExchanges, functionalCurrency.getCurrencyCode());
        refreshCurrencyOptions(user);
    }

    @Override
    public void editCurrency(MtCurrency mtCurrency, User user) {
        MtCurrency dbData = currencyService.findCurrencyById(user, mtCurrency.getId());
        if (Objects.isNull(dbData)) {
            throw new ValidateException(I18nMessage.of(I18NKey.CURRENCY_NOT_EXIST, I18NExt.getOrDefault(I18NKey.CURRENCY_NOT_EXIST, "币种不存在,请添加币种")));// ignoreI18n
        }
        if (Objects.equals(MtCurrency.CURRENCY_CUSTOM, mtCurrency.getCurrencyType())) {
            dbData.setCurrencyLabel(mtCurrency.getCurrencyLabel());
        }
        dbData.setCurrencyPrefix(mtCurrency.getCurrencyPrefix());
        dbData.setCurrencySuffix(mtCurrency.getCurrencySuffix());
        dbData.setCurrencySymbol(mtCurrency.getCurrencySymbol());
        dbData.setCurrencyUnit(mtCurrency.getCurrencyUnit());
        dbData.setLastModifiedBy(user.getUserId());
        dbData.setLastModifiedTime(System.currentTimeMillis());
        currencyService.batchUpsertCurrency(user, Lists.newArrayList(dbData));
        syncCurrencyLabelToTranslatePlatForm(Lists.newArrayList(dbData), user);
    }

    private void syncCurrencyLabelToTranslatePlatForm(List<MtCurrency> currencyList, User user) {
        if (CollectionUtils.empty(currencyList)) {
            return;
        }
        I18nClient i18nClient = I18nClient.getInstance();
        //保存字段
        List<Localization> lcs = Lists.newArrayList();
        currencyList.forEach(currency -> {
            Localization lc = Localization.builder()
                    .key(MtCurrency.getCurrencyI18NKey(currency.getCurrencyCode()))
                    .tags(Lists.newArrayList("custom_object"))
                    .tenantId(Long.parseLong(user.getTenantId()))
                    .build();
            i18nClient.build(lc, RequestContextManager.getContext().getLang().getValue(), currency.getCurrencyLabel());
            lcs.add(lc);
        });
        log.info("syncCurrencyLabelToTranslatePlatForm,tenantId:{},userId:{},result:{}", user.getTenantId(), user.getUserId(), JSON.toJSONString(lcs));
        i18nClient.save4Translate(Long.parseLong(user.getTenantId()), lcs, false);
    }


    @Override
    public void disableCurrency(String currencyCode, User user) {
        if (StringUtils.isBlank(currencyCode)) {
            log.warn("disableCurrency error,currency param do not empty,user={}", user);
            throw new ValidateException(I18nMessage.of(I18NKey.MULTI_CURRENCY_PARAM_ERROR, I18N.text(I18NKey.MULTI_CURRENCY_PARAM_ERROR)));
        }
        MtCurrency functionalCurrency = findFunctionalCurrency(user);
        if (functionalCurrency.getCurrencyCode().equals(currencyCode)) {
            log.warn("functional currency cannot be operator,user={}", user);
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.FUNCTIONAL_CURRENCY_CANNOT_BE_OPERATOR, I18N.text(I18NKey.FUNCTIONAL_CURRENCY_CANNOT_BE_OPERATOR)));
        }
        MtCurrency currency = currencyService.queryCurrencyByCode(user, currencyCode);
        if (DELETE_STATUS.INVALID.getValue() == currency.getStatus()) {
            return;
        }
        currency.setStatus(DELETE_STATUS.INVALID.getValue());
        currency.setLastModifiedBy(user.getUserId());
        currency.setLastModifiedTime(System.currentTimeMillis());
        currencyService.batchUpsertCurrency(user, Lists.newArrayList(currency));
        refreshCurrencyOptions(user);
    }

    @Override
    @Transactional
    public void enableCurrency(String currencyCode, String exchangeRate, User user) {
        if (StringUtils.isBlank(currencyCode)) {
            log.warn("enableCurrency error,currency param do not empty,user={}", user);
            throw new ValidateException(I18nMessage.of(I18NKey.MULTI_CURRENCY_PARAM_ERROR, I18N.text(I18NKey.MULTI_CURRENCY_PARAM_ERROR)));
        }
        MtCurrency functionalCurrency = findFunctionalCurrency(user);
        if (functionalCurrency.getCurrencyCode().equals(currencyCode)) {
            log.warn("functional currency cannot be operator,user={}", user);
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.FUNCTIONAL_CURRENCY_CANNOT_BE_OPERATOR, I18N.text(I18NKey.FUNCTIONAL_CURRENCY_CANNOT_BE_OPERATOR)));
        }
        MtCurrency mtCurrency = currencyService.queryCurrencyByCode(user, currencyCode);
        if (DELETE_STATUS.NORMAL.getValue() == mtCurrency.getStatus()) {
            return;
        }
        Long enableTime = System.currentTimeMillis();
        String exchangeRateVersion = String.valueOf(enableTime);
        mtCurrency.setStatus(DELETE_STATUS.NORMAL.getValue());
        mtCurrency.setExchangeRate(exchangeRate);
        mtCurrency.setExchangeRateVersion(exchangeRateVersion);
        mtCurrency.setLastModifiedBy(user.getUserId());
        mtCurrency.setLastModifiedTime(System.currentTimeMillis());

        MtExchangeRate mtExchangeRate = MtExchangeRate.builder()
                .objectDescribeApiName(MtExchangeRate.MT_EXCHANGE_RATE_OBJ)
                .tenantId(user.getTenantId())
                .currencyCode(currencyCode)
                .exchangeRate(exchangeRate)
                .exchangeRateVersion(exchangeRateVersion)
                .functionalCurrency(functionalCurrency.getCurrencyCode())
                .createBy(user.getUserId())
                .createTime(enableTime)
                .build();
        multiCurrencyLogicService.upsertMultiCurrency(user, Lists.newArrayList(mtCurrency), Lists.newArrayList(mtExchangeRate));
        refreshCurrencyOptions(user);
    }


    public void bulkRefreshCurrencyOptions(List<String> tenantIds) {
        if (CollectionUtils.empty(tenantIds)) {
            return;
        }
        for (String tenantId : tenantIds) {
            User user = User.systemUser(tenantId);
            refreshCurrencyOptions(user);
        }
    }

    public void refreshCurrencyOptions(User user) {
        List<String> currencyObjects = findCurrencyObjectApiNames(user);
        refreshCurrencyOptionsSkipValidate(user, currencyObjects, false);
    }

    private void refreshCurrencyOptionsSkipValidate(User user, List<String> currencyObjects, boolean skipValidate) {
        List<MtCurrency> currencyList = currencyService.queryCurrencyData(user);
        String functionalCurrency = currencyList.stream()
                .filter(x -> BooleanUtils.isTrue(x.getIsFunctional()))
                .findFirst().map(MtCurrency::getCurrencyCode).orElse(null);
        MultiCurrencyOpen.Arg arg = buildMultiCurrencyOpenArg(user, currencyList, currencyObjects, functionalCurrency, true);
        arg.setNotValid(skipValidate);
        ParallelUtils.createBackgroundTask().submit(() ->
                metadataMultiCurrencyService.refreshCurrencyOptions(arg)
        ).run();
    }


    @Override
    public MtCurrency findFunctionalCurrency(User user) {
        return currencyService.queryFunctionalCode(user);
    }

    @Override
    public Optional<String> findFunctionalCurrencyCode(User user) {
        String cacheKey = buildCacheKey(user);
        CacheContext cacheContext = CacheContext.getContext();
        String cache = cacheContext.getCache(cacheKey);
        if (StringUtils.isNotEmpty(cache)) {
            return Optional.of(cache);
        }
        // 判断是否缓存了空值
        if (cacheContext.containsCache(cacheKey)) {
            return Optional.empty();
        }
        String currencyCode = findFunctionalCurrency(user).getCurrencyCode();
        cacheContext.setCache(cacheKey, currencyCode);
        return Optional.ofNullable(currencyCode);
    }

    private String buildCacheKey(User user) {
        return "FUNCTIONAL_CURRENCY_CODE" + "_" + user.getTenantId();
    }

    @Override
    @Transactional
    public void updateFunctionalCurrency(User user, String functionalCode, boolean skipValidate) {
        List<String> currencyCodes = Lists.newArrayList();
        MtCurrency functional = findFunctionalCurrency(user);
        if (Objects.nonNull(functional)) {
            currencyCodes.add(functional.getCurrencyCode());
        }
        if (StringUtils.isNotBlank(functionalCode)) {
            currencyCodes.add(functionalCode);
        }
        multiCurrencyLogicService.deleteMultiCurrencyByCodes(user, Lists.newArrayList(currencyCodes));
        String exchangeRateVersion = String.valueOf(System.currentTimeMillis());
        MtCurrency funtionalMtCurrency = MtCurrency.getFuntionalMtCurrency(functionalCode, user, exchangeRateVersion);
        MtExchangeRate funcionalMtExchangeRate = MtExchangeRate.getFuncionalMtExchangeRate(functionalCode, user, exchangeRateVersion);
        multiCurrencyLogicService.upsertMultiCurrency(user, Lists.newArrayList(funtionalMtCurrency), Lists.newArrayList(funcionalMtExchangeRate));
        List<String> currencyObjects = findCurrencyObjectApiNames(user);
        refreshCurrencyOptionsSkipValidate(user, currencyObjects, skipValidate);
    }

    @Override
    public MtCurrency findCurrencyByCode(String currencyCode, User user) {
        return currencyService.queryCurrencyByCode(user, currencyCode);
    }

    @Override
    public List<MtCurrency> findCurrencyByCodes(User user, List<String> currencyCodes) {
        return currencyService.findCurrencyByCodes(user, currencyCodes);
    }

    @Override
    @Transactional
    public void batchModifyRate(List<MtExchangeRate> exchangeRateList, User user) {
        if (CollectionUtils.empty(exchangeRateList)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.MULTI_CURRENCY_PARAM_ERROR, I18N.text(I18NKey.MULTI_CURRENCY_PARAM_ERROR)));
        }
        List<MtCurrency> currencyFromDb = currencyService.queryCurrencyData(user);
        if (CollectionUtils.empty(currencyFromDb)) {
            log.warn("no any currency, user={}", user);
            return;
        }
        MtCurrency functionalCurrency = currencyFromDb.stream().filter(MtCurrency::getIsFunctional).findFirst().orElse(null);
        if (Objects.isNull(functionalCurrency)) {
            log.warn("functional currency does not exist, user={}", user);
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.FUNCTIONAL_CURRENCY_NOT_EXIST, I18N.text(I18NKey.FUNCTIONAL_CURRENCY_NOT_EXIST)));
        }
        //批量调整汇率去除本位币
        exchangeRateList.removeIf(x -> Objects.equals(functionalCurrency.getCurrencyCode(), x.getCurrencyCode()));
        //检查当前汇率是否发生变化
        List<MtCurrency> updateCurrencies = Lists.newArrayList();
        Long time = System.currentTimeMillis();
        boolean isChange = false;
        for (MtExchangeRate exchangeRate : exchangeRateList) {
            MtCurrency currency = currencyFromDb.stream()
                    .filter(x -> Objects.equals(x.getCurrencyCode(), exchangeRate.getCurrencyCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(currency)) {
                continue;
            }
            if (new BigDecimal(exchangeRate.getExchangeRate()).compareTo(new BigDecimal(currency.getExchangeRate())) != 0) {
                isChange = true;
                currency.setExchangeRate(exchangeRate.getExchangeRate());
            }
            currency.setExchangeRateVersion(String.valueOf(time));
            currency.setLastModifiedBy(user.getUserId());
            currency.setLastModifiedTime(time);
            updateCurrencies.add(currency);
        }
        if (BooleanUtils.isFalse(isChange)) {
            log.warn("no change for exchange rate!,user={}", user);
            return;
        }
        currencyService.batchUpsertCurrency(user, updateCurrencies);
        List<MtCurrencyExchange> addCurrencyExchanges = buildCurrencyExchange(exchangeRateList, user, time, functionalCurrency.getCurrencyCode());
        updateCurrencyExchanges(user, addCurrencyExchanges, functionalCurrency.getCurrencyCode());
    }

    public List<MtCurrencyExchange> buildCurrencyExchange(List<MtExchangeRate> exchangeRates, User user, Long time, String functionalCurrency) {
        List<MtCurrencyExchange> result = Lists.newArrayList();
        exchangeRates.forEach(x -> {
            MtCurrencyExchange currencyExchange = new MtCurrencyExchange();
            currencyExchange.setFromCurrencyCode(x.getCurrencyCode());
            currencyExchange.setExchangeRate(x.getExchangeRate());
            currencyExchange.setToCurrencyCode(functionalCurrency);
            currencyExchange.setExchangeRateVersion(String.valueOf(time));
            currencyExchange.setObjectDescribeApiName(MtCurrencyExchange.MT_CURRENCY_EXCHANGE_OBJ);
            currencyExchange.setTenantId(user.getTenantId());
            currencyExchange.setLastModifiedBy(user.getUserId());
            currencyExchange.setLastModifiedTime(time);
            result.add(currencyExchange);
        });
        return result;
    }

    @Override
    public QueryResult<MtExchangeRate> queryRate(String currencyCode, Long startTime, Long endTime, Integer pageSize, Integer pageNumber, User user) {
        QueryExchangeRate.Arg arg = QueryExchangeRate.Arg.builder()
                .currencyCode(currencyCode)
                .startTime(startTime)
                .endTime(endTime)
                .pageSize(pageSize)
                .pageNumber(pageNumber)
                .build();
        QueryResult<IObjectData> queryResult = exchangeRateService.queryExchangeRateData(user, arg);
        List<MtExchangeRate> exchangeRates = Lists.newArrayList();
        queryResult.getData().forEach(x -> {
            MtExchangeRate exchangeRate = MtExchangeRate.buildMtExchangeRate(x);
            exchangeRates.add(exchangeRate);
        });
        QueryResult<MtExchangeRate> currencyQueryResult = new QueryResult<>();
        currencyQueryResult.setData(exchangeRates);
        currencyQueryResult.setTotalNumber(queryResult.getTotalNumber());
        return currencyQueryResult;
    }

    @Override
    public MtCurrencyExchange findCurrencyExchange(User user, String fromCurrencyCode, String toCurrencyCode) {
        if (Strings.isNullOrEmpty(fromCurrencyCode) || Strings.isNullOrEmpty(toCurrencyCode)) {
            return null;
        }
        Query query = buildSearchQuery(user, 1);
        query.and(FilterExt.of(Operator.EQ, MtCurrencyExchange.CURRENCY_FROM_CURRENCY_CODE, fromCurrencyCode).getFilter());
        query.and(FilterExt.of(Operator.EQ, MtCurrencyExchange.CURRENCY_TO_CURRENCY_CODE, toCurrencyCode).getFilter());
        List<MtCurrencyExchange> currencyExchanges = currencyExchangeRepository.findBy(user, query, MtCurrencyExchange.class);
        return currencyExchanges.stream().findFirst().orElse(null);
    }

    public List<MtCurrencyExchange> findCurrencyExchangeList(User user, String toCurrencyCode) {
        Query query = buildSearchQuery(user, 2000);
        query.and(FilterExt.of(Operator.EQ, MtCurrencyExchange.CURRENCY_TO_CURRENCY_CODE, toCurrencyCode).getFilter());
        List<MtCurrencyExchange> currencyExchanges = currencyExchangeRepository.findBy(user, query, MtCurrencyExchange.class);
        if (CollectionUtils.empty(currencyExchanges)) {
            return Lists.newArrayList();
        }
        return currencyExchanges;
    }

    @Transactional
    public void updateCurrencyExchanges(User user, List<MtCurrencyExchange> exchanges, String toCurrencyCode) {
        if (CollectionUtils.empty(exchanges)) {
            return;
        }
        List<MtCurrencyExchange> currencyExchangeResult = Lists.newArrayList();
        List<MtCurrencyExchange> currencyExchangeList = findCurrencyExchangeList(user, toCurrencyCode);

        //删除
        List<String> removeCurrencyCodes = exchanges.stream()
                .filter(x -> StringUtils.isBlank(x.getExchangeRate()))
                .map(x -> x.getFromCurrencyCode())
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(currencyExchangeList) && CollectionUtils.notEmpty(removeCurrencyCodes)) {
            List<MtCurrencyExchange> realRemoveCurrencyExchanges = currencyExchangeList.stream()
                    .filter(x -> removeCurrencyCodes.contains(x.getFromCurrencyCode()))
                    .collect(Collectors.toList());
            currencyExchangeRepository.bulkInvalidAndDelete(user, realRemoveCurrencyExchanges);
        }

        List<String> addOrUpdateCurrencyCodes = exchanges.stream()
                .filter(x -> StringUtils.isNotBlank(x.getExchangeRate()))
                .map(x -> x.getFromCurrencyCode())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(addOrUpdateCurrencyCodes)) {
            return;
        }
        //更新
        List<MtCurrencyExchange> realUpdateCurrencyExchanges = Lists.newArrayList();
        currencyExchangeList.stream()
                .filter(x -> addOrUpdateCurrencyCodes.contains(x.getFromCurrencyCode()))
                .forEach(dbExchange -> {
                    MtCurrencyExchange exchange = exchanges.stream()
                            .filter(x -> Objects.equals(x.getFromCurrencyCode(), dbExchange.getFromCurrencyCode()))
                            .findFirst().orElse(null);
                    if (Objects.isNull(exchange)) {
                        return;
                    }
                    dbExchange.setExchangeRate(exchange.getExchangeRate());
                    realUpdateCurrencyExchanges.add(dbExchange);
                });
        if (CollectionUtils.notEmpty(realUpdateCurrencyExchanges)) {
            List<MtCurrencyExchange> upsertCurrencyExchanges = buildCurrencyExchange(realUpdateCurrencyExchanges, user);
            List<MtCurrencyExchange> updateResult = currencyExchangeRepository.bulkUpsert(user, upsertCurrencyExchanges);
            currencyExchangeResult.addAll(updateResult);
        }
        List<String> realUpdateCurrencyCodes = realUpdateCurrencyExchanges.stream()
                .map(x -> x.getFromCurrencyCode())
                .collect(Collectors.toList());
        //新建
        List<MtCurrencyExchange> addCurrencyExchanges = exchanges.stream()
                .filter(x -> StringUtils.isNotBlank(x.getExchangeRate()))
                .filter(x -> !realUpdateCurrencyCodes.contains(x.getFromCurrencyCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(addCurrencyExchanges)) {
            List<MtCurrencyExchange> upsertCurrencyExchanges = buildCurrencyExchange(addCurrencyExchanges, user);
            List<MtCurrencyExchange> addResult = currencyExchangeRepository.bulkCreate(user, upsertCurrencyExchanges);
            currencyExchangeResult.addAll(addResult);
        }

        //历史汇率
        List<MtExchangeRate> exchangeRates = buildExchangeRate(currencyExchangeResult, user);
        exchangeRateService.batchInsertExchangeRate(user, exchangeRates);
    }

    private List<MtExchangeRate> buildExchangeRate(List<MtCurrencyExchange> exchanges, User user) {
        List<MtExchangeRate> exchangeRates = Lists.newArrayList();
        exchanges.forEach(x -> {
            MtExchangeRate exchangeRate = new MtExchangeRate();
            exchangeRate.setCurrencyCode(x.getFromCurrencyCode());
            exchangeRate.setToCurrencyCode(x.getToCurrencyCode());
            exchangeRate.setExchangeRate(x.getExchangeRate());
            exchangeRate.setExchangeRateVersion(x.getExchangeRateVersion());
            exchangeRate.setObjectDescribeApiName(MtExchangeRate.MT_EXCHANGE_RATE_OBJ);
            exchangeRate.setTenantId(user.getTenantId());
            exchangeRates.add(exchangeRate);
        });
        return exchangeRates;
    }

    public List<MtCurrencyExchange> buildCurrencyExchange(List<MtCurrencyExchange> exchanges, User user) {
        List<MtCurrencyExchange> currencyExchanges = Lists.newArrayList();
        Long time = System.currentTimeMillis();
        exchanges.forEach(x -> {
            MtCurrencyExchange currencyExchange = new MtCurrencyExchange();
            if (StringUtils.isNotBlank(x.getId())) {
                currencyExchange.setId(x.getId());
            }
            currencyExchange.setFromCurrencyCode(x.getFromCurrencyCode());
            currencyExchange.setToCurrencyCode(x.getToCurrencyCode());
            currencyExchange.setExchangeRate(x.getExchangeRate());
            currencyExchange.setExchangeRateVersion(String.valueOf(time));
            currencyExchange.setTenantId(user.getTenantId());
            currencyExchange.setObjectDescribeApiName(MtCurrencyExchange.MT_CURRENCY_EXCHANGE_OBJ);
            currencyExchange.setLastModifiedBy(user.getUserId());
            currencyExchange.setLastModifiedTime(time);
            currencyExchanges.add(currencyExchange);
        });
        return currencyExchanges;
    }

    @Override
    public List<MtCurrencyExchange> findAllCurrencyExchanges(User user) {
        List<MtCurrencyExchange> allCurrencyExchanges = Lists.newArrayList();
        Query query = buildSearchQuery(user, 1);
        Integer countOnly = currencyExchangeRepository.findCountOnly(user, query, MtCurrencyExchange.class);
        int totalPage = SearchTemplateQueryExt.calculateTotalPage(countOnly, 200);
        for (int i = 1; i <= totalPage; i++) {
            int offset = SearchTemplateQueryExt.calculateOffset(i, 200);
            Query searchQuery = buildSearchQuery(user, 200);
            searchQuery.setOffset(offset);
            searchQuery.addOrders(OrderByExt.orderByField(IObjectData.ID, true));
            List<MtCurrencyExchange> currencyExchanges = currencyExchangeRepository.findBy(user, searchQuery, MtCurrencyExchange.class);
            if (CollectionUtils.notEmpty(currencyExchanges)) {
                allCurrencyExchanges.addAll(currencyExchanges);
            }
        }
        return allCurrencyExchanges;
    }

    private Query buildSearchQuery(User user, int limit) {
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        filters.add(FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, MtCurrencyExchange.MT_CURRENCY_EXCHANGE_OBJ).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return Query.builder().limit(limit).searchQuery(searchQuery).build();
    }

    @Override
    public List<IFieldDescribe> directFillMultiCurrencyFields(User user, String describeApiName) throws MetadataServiceException {
        List<IFieldDescribe> multiCurrencyFields = Lists.newArrayList();
        Integer multiCurrencyStatus = this.findMultiCurrencyStatus(user);
        if (MultiCurrencyStatus.OPEN_SUCCESS.getStatus().equals(multiCurrencyStatus)) {
            List<MtCurrency> mtCurrencyList = this.findCurrencyList(user);
            Optional<MtCurrency> functionalCurrency = mtCurrencyList.stream().filter(MtCurrency::getIsFunctional).findFirst();
            List<Map<String, Object>> optionList = Lists.newArrayList();
            List<Map<String, Object>> functionalCurrencyList = Lists.newArrayList();
            if (functionalCurrency.isPresent()) {
                Map<String, Object> functionalCurrencyMap = Maps.newHashMap();
                functionalCurrencyMap.put(ISelectOption.OPTION_LABEL, MtCurrency.getI18NLabel(functionalCurrency.get().getCurrencyCode()));
                functionalCurrencyMap.put(ISelectOption.NOT_USABLE, false);
                functionalCurrencyMap.put(ISelectOption.OPTION_VALUE, functionalCurrency.get().getCurrencyCode());
                functionalCurrencyList.add(functionalCurrencyMap);
            } else {
                throw new MetaDataBusinessException(I18nMessage.ofI8nKey(I18NKey.FUNCTIONAL_CURRENCY_NOT_EXIST));
            }
            mtCurrencyList.forEach(x -> {
                Map<String, Object> optionMap = Maps.newHashMap();
                optionMap.put(ISelectOption.OPTION_LABEL, MtCurrency.getI18NLabel(x.getCurrencyCode()));
                optionMap.put(ISelectOption.NOT_USABLE, !Objects.equals(DELETE_STATUS.NORMAL.getValue(), x.getStatus()));
                optionMap.put(ISelectOption.OPTION_VALUE, x.getCurrencyCode());
                optionList.add(optionMap);
            });
            String optionJson = JSON.toJSONString(optionList);
            String functionalJson = JSON.toJSONString(functionalCurrencyList);
            List<IFieldDescribe> multiCurrencyFieldList = metadataMultiCurrencyService.generateMultiCurrencyFields(describeApiName, optionJson, functionalJson, RequestUtil.getCurrentLang().getValue());
            multiCurrencyFields.addAll(multiCurrencyFieldList);
        }
        return multiCurrencyFields;
    }

    @Override
    public void fillMultiCurrencyField(User user, IObjectDescribe objectDescribe) throws MetadataServiceException {
        //预设对象和大对象不处理多币种字段
        fillMultiCurrencyField(user, objectDescribe, (IObjectDescribe) null);
    }

    @Override
    public void fillMultiCurrencyField(User user, IObjectDescribe objectDescribe, IObjectDescribe objectDescribeInDB) throws MetadataServiceException {
        //预设对象和大对象不处理多币种字段
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        // 原始逻辑检查 !isCustomObject() 新逻辑预制对象也需要支持
        if (skipMultiCurrencyProcess(user, describeExt)) {
            return;
        }
        // 校验多币种字段禁用时的业务规则
        validateMultiCurrencyFieldsDisabled(describeExt, objectDescribe.getFieldDescribes());
        if (describeExt.containsMultiCurrencyField()) {
            if (Objects.isNull(objectDescribeInDB)) {
                masterAndDetailSyncMultiCurrencyFields(user, objectDescribe, false);
            } else {
                if (ObjectDescribeExt.of(objectDescribe).isSlaveObject() && !ObjectDescribeExt.of(objectDescribeInDB).isSlaveObject()) {
                    masterAndDetailSyncMultiCurrencyFields(user, objectDescribe, false);
                }
            }
            return;
        }
        List<IFieldDescribe> currencyFields = describeExt.getFieldByRenderTypes(Sets.newHashSet(IFieldType.CURRENCY));
        if (CollectionUtils.empty(currencyFields)) {
            return;
        }
        masterAndDetailSyncMultiCurrencyFields(user, objectDescribe);
    }

    @Override
    public void fillMultiCurrencyField(User user, IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribes) throws MetadataServiceException {
        if (CollectionUtils.empty(fieldDescribes)) {
            return;
        }
        //大对象不处理多币种字段
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (skipMultiCurrencyProcess(user, describeExt)) {
            return;
        }
        boolean containsMultiCurrencyField = describeExt.containsMultiCurrencyField();
        boolean hasCurrencyField = fieldDescribes.stream()
                .anyMatch(field -> IFieldType.CURRENCY.equals(FieldDescribeExt.of(field).getTypeOrReturnType()));
        boolean hasMasterDetailField = fieldDescribes.stream().anyMatch(field -> FieldDescribeExt.of(field).isMasterDetailField());
        if (containsMultiCurrencyField && hasMasterDetailField) {
            masterAndDetailSyncMultiCurrencyFields(user, objectDescribe, false);
            return;
        }
        if (!hasCurrencyField) {
            return;
        }
        // 校验多币种字段禁用时的业务规则
        validateMultiCurrencyFieldsDisabled(describeExt, fieldDescribes);
        if (containsMultiCurrencyField) {
            return;
        }
        List<IFieldDescribe> multiCurrencyFields = directFillMultiCurrencyFields(user, objectDescribe.getApiName());
        fieldDescribes.addAll(multiCurrencyFields);
        masterAndDetailSyncMultiCurrencyFields(user, objectDescribe, false);
    }

    private boolean skipMultiCurrencyProcess(User user, ObjectDescribeExt describeExt) {
        Integer multiCurrencyStatus = this.findMultiCurrencyStatus(user);
        if (describeExt.isBigObject() || !MultiCurrencyStatus.OPEN_SUCCESS.getStatus().equals(multiCurrencyStatus)) {
            return true;
        }
        return false;
    }

    private void masterAndDetailSyncMultiCurrencyFields(User user, IObjectDescribe objectDescribe) throws MetadataServiceException {
        masterAndDetailSyncMultiCurrencyFields(user, objectDescribe, true);
    }

    private void masterAndDetailSyncMultiCurrencyFields(User user, IObjectDescribe objectDescribe, boolean containCurrentObj) throws MetadataServiceException {
        if (containCurrentObj) {
            List<IFieldDescribe> multiCurrencyFields = directFillMultiCurrencyFields(user, objectDescribe.getApiName());
            objectDescribe.addFieldDescribeList(multiCurrencyFields);
        } 
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (describeExt.isSlaveObject()) {
            Optional<MasterDetailFieldDescribe> masterDetaiOptional = describeExt.getMasterDetailFieldDescribe();
            if (!masterDetaiOptional.isPresent()) {
                return;
            }
            String targetApiName = masterDetaiOptional.get().getTargetApiName();
            List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(user.getTenantId(), targetApiName);
            IObjectDescribe masterDescribe = describeLogicService.findObject(user.getTenantId(), targetApiName);
            detailDescribes.add(masterDescribe);
            updateDetailObjectsMultiCurrencyFields(user, detailDescribes, describeExt.getApiName());
        } else {
            List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());
            updateDetailObjectsMultiCurrencyFields(user, detailDescribes, null);
        }
    }


    /**
     * 更新详情对象的多币种字段
     * @param user 用户信息
     * @param describeList 详情对象列表
     * @param excludeApiName 需要排除的对象API名称，如果为null则不排除任何对象
     */
    private void updateDetailObjectsMultiCurrencyFields(User user, List<IObjectDescribe> describeList, String excludeApiName) throws MetadataServiceException {
        for (IObjectDescribe describe : describeList) {
            // 如果指定了需要排除的API名称，则排除该对象
            if (StringUtils.equals(describe.getApiName(), excludeApiName)) {
                continue;
            }
            // 过滤掉已经包含多币种字段的对象
            if (ObjectDescribeExt.of(describe).containsMultiCurrencyField()) {
                continue;
            }
            List<IFieldDescribe> fillMultiCurrencyFields = directFillMultiCurrencyFields(user, describe.getApiName());
            if (CollectionUtils.empty(fillMultiCurrencyFields)) {
                return;
            }
            describe.addFieldDescribeList(fillMultiCurrencyFields);
            describeLogicService.update(user, describe);
        }
    }

    /**
     * 校验多币种字段禁用时的业务规则
     *
     * @param describeExt    对象描述扩展
     * @param fieldDescribes 要校验的字段列表
     * @throws ValidateException 当存在非本位币字段时抛出异常
     */
    private void validateMultiCurrencyFieldsDisabled(ObjectDescribeExt describeExt, List<IFieldDescribe> fieldDescribes) {
        if (!describeExt.multiCurrencyFieldsDisabled()) {
            return;
        }
        // 检查是否有金额字段的"是否本位币"设置为"否"（即 currencyType 为 "oc"）
        List<String> nonBaseCurrencyFieldLabels = fieldDescribes.stream()
                .filter(field -> IFieldType.CURRENCY.equals(field.getType()))
                .filter(field -> MtCurrency.ORIGINAL_CURRENCY.equals(field.getCurrencyType()))
                .map(IFieldDescribe::getLabel)
                .collect(Collectors.toList());
        if (!nonBaseCurrencyFieldLabels.isEmpty()) {
            String fieldNames = String.join(",", nonBaseCurrencyFieldLabels);
            throw new ValidateException(I18nMessage.of(I18NKey.MULTI_CURRENCY_FIELDS_DISABLED_NON_BASE_CURRENCY,
                    I18N.text(I18NKey.MULTI_CURRENCY_FIELDS_DISABLED_NON_BASE_CURRENCY, fieldNames),
                    fieldNames));
        }
    }
}
