package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static com.facishare.crm.openapi.Utils.UNIT_INFO_API_NAME;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class UnitInfoObjectImportProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IUniqueRule uniqueRule;

    private UnitInfoObjectImportProvider unitInfoObjectImportProvider;

    @BeforeEach
    void setUp() {
        unitInfoObjectImportProvider = new UnitInfoObjectImportProvider();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的单位信息API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的单位信息API名称")
    void testGetObjectCode_返回正确的单位信息API名称() {
        // 执行被测试方法
        String result = unitInfoObjectImportProvider.getObjectCode();

        // 验证结果
        assertEquals(UNIT_INFO_API_NAME, result);
        assertEquals("UnitInfoObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportObject方法始终返回空Optional，因为单位不支持导入
     */
    @Test
    @DisplayName("getImportObject - 始终返回空Optional因为单位不支持导入")
    void testGetImportObject_始终返回空Optional因为单位不支持导入() {
        // 执行被测试方法
        Optional<ImportObject> result = unitInfoObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

        // 验证结果
        assertFalse(result.isPresent());
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，getImportObject方法仍返回空Optional
     */
    @Test
    @DisplayName("getImportObject - objectDescribe为null时仍返回空Optional")
    void testGetImportObject_objectDescribe为null时仍返回空Optional() {
        // 执行被测试方法
        Optional<ImportObject> result = unitInfoObjectImportProvider.getImportObject(null, uniqueRule);

        // 验证结果
        assertFalse(result.isPresent());
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当uniqueRule为null时，getImportObject方法仍返回空Optional
     */
    @Test
    @DisplayName("getImportObject - uniqueRule为null时仍返回空Optional")
    void testGetImportObject_uniqueRule为null时仍返回空Optional() {
        // 执行被测试方法
        Optional<ImportObject> result = unitInfoObjectImportProvider.getImportObject(objectDescribe, null);

        // 验证结果
        assertFalse(result.isPresent());
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当所有参数为null时，getImportObject方法仍返回空Optional
     */
    @Test
    @DisplayName("getImportObject - 所有参数为null时仍返回空Optional")
    void testGetImportObject_所有参数为null时仍返回空Optional() {
        // 执行被测试方法
        Optional<ImportObject> result = unitInfoObjectImportProvider.getImportObject(null, null);

        // 验证结果
        assertFalse(result.isPresent());
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试单位对象的业务特性 - 不支持任何形式的导入
     */
    @Test
    @DisplayName("业务特性 - 单位对象不支持任何形式的导入")
    void test业务特性_单位对象不支持任何形式的导入() {
        // 执行被测试方法
        String objectCode = unitInfoObjectImportProvider.getObjectCode();
        Optional<ImportObject> importObject1 = unitInfoObjectImportProvider.getImportObject(objectDescribe, uniqueRule);
        Optional<ImportObject> importObject2 = unitInfoObjectImportProvider.getImportObject(null, null);

        // 验证业务特性
        assertEquals(UNIT_INFO_API_NAME, objectCode);
        assertFalse(importObject1.isPresent());
        assertFalse(importObject2.isPresent());

        // 验证不支持导入的特性
        assertTrue(importObject1.isEmpty());
        assertTrue(importObject2.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多次调用getImportObject方法的一致性
     */
    @Test
    @DisplayName("方法一致性 - 多次调用getImportObject方法结果一致")
    void test方法一致性_多次调用getImportObject方法结果一致() {
        // 多次执行被测试方法
        Optional<ImportObject> result1 = unitInfoObjectImportProvider.getImportObject(objectDescribe, uniqueRule);
        Optional<ImportObject> result2 = unitInfoObjectImportProvider.getImportObject(objectDescribe, uniqueRule);
        Optional<ImportObject> result3 = unitInfoObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

        // 验证结果一致性
        assertFalse(result1.isPresent());
        assertFalse(result2.isPresent());
        assertFalse(result3.isPresent());

        assertEquals(result1.isPresent(), result2.isPresent());
        assertEquals(result2.isPresent(), result3.isPresent());
        assertEquals(result1.isEmpty(), result2.isEmpty());
        assertEquals(result2.isEmpty(), result3.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportObject方法与父类行为的差异
     */
    @Test
    @DisplayName("父类差异 - getImportObject方法覆盖父类行为")
    void test父类差异_getImportObject方法覆盖父类行为() {
        // 执行被测试方法
        Optional<ImportObject> result = unitInfoObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

        // 验证结果
        assertFalse(result.isPresent());
        assertTrue(result.isEmpty());

        // 验证覆盖了父类的默认行为
        // 父类DefaultObjectImportProvider的getImportObject方法通常会返回有值的Optional
        // 但UnitInfoObjectImportProvider明确覆盖为返回空Optional
        assertNotNull(result);
        assertEquals(Optional.empty(), result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试单位对象提供者的完整功能验证
     */
    @Test
    @DisplayName("完整功能 - 单位对象提供者的所有功能验证")
    void test完整功能_单位对象提供者的所有功能验证() {
        // 测试getObjectCode
        String objectCode = unitInfoObjectImportProvider.getObjectCode();
        assertEquals(UNIT_INFO_API_NAME, objectCode);
        assertEquals("UnitInfoObj", objectCode);

        // 测试getImportObject在各种情况下的行为
        Optional<ImportObject> result1 = unitInfoObjectImportProvider.getImportObject(objectDescribe, uniqueRule);
        Optional<ImportObject> result2 = unitInfoObjectImportProvider.getImportObject(null, uniqueRule);
        Optional<ImportObject> result3 = unitInfoObjectImportProvider.getImportObject(objectDescribe, null);
        Optional<ImportObject> result4 = unitInfoObjectImportProvider.getImportObject(null, null);

        // 验证所有情况下都不支持导入
        assertFalse(result1.isPresent());
        assertFalse(result2.isPresent());
        assertFalse(result3.isPresent());
        assertFalse(result4.isPresent());

        // 验证返回的都是空Optional
        assertEquals(Optional.empty(), result1);
        assertEquals(Optional.empty(), result2);
        assertEquals(Optional.empty(), result3);
        assertEquals(Optional.empty(), result4);
    }
}
