package com.facishare.paas.appframework.metadata.util;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WebFileUtil 测试类
 * 主要测试文件扩展名处理逻辑和文件上传功能
 */
@ExtendWith(MockitoExtension.class)
class WebFileUtilTest {

    @Mock
    private StoneProxyApi stoneProxyApi;

    @InjectMocks
    private WebFileUtil webFileUtil;

    private User testUser;
    private File testFile;

    @BeforeEach
    void setUp() throws IOException {
        testUser = new User("test-tenant", "12345");

        // 创建临时测试文件
        testFile = File.createTempFile("test", ".xlsx");
        testFile.deleteOnExit();
    }

    @Test
    @DisplayName("测试路径已包含正确扩展名的情况")
    void testEnsureFileExtension_PathAlreadyHasCorrectExtension() {
        // 测试路径已经包含正确的扩展名
        String result = invokeEnsureFileExtension("N_12345.xlsx", "xlsx");
        assertEquals("N_12345.xlsx", result);
        
        // 测试大小写不敏感
        result = invokeEnsureFileExtension("N_12345.XLSX", "xlsx");
        assertEquals("N_12345.XLSX", result);
    }

    @Test
    @DisplayName("测试路径缺少扩展名的情况")
    void testEnsureFileExtension_PathMissingExtension() {
        // 测试路径没有扩展名，需要添加
        String result = invokeEnsureFileExtension("N_12345", "xlsx");
        assertEquals("N_12345.xlsx", result);
        
        // 测试其他文件类型
        result = invokeEnsureFileExtension("N_67890", "pdf");
        assertEquals("N_67890.pdf", result);
    }

    @Test
    @DisplayName("测试路径包含其他扩展名的情况")
    void testEnsureFileExtension_PathHasDifferentExtension() {
        // 根据简化后的逻辑，会直接添加期望的扩展名
        String result = invokeEnsureFileExtension("N_12345.pdf", "xlsx");
        assertEquals("N_12345.pdf.xlsx", result);

        result = invokeEnsureFileExtension("N_12345.doc", "xlsx");
        assertEquals("N_12345.doc.xlsx", result);
    }

    @Test
    @DisplayName("测试空值和边界情况")
    void testEnsureFileExtension_EdgeCases() {
        // 测试空路径
        String result = invokeEnsureFileExtension(null, "xlsx");
        assertNull(result);
        
        result = invokeEnsureFileExtension("", "xlsx");
        assertEquals("", result);
        
        // 测试空扩展名
        result = invokeEnsureFileExtension("N_12345", null);
        assertEquals("N_12345", result);
        
        result = invokeEnsureFileExtension("N_12345", "");
        assertEquals("N_12345", result);
    }

    @Test
    @DisplayName("测试复杂路径格式")
    void testEnsureFileExtension_ComplexPaths() {
        // 测试包含路径分隔符的情况
        String result = invokeEnsureFileExtension("folder/N_12345", "xlsx");
        assertEquals("folder/N_12345.xlsx", result);
        
        // 测试包含多个点的情况
        result = invokeEnsureFileExtension("file.name.without.ext", "xlsx");
        assertEquals("file.name.without.ext.xlsx", result);
        
        // 测试已有正确扩展名但包含多个点
        result = invokeEnsureFileExtension("file.name.with.xlsx", "xlsx");
        assertEquals("file.name.with.xlsx", result);
    }

    @Test
    @DisplayName("测试 updateFileDirectFromFile 方法 - Stone SDK 返回路径包含扩展名")
    void testUpdateFileDirectFromFile_PathWithExtension() throws Exception {
        // 准备测试数据
        String ea = "test-ea";
        int expiredDay = 7;
        String ext = "xlsx";
        String responsePath = "N_12345.xlsx";
        long fileSize = 1024L;

        // Mock Stone SDK 响应
        StoneFileUploadResponse mockResponse = mock(StoneFileUploadResponse.class);
        when(mockResponse.getPath()).thenReturn(responsePath);
        when(mockResponse.getSize()).thenReturn(fileSize);
        when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(FileInputStream.class)))
                .thenReturn(mockResponse);

        // 执行测试
        NUploadFileDirect.Result result = webFileUtil.updateFileDirectFromFile(ea, testUser, testFile, expiredDay, ext);

        // 验证结果
        assertNotNull(result);
        assertEquals(responsePath, result.getFinalNPath()); // 路径已有扩展名，应保持不变
        assertEquals(fileSize, result.getFileSize());

        // 验证 Stone SDK 调用
        verify(stoneProxyApi).uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(FileInputStream.class));
    }

    @Test
    @DisplayName("测试 updateFileDirectFromFile 方法 - Stone SDK 返回路径缺少扩展名")
    void testUpdateFileDirectFromFile_PathMissingExtension() throws Exception {
        // 准备测试数据
        String ea = "test-ea";
        int expiredDay = 7;
        String ext = "xlsx";
        String responsePath = "N_12345"; // 缺少扩展名
        String expectedPath = "N_12345.xlsx"; // 期望补充扩展名
        long fileSize = 1024L;

        // Mock Stone SDK 响应
        StoneFileUploadResponse mockResponse = mock(StoneFileUploadResponse.class);
        when(mockResponse.getPath()).thenReturn(responsePath);
        when(mockResponse.getSize()).thenReturn(fileSize);
        when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(FileInputStream.class)))
                .thenReturn(mockResponse);

        // 执行测试
        NUploadFileDirect.Result result = webFileUtil.updateFileDirectFromFile(ea, testUser, testFile, expiredDay, ext);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPath, result.getFinalNPath()); // 应该补充扩展名
        assertEquals(fileSize, result.getFileSize());

        // 验证 Stone SDK 调用
        verify(stoneProxyApi).uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(FileInputStream.class));
    }

    @Test
    @DisplayName("测试 updateFileDirectFromFile 方法 - Stone SDK 返回路径有其他扩展名")
    void testUpdateFileDirectFromFile_PathWithDifferentExtension() throws Exception {
        // 准备测试数据
        String ea = "test-ea";
        int expiredDay = 7;
        String ext = "xlsx";
        String responsePath = "N_12345.pdf"; // 有其他扩展名
        String expectedPath = "N_12345.pdf.xlsx"; // 简单添加期望扩展名
        long fileSize = 1024L;

        // Mock Stone SDK 响应
        StoneFileUploadResponse mockResponse = mock(StoneFileUploadResponse.class);
        when(mockResponse.getPath()).thenReturn(responsePath);
        when(mockResponse.getSize()).thenReturn(fileSize);
        when(stoneProxyApi.uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(FileInputStream.class)))
                .thenReturn(mockResponse);

        // 执行测试
        NUploadFileDirect.Result result = webFileUtil.updateFileDirectFromFile(ea, testUser, testFile, expiredDay, ext);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPath, result.getFinalNPath()); // 应该添加期望扩展名
        assertEquals(fileSize, result.getFileSize());

        // 验证 Stone SDK 调用
        verify(stoneProxyApi).uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(FileInputStream.class));
    }

    /**
     * 使用反射调用私有方法 ensureFileExtension
     */
    private String invokeEnsureFileExtension(String filePath, String expectedExt) {
        return (String) ReflectionTestUtils.invokeMethod(webFileUtil, "ensureFileExtension", filePath, expectedExt);
    }
}
