package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for MetadataContextUtils
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MetadataContextUtils 单元测试")
class MetadataContextUtilsJUnit5Test {

    // ==================== getMetadataSearchTemplateContextMap 方法测试 ====================

    @Test
    @DisplayName("获取元数据搜索模板上下文映射 - 正常情况")
    void testGetMetadataSearchTemplateContextMap_Success() {
        // Arrange
        String tenantId = "123456789";
        String userId = "user-987654321";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(tenantId, result.get(ISearchTemplate.TENANT_ID));
        assertEquals(userId, result.get(ISearchTemplate.USER_ID));
    }

    @Test
    @DisplayName("获取元数据搜索模板上下文映射 - null用户")
    void testGetMetadataSearchTemplateContextMap_NullUser() {
        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(null);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("获取元数据搜索模板上下文映射 - 数字租户ID")
    void testGetMetadataSearchTemplateContextMap_NumericTenantId() {
        // Arrange
        String tenantId = "999888777";
        String userId = "user-111222333";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(tenantId, result.get(ISearchTemplate.TENANT_ID));
        assertEquals(userId, result.get(ISearchTemplate.USER_ID));
    }

    @Test
    @DisplayName("获取元数据搜索模板上下文映射 - 特殊字符用户ID")
    void testGetMetadataSearchTemplateContextMap_SpecialCharacterUserId() {
        // Arrange
        String tenantId = "tenant-123";
        String userId = "user@special.chars_456";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(tenantId, result.get(ISearchTemplate.TENANT_ID));
        assertEquals(userId, result.get(ISearchTemplate.USER_ID));
    }

    @Test
    @DisplayName("获取元数据搜索模板上下文映射 - 空字符串ID")
    void testGetMetadataSearchTemplateContextMap_EmptyStringIds() {
        // Arrange
        String tenantId = "";
        String userId = "";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(tenantId, result.get(ISearchTemplate.TENANT_ID));
        assertEquals(userId, result.get(ISearchTemplate.USER_ID));
    }

    @Test
    @DisplayName("获取元数据搜索模板上下文映射 - 极长ID")
    void testGetMetadataSearchTemplateContextMap_VeryLongIds() {
        // Arrange
        StringBuilder longId = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longId.append("very_long_id_segment_").append(i).append("_");
        }
        String tenantId = "tenant_" + longId.toString();
        String userId = "user_" + longId.toString();
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(tenantId, result.get(ISearchTemplate.TENANT_ID));
        assertEquals(userId, result.get(ISearchTemplate.USER_ID));
    }

    @Test
    @DisplayName("获取元数据搜索模板上下文映射 - 国际化字符ID")
    void testGetMetadataSearchTemplateContextMap_InternationalCharacterIds() {
        // Arrange
        String tenantId = "租户_123_テナント";
        String userId = "用户_456_ユーザー";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(tenantId, result.get(ISearchTemplate.TENANT_ID));
        assertEquals(userId, result.get(ISearchTemplate.USER_ID));
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 验证返回的Map类型")
    void testEdgeCase_VerifyReturnedMapType() {
        // Arrange
        String tenantId = "test-tenant";
        String userId = "test-user";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof Map);
        
        // 验证Map的可修改性
        assertDoesNotThrow(() -> {
            result.put("test_key", "test_value");
        });
        
        // 验证添加后的大小
        assertEquals(3, result.size());
    }

    @Test
    @DisplayName("边界测试 - 验证键的正确性")
    void testEdgeCase_VerifyCorrectKeys() {
        // Arrange
        String tenantId = "test-tenant";
        String userId = "test-user";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey(ISearchTemplate.TENANT_ID));
        assertTrue(result.containsKey(ISearchTemplate.USER_ID));
        
        // 验证不包含其他键
        assertEquals(2, result.keySet().size());
        
        // 验证值的类型
        assertTrue(result.get(ISearchTemplate.TENANT_ID) instanceof String);
        assertTrue(result.get(ISearchTemplate.USER_ID) instanceof String);
    }

    @Test
    @DisplayName("边界测试 - 验证值的字符串转换")
    void testEdgeCase_VerifyStringConversion() {
        // Arrange
        String tenantId = "123";
        String userId = "456";
        User user = new User(tenantId, userId);

        // Act
        Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert
        assertNotNull(result);
        
        // 验证值确实是字符串类型
        Object tenantIdValue = result.get(ISearchTemplate.TENANT_ID);
        Object userIdValue = result.get(ISearchTemplate.USER_ID);
        
        assertTrue(tenantIdValue instanceof String);
        assertTrue(userIdValue instanceof String);
        
        assertEquals("123", tenantIdValue);
        assertEquals("456", userIdValue);
    }

    // ==================== 性能测试 ====================

    @Test
    @DisplayName("性能测试 - 大量上下文映射创建")
    void testPerformance_MassiveContextMapCreation() {
        // Arrange
        int testCount = 10000;
        User[] users = new User[testCount];
        
        for (int i = 0; i < testCount; i++) {
            users[i] = new User("tenant_" + i, "user_" + i);
        }

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(users[i]);
            assertNotNull(result);
            assertEquals(2, result.size());
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：10000次操作应该在500毫秒内完成
        assertTrue(duration < 500, "Performance test failed: took " + duration + "ms for " + testCount + " operations");
    }

    @Test
    @DisplayName("性能测试 - null用户处理性能")
    void testPerformance_NullUserHandling() {
        // Arrange
        int testCount = 100000;

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            Map result = MetadataContextUtils.getMetadataSearchTemplateContextMap(null);
            assertNull(result);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：100000次null处理应该在100毫秒内完成
        assertTrue(duration < 100, "Performance test failed: took " + duration + "ms for " + testCount + " null operations");
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的元数据上下文构建流程")
    void testIntegration_CompleteMetadataContextBuildingFlow() {
        // Arrange - 模拟真实业务场景
        String businessTenantId = "company_123456";
        String businessUserId = "employee_789012";
        User businessUser = new User(businessTenantId, businessUserId);

        // Act - 执行完整的上下文构建流程
        Map contextMap = MetadataContextUtils.getMetadataSearchTemplateContextMap(businessUser);

        // Assert - 验证完整流程结果
        assertNotNull(contextMap, "上下文映射不应为null");
        assertEquals(2, contextMap.size(), "上下文映射应包含2个键值对");
        
        // 验证租户ID上下文
        String tenantIdContext = (String) contextMap.get(ISearchTemplate.TENANT_ID);
        assertNotNull(tenantIdContext, "租户ID上下文不应为null");
        assertEquals(businessTenantId, tenantIdContext, "租户ID上下文应与用户租户ID一致");
        
        // 验证用户ID上下文
        String userIdContext = (String) contextMap.get(ISearchTemplate.USER_ID);
        assertNotNull(userIdContext, "用户ID上下文不应为null");
        assertEquals(businessUserId, userIdContext, "用户ID上下文应与用户ID一致");
        
        // 验证业务语义
        assertTrue(tenantIdContext.contains("company"), "租户ID应包含公司标识");
        assertTrue(userIdContext.contains("employee"), "用户ID应包含员工标识");
        
        // 验证上下文的实用性
        assertFalse(tenantIdContext.trim().isEmpty(), "租户ID上下文不应为空");
        assertFalse(userIdContext.trim().isEmpty(), "用户ID上下文不应为空");
        
        // 验证上下文可用于搜索模板
        assertTrue(contextMap.containsKey(ISearchTemplate.TENANT_ID), "应包含搜索模板所需的租户ID键");
        assertTrue(contextMap.containsKey(ISearchTemplate.USER_ID), "应包含搜索模板所需的用户ID键");
        
        // 验证上下文的完整性
        assertEquals(businessUser.getTenantId(), contextMap.get(ISearchTemplate.TENANT_ID), 
                "上下文中的租户ID应与原始用户对象一致");
        assertEquals(businessUser.getUserId(), contextMap.get(ISearchTemplate.USER_ID), 
                "上下文中的用户ID应与原始用户对象一致");
    }

    @Test
    @DisplayName("集成测试 - 多用户上下文构建对比")
    void testIntegration_MultiUserContextComparison() {
        // Arrange - 创建多个不同的用户
        User user1 = new User("tenant_001", "user_001");
        User user2 = new User("tenant_002", "user_002");
        User user3 = new User("tenant_001", "user_003"); // 同租户不同用户

        // Act - 为每个用户构建上下文
        Map context1 = MetadataContextUtils.getMetadataSearchTemplateContextMap(user1);
        Map context2 = MetadataContextUtils.getMetadataSearchTemplateContextMap(user2);
        Map context3 = MetadataContextUtils.getMetadataSearchTemplateContextMap(user3);

        // Assert - 验证上下文的独立性和正确性
        // 验证所有上下文都不为null且结构一致
        assertNotNull(context1);
        assertNotNull(context2);
        assertNotNull(context3);
        assertEquals(2, context1.size());
        assertEquals(2, context2.size());
        assertEquals(2, context3.size());
        
        // 验证不同用户的上下文是独立的
        assertNotEquals(context1, context2, "不同用户的上下文应该不同");
        assertNotEquals(context1, context3, "不同用户的上下文应该不同");
        assertNotEquals(context2, context3, "不同用户的上下文应该不同");
        
        // 验证同租户用户的租户ID上下文相同
        assertEquals(context1.get(ISearchTemplate.TENANT_ID), context3.get(ISearchTemplate.TENANT_ID), 
                "同租户用户的租户ID上下文应该相同");
        
        // 验证不同租户用户的租户ID上下文不同
        assertNotEquals(context1.get(ISearchTemplate.TENANT_ID), context2.get(ISearchTemplate.TENANT_ID), 
                "不同租户用户的租户ID上下文应该不同");
        
        // 验证所有用户的用户ID上下文都不同
        assertNotEquals(context1.get(ISearchTemplate.USER_ID), context2.get(ISearchTemplate.USER_ID));
        assertNotEquals(context1.get(ISearchTemplate.USER_ID), context3.get(ISearchTemplate.USER_ID));
        assertNotEquals(context2.get(ISearchTemplate.USER_ID), context3.get(ISearchTemplate.USER_ID));
    }

    @Test
    @DisplayName("集成测试 - 上下文映射的可变性验证")
    void testIntegration_ContextMapMutabilityVerification() {
        // Arrange
        User user = new User("test_tenant", "test_user");

        // Act
        Map originalContext = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);
        
        // 修改返回的Map
        originalContext.put("additional_key", "additional_value");
        originalContext.put(ISearchTemplate.TENANT_ID, "modified_tenant");

        // 再次获取上下文
        Map newContext = MetadataContextUtils.getMetadataSearchTemplateContextMap(user);

        // Assert - 验证每次调用都返回新的独立Map
        assertNotEquals(originalContext, newContext, "每次调用应返回新的独立Map");
        assertEquals("test_tenant", newContext.get(ISearchTemplate.TENANT_ID), 
                "新上下文不应受之前修改的影响");
        assertFalse(newContext.containsKey("additional_key"), 
                "新上下文不应包含之前添加的键");
        assertEquals(2, newContext.size(), "新上下文应保持原始结构");
    }
}
