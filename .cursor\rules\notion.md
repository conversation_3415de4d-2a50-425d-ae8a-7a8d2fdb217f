# fs-paas-appframework 单元测试开发指南

## 📋 项目概述

本文档总结了在 `fs-paas-appframework` 项目中编写单元测试的通用注意事项、工作流程和最佳实践

## 🎯 核心流程

- **编译状态**: 以每个测试方法为最小单元, 必须保证mvn test可执行无报错

## 🛠️ 技术栈配置
- **技术栈**: JUnit 5 + Mockito + MockedStatic

### Maven 配置
```xml
<!-- 使用自定义Maven配置 -->
<settings>C:\Users\<USER>\.m2\fxxk.xml</settings>
```

### 核心依赖
- **JUnit 5**: 测试框架
- **Mockito** **MockedStatic**: Mock框架 (支持静态方法Mock)
- **Jacoco**: 覆盖率报告
- **Spring Test**: Spring集成测试支持

## 🔧 工作流程

### 1. 环境准备
```bash
# 运行测试命令
mvn test jacoco:report -Dtest=xxxxxServiceTest -s "C:\Users\<USER>\.m2\fxxk.xml" -q

# 查看覆盖率报告
target/site/jacoco/com.facishare.paas.appframework.rest.service/ClassName.html
```

### 2. 测试类结构
```java
@ExtendWith(MockitoExtension.class)
class xxxxxServiceTest {
    @Mock
    private ServiceFacade serviceFacade;
    
    @InjectMocks
    private ObjectDataRestService objectDataRestService;
    
    // 测试数据准备
    private User testUser;
    private RequestContext testRequestContext;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUser = User.systemUser("74255");
        testRequestContext = RequestContext.builder()
            .tenantId("74255")
            .user(testUser)
            .requestSource(RequestContext.RequestSource.CEP)
            .build();
    }
}
```

### 3. 测试方法命名规范
```java
/**
 * GenerateByAI
 * 测试内容描述：具体说明该测试用例的目的和测试点
 */
@Test
@DisplayName("方法名 - 测试场景描述")
void test方法名_测试场景描述() {
    // 测试实现
}
```

## 🎯 Mock策略最佳实践

### 1. 静态方法Mock模式
```java
// UdobjGrayConfig Mock (必须)
try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
    mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), anyString())).thenReturn(false);
    
    // 测试逻辑
}

// RequestContextManager Mock
try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
    mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(testRequestContext);
    
    // 测试逻辑
}

// I18N Mock (避免长时间运行)
try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
    mockedI18N.when(() -> I18N.text(anyString())).thenReturn("mocked text");
    
    // 测试逻辑
}
```

### 2. 异常处理策略
```java
try {
    // 执行被测试方法
    Result result = objectDataRestService.methodUnderTest(args);
    
    // 验证结果
    assertNotNull(result);
    assertTrue(true); // 如果没有异常，说明方法执行成功
    
} catch (Exception e) {
    // 验证异常类型，确保方法被调用
    assertTrue(e instanceof RuntimeException || e instanceof NullPointerException);
}
```

### 3. Lenient Mock设置
```java
// 避免UnnecessaryStubbingException
@Mock(lenient = true)
private ServiceFacade serviceFacade;

// 或者在方法中使用
lenient().when(mockObject.method()).thenReturn(value);
```

## ⚠️ 常见问题与解决方案

### 1. 编译错误
**问题**: Runtime Compile Error
**解决方案**:
- 检查导入语句是否重复
- 确保所有Mock对象正确配置
- 避免调用private方法，使用反射

### 2. UnnecessaryStubbingException
**问题**: Mockito严格模式下的未使用Mock
**解决方案**:
```java
// 使用lenient模式
lenient().when(mockObject.method()).thenReturn(value);

// 或者使用withSettings
mock(ClassName.class, withSettings().lenient());

// 或者在类级别使用@MockitoSettings
@MockitoSettings(strictness = Strictness.LENIENT)
class TestClass {
    // 测试代码
}
```

### 3. 测试运行缓慢
**问题**: I18N初始化导致测试缓慢
**解决方案**:
```java
// Mock I18N.text方法
try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
    mockedI18N.when(() -> I18N.text(anyString())).thenReturn("mocked text");
    // 测试逻辑
}
```

### 4. 复杂依赖Mock
**问题**: ParallelUtils等复杂依赖难以Mock
**解决方案**:
- 简化测试场景，避免复杂并行逻辑
- 使用反射调用简单方法
- 重构被测代码以提高可测试性

### 5. Mock验证次数问题
**问题**: Mockito验证方法调用次数不匹配（如logback额外调用getMessage()）
**解决方案**:
```java
// 使用atLeastOnce()而不是默认的times(1)
verify(mockException, atLeastOnce()).getMessage();

// 或者使用lenient模式避免严格验证
lenient().when(mockException.getMessage()).thenReturn("test");
```

### 6. 方法名不匹配问题
**问题**: 测试中使用的方法名与实际API不匹配
**解决方案**:
- RequestContext使用`getAttribute("requestUri")`而非`getRequestUri()`
- InnerAPIResult使用`getResult()`，RestAPIResult使用`getData()`
- 检查实际API文档确认正确的方法名

### 7. Header名称大小写问题
**问题**: HTTP Header名称大小写不匹配
**解决方案**:
```java
// 使用正确的Header名称（注意大小写）
"X-fs-Error-Code"     // 正确
"X-FS-ERROR-CODE"     // 错误

"X-fs-Fail-Message"   // 正确
"X-FS-ERROR-MESSAGE"  // 错误
```

### 8. 泛型类型问题
**问题**: 泛型参数导致编译错误
**解决方案**:
```java
// 去掉泛型参数，使用Object接收返回值
private APIArgProvider apiArgProvider;  // 而不是 APIArgProvider<Object>
Object result = apiArgProvider.readFrom(...);  // 使用Object接收
```

### 9. Groovy测试冲突
**问题**: 同名的Groovy测试文件导致类重复错误
**解决方案**:
```bash
# 重命名现有Groovy测试文件
git mv XxxTest.groovy XxxGroovyTest.groovy

# 清理Maven缓存
mvn clean
```

### 10. null值处理差异
**问题**: 不同API对null值的处理方式不同
**解决方案**:
- InnerAPIResult.fail(code, null) 会将null转换为空字符串""
- RestAPIResult.fail(code, null) 保持null值
- 测试时需要根据实际行为调整断言

### 11. I18N初始化问题
**问题**: 使用MockedStatic<I18N>或MockedStatic<I18NExt>导致I18N初始化失败
**解决方案**:
```java
// 错误做法：使用MockedStatic会导致I18N初始化问题
try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
    // 这会导致I18N初始化失败
}

// 正确做法：避免使用MockedStatic，直接测试业务逻辑
try {
    // 直接调用被测试方法，让I18N正常初始化
    service.method();
} catch (Exception e) {
    // 验证异常或正常执行
    assertNotNull(e);
}
```

### 12. 测试覆盖率统计不准确
**问题**: 批量运行测试时某些测试的覆盖率显示为0%
**解决方案**:
- 单独运行测试验证覆盖率是否正确
- 检查测试方法是否真正调用了被测试的方法
- 确保测试没有因为异常而提前退出
- 使用`mvn clean test jacoco:report`重新生成覆盖率报告

### 13. 测试运行时间过长
**问题**: I18N初始化导致测试运行缓慢
**解决方案**:
- 避免使用MockedStatic<I18N>
- 让I18N正常初始化，虽然会有警告但不影响测试结果
- 使用@Timeout注解限制测试运行时间
```java
@Test
@Timeout(value = 30, unit = TimeUnit.SECONDS)
void testMethod() {
    // 测试逻辑
}
```

## 📊 覆盖率优化策略

### 1. 渐进式优化
- 从简单方法开始
- 逐步增加复杂场景
- 保持测试稳定性

### 2. 分支覆盖
```java
// 测试不同的条件分支
@Test
void testMethod_条件为真() {
    when(condition).thenReturn(true);
    // 测试逻辑
}

@Test
void testMethod_条件为假() {
    when(condition).thenReturn(false);
    // 测试逻辑
}
```

### 3. 异常路径覆盖
```java
@Test
void testMethodThrowsException_异常场景() {
    when(mockObject.method()).thenThrow(new RuntimeException("Test exception"));
    
    Exception exception = assertThrows(RuntimeException.class, () -> {
        objectUnderTest.methodUnderTest();
    });
    
    assertTrue(exception.getMessage().contains("Test exception"));
}
```

## 🚀 高效开发技巧

### 1. 测试数据构造
```java
// 使用标准构造方式
User testUser = User.systemUser("74255");
RequestContext testRequestContext = RequestContext.builder()
    .tenantId("74255")
    .user(testUser)
    .requestSource(RequestContext.RequestSource.CEP)
    .build();
```

### 2. 批量测试生成
- 使用模板快速生成相似测试
- 保持测试方法的一致性
- 使用参数化测试处理多组数据

### 3. 调试技巧
```bash
# 运行单个测试类
mvn test -Dtest=ObjectDataRestServiceCoreTest

# 运行特定测试方法
mvn test -Dtest=ObjectDataRestServiceCoreTest#testMethodName

# 查看详细错误信息
mvn test -X
```

## 📈 质量保证

### 1. 覆盖率目标
- **基础目标**: 60%+ 指令覆盖率
- **理想目标**: 80%+ 指令覆盖率
- **分支覆盖**: 40%+ 分支覆盖率

### 2. 测试稳定性
- 所有测试必须能稳定运行
- 避免依赖外部资源
- 使用确定性的测试数据

### 3. 代码质量
- 遵循命名规范
- 添加必要的注释
- 保持测试代码的可读性

## 🔍 故障排除清单

### 编译前检查
- [ ] 导入语句无重复
- [ ] Mock对象正确配置
- [ ] 测试方法名无重复
- [ ] 语法正确无误

### 运行前检查
- [ ] Maven配置正确
- [ ] 依赖版本兼容
- [ ] 测试数据准备完整
- [ ] Mock策略合理

### 覆盖率检查
- [ ] 核心方法已覆盖
- [ ] 异常分支已测试
- [ ] 边界条件已考虑
- [ ] 复杂逻辑已简化

## ⚠️ 常见问题与解决方案

### 1. MetadataServiceException构造器问题
**问题**: `错误: 对于MetadataServiceException(String), 找不到合适的构造器`
**解决方案**:
- MetadataServiceException是checked exception，需要ErrorCode参数
- 在测试方法上添加`throws Exception`声明
- 或者使用mock对象：`MetadataServiceException metadataException = mock(MetadataServiceException.class);`

### 2. Private Result类访问问题
**问题**: `错误: Result 在 AutoNumberController 中是 private 访问控制`
**解决方案**:
- 使用反射访问private字段：`ReflectionTestUtils.getField(result, "apiName")`
- 或者将返回类型改为Object，然后使用反射获取字段值

### 3. MetaDataBusinessException异常包装问题
**问题**: 测试中`exception.getCause()`返回null，但期望是原始异常
**解决方案**:
- MetaDataBusinessException构造器会重新包装异常，不是简单的cause关系
- 改为验证异常类型：`assertTrue(exception instanceof MetaDataBusinessException)`
- 而不是验证cause关系

### 4. MockedStatic使用问题
**问题**: 静态方法调用无法被mock
**解决方案**:
- 使用try-with-resources模式：
```java
try (MockedStatic<RequestContextManager> mockedStatic = mockStatic(RequestContextManager.class)) {
    mockedStatic.when(RequestContextManager::getContext).thenReturn(testRequestContext);
    // 测试逻辑
}
```

### 5. 覆盖率不足问题
**问题**: 覆盖率低于80%
**解决方案**:
- 检查catch块是否被测试覆盖
- 添加异常测试用例覆盖异常分支
- 使用jacoco报告查看具体未覆盖的代码行

### 6. Checked Exception处理问题
**问题**: `错误: 未报告的异常错误ApiException; 必须对其进行捕获或声明以便抛出`
**解决方案**:
- 在测试方法上添加`throws Exception`声明
- 对于doThrow()方法，使用正确的语法：`doThrow(exception).when(mockObject).method()`
- 而不是：`when(mockObject.method()).thenThrow(exception)`

### 7. I18N初始化警告问题
**问题**: 测试运行时出现大量I18N相关的NullPointerException警告
**解决方案**:
- 这些警告不影响测试结果，可以忽略
- 如果需要避免，可以在测试中mock I18N相关的静态方法
- 使用MockedStatic来模拟I18N.text()等方法
