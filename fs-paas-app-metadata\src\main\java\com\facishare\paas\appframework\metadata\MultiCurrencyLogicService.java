package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.CurrencyConfig;
import com.facishare.paas.appframework.metadata.multicurrency.RefreshInternational;
import com.facishare.paas.appframework.metadata.repository.model.MtCurrencyExchange;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface MultiCurrencyLogicService {

    /**
     * 开启多货币
     *
     * @param functionalCurrency 本位币
     * @param user               用户信息
     */
    void openMultiCurrency(String functionalCurrency, User user);

    // 开启关闭[显示用户币种转换值]
    void changeUserCurrencyTransStatus(MultiCurrencyStatus status, User user);

    /**
     * 重刷多货币，补刷接口
     */
    void refreshMultiCurrency(RefreshInternational.Arg arg, User user);

    /**
     * 查看企业多币种开启状态
     */
    Integer findMultiCurrencyStatus(User user);
    boolean isOpenMultiCurrency(User user);

    // 查询[多币种]和[显示用户币种转换值]开关的状态
    CurrencyConfig findCurrencyTransStatus(User user);

    /**
     * 查询货币列表
     */
    List<MtCurrency> findCurrencyList(User user);

    List<MtCurrency> findCurrencyBySearchQuery(User user, SearchTemplateQuery searchTemplateQuery);

    /**
     * 查询可选货币列表
     */
    List<MtCurrency> findUnusedCurrencyList(User user);

    /**
     * 获取所有货币列表
     */
    List<MtCurrency> findAllCurrency(User user);

    /**
     * 添加货币
     *
     * @param mtCurrency 货币
     * @param user       用户信息
     */
    void addCurrency(MtCurrency mtCurrency, User user);

    /**
     * 添加货币
     *
     * @param mtCurrency 货币
     * @param user       用户信息
     */
    void editCurrency(MtCurrency mtCurrency, User user);


    /**
     * 禁用货币
     *
     * @param currencyCode 货币代码
     * @param user         用户信息
     */
    void disableCurrency(String currencyCode, User user);

    /**
     * 启用货币
     *
     * @param currencyCode 货币代码
     * @param exchangeRate 汇率
     * @param user         用户信息
     */
    void enableCurrency(String currencyCode, String exchangeRate, User user);

    /**
     * 查询指定货币代码的汇率
     *
     * @param currencyCode 货币代码
     * @param user         用户信息
     * @return 指定代码汇率信息
     */
    MtCurrency findCurrencyByCode(String currencyCode, User user);

    /**
     * 批量查询货币信息
     */
    List<MtCurrency> findCurrencyByCodes(User user, List<String> currencyCodes);

    /**
     * 批量更新汇率
     *
     * @param exchangeRateList 汇率列表
     * @param user             用户信息
     */
    void batchModifyRate(List<MtExchangeRate> exchangeRateList, User user);

    /**
     * 查询历史汇率
     *
     * @param currencyCode 货币代码
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 指定范围内的历史汇率
     */
    QueryResult<MtExchangeRate> queryRate(String currencyCode, Long startTime, Long endTime, Integer pageSize, Integer pageNumber, User user);


    /**
     * 查询本位币信息
     */
    MtCurrency findFunctionalCurrency(User user);

    Optional<String> findFunctionalCurrencyCode(User user);

    void updateFunctionalCurrency(User user, String functionalCurrency, boolean skipValidate);

    @Transactional
    void upsertMultiCurrency(User user, List<MtCurrency> mtCurrencyList, List<MtExchangeRate> mtExchangeRateList);

    void deleteMultiCurrencyByCodes(User user, List<String> currencyCodes);

    MtCurrencyExchange findCurrencyExchange(User user, String fromCurrencyCode, String toCurrencyCode);

    List<MtCurrencyExchange> findCurrencyExchangeList(User user, String toCurrencyCode);

    void updateCurrencyExchanges(User user, List<MtCurrencyExchange> exchanges, String toCurrencyCode);

    List<MtCurrencyExchange> findAllCurrencyExchanges(User user);

    Map<String, String> findCurrencySymbolByCurrencyCodes(List<String> currencyCodes);

    /**
     * 返回个人币种code，如果个人币种为空或禁用，返回本位币
     * 如果发生异常, 返回""
     * @param user
     * @return
     */
    String findPersonCurrencyCode(User user);

    void bulkRefreshCurrencyOptions(List<String> tenantIds);

    /**
     * 为对象描述填充多币种字段
     * <p>
     * 当对象包含货币类型字段时，自动为该对象添加多币种相关的字段，
     * 包括货币代码、汇率等字段。仅对自定义对象且非大对象生效。
     *
     * @param user           用户信息
     * @param objectDescribe 对象描述
     * @throws MetadataServiceException 元数据服务异常
     */
    void fillMultiCurrencyField(User user, IObjectDescribe objectDescribe) throws MetadataServiceException;

    void fillMultiCurrencyField(User user, IObjectDescribe objectDescribe, IObjectDescribe objectDescribeInDB) throws MetadataServiceException;

    /**
     * 为字段列表填充多币种字段
     * <p>
     * 当对象包含货币类型字段时，将多币种相关字段添加到指定的字段列表中。
     * 适用于需要将多币种字段添加到现有字段集合的场景。
     *
     * @param user           用户信息
     * @param objectDescribe 对象描述
     * @param fieldDescribes 字段描述列表，多币种字段将添加到此列表中
     * @throws MetadataServiceException 元数据服务异常
     */
    void fillMultiCurrencyField(User user, IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribes) throws MetadataServiceException;

    /**
     * 直接生成多币种字段列表
     * <p>
     * 根据企业的多币种配置，生成对应的多币种字段描述列表。
     * 包括货币选择字段、汇率字段等，字段选项基于企业已配置的货币列表。
     *
     * @param user            用户信息
     * @param describeApiName 对象API名称
     * @return 多币种字段描述列表
     * @throws MetadataServiceException 元数据服务异常
     */
    List<IFieldDescribe> directFillMultiCurrencyFields(User user, String describeApiName) throws MetadataServiceException;
}
