package com.facishare.paas.appframework.privilege;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ContextCacheUtil;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.OutDataPrivilege;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.paas.appframework.privilege.dto.DeleteTemporaryRights;
import com.facishare.paas.appframework.privilege.dto.UpdateTemporaryRights;
import com.facishare.paas.appframework.privilege.util.ActionCodeConvertUtil;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.privilege.dto.TemporaryRights.*;
import static com.facishare.paas.appframework.privilege.util.PrivilegeConstants.*;

/**
 * Created by zhouwr on 2017/10/16
 */
@Slf4j
@Service("dataPrivilegeService")
public class DataPrivilegeServiceImpl implements DataPrivilegeService {

    @Autowired
    private DataPrivilegeProxy proxy;
    @Autowired
    private OutDataPrivilegeProxy outDataPrivilegeProxy;

    @Autowired
    private CrmService crmService;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private AppDefaultRocketMQProducer caTemporaryPrivilegeMQSender;

    @Autowired
    private DataAuthServiceProxy dataAuthServiceProxy;

    @Autowired
    private LogService logService;

    @Autowired
    private DataSharingProcessor dataSharingProcessor;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    @Autowired
    private DataPrivilegeCommonService dataPrivilegeCommonService;

    @Autowired
    private ManageGroupService manageGroupService;

    private String CALL_CENTER = "CALL_CENTER";

    private FsGrayReleaseBiz dataAuthGray = FsGrayRelease.getInstance("data-auth");

    private boolean enableCheckEntityShareLicenseInfo(String tenantId) {
        return dataAuthGray.isAllow("enableCheckEntityShareLicenseInfo", tenantId);
    }

    private boolean enableEntityShareCountByQueryScope(String tenantId) {
        return dataAuthGray.isAllow("enableEntityShareCountByQueryScope", tenantId);
    }

    private boolean enableUpdateTranslationByEditTeamRole(String tenantId) {
        return dataAuthGray.isAllow("enableUpdateTranslationByEditTeamRole", tenantId);
    }

    private boolean enableTemporaryPrivilegeListSupportTimeRange(String tenantId) {
        return dataAuthGray.isAllow("enableTemporaryPrivilegeListSupportTimeRange", tenantId);
    }

    private boolean enableDataAuthSupportMultiLanguageMessage(String tenantId) {
        return dataAuthGray.isAllow("enableDataAuthSupportMultiLanguageMessage", tenantId);
    }

    @Override
    public Map<String, Permissions> checkDataPrivilege(User user, List<String> idList, String apiName) {
        if (CollectionUtils.empty(idList)) {
            return Maps.newHashMap();
        }

        Map<String, String> objectsPermission = ContextCacheUtil.getDataPrivilegesCache(user.getUserId(), idList);
        Map<String, Permissions> result = objectsPermission.keySet().stream().collect(Collectors.toMap(
                x -> x, value -> Permissions.getEnumByValue(objectsPermission.get(value))));

        List<String> rpcIdList = idList.stream().filter(x -> !objectsPermission.containsKey(x)).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(rpcIdList)) {
            Map<String, Permissions> rpcPermissions = doCheckDataPrivilege(user, rpcIdList, apiName);
            result.putAll(rpcPermissions);

            Map<String, String> rpcPermissionCodes = Maps.newHashMap();
            rpcPermissions.forEach((id, permission) -> rpcPermissionCodes.put(id, permission.getValue()));
            ContextCacheUtil.cacheDataPrivilege(user.getUserId(), rpcPermissionCodes);
        }

        return result;
    }

    private Map<String, Permissions> doCheckDataPrivilege(User user, List<String> idList, String apiName) {
        if (CollectionUtils.empty(idList)) {
            return Maps.newHashMap();
        }
        String objectEditAllKey = ActionCodeConvertUtil.convert2FuncCode(apiName, SUFFIX_OBJECT_LEVEL_EDIT_ALL);
        String objectViewAllKey = ActionCodeConvertUtil.convert2FuncCode(apiName, SUFFIX_OBJECT_LEVEL_VIEW_ALL);
        Map<String, Boolean> editAndViewAllDataPrivilege = functionPrivilegeService.funDataPrivilegeCheck(user, apiName, Lists.newArrayList(SYSTEM_LEVEL_EDIT_ALL_DATA, objectEditAllKey
                , SYSTEM_LEVEL_VIEW_ALL_DATA, objectViewAllKey));
        if (BooleanUtils.isTrue(editAndViewAllDataPrivilege.get(SYSTEM_LEVEL_EDIT_ALL_DATA)) || BooleanUtils.isTrue(editAndViewAllDataPrivilege.get(objectEditAllKey))) {
            return idList.stream().distinct().collect(Collectors.toMap(x -> x, x -> Permissions.READ_WRITE));
        }

        boolean isSubCascadeConfig = crmService.getIsSubCascadeConfig(user.getTenantId());
        ObjectsPermission.Arg arg = new ObjectsPermission.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setObjects(idList);
        arg.setEntityId(apiName);
        arg.setCascadeDept(isSubCascadeConfig);
        arg.setCascadeSubordinates(isSubCascadeConfig);
        arg.setRoleType(DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.OWNER.getValue());

        Map<String, String> header = RestUtils.buildHeaders(user);
        ObjectsPermission.Result permission = dataAuthServiceProxy.objectsPermission(header, arg);
        if (!permission.isSuccess()) {
            log.warn("objectsPermission failed,arg:{},result:{},user=>{}", JSON.toJSONString(arg),
                    JSON.toJSONString(permission), user);
            throwBusinessMessageByCode(user, permission.getErrCode(), permission.getErrMessage());
        }
        Map<String, String> objectsPermission = permission.getResult();
        Map<String, Permissions> result = idList.stream().distinct().collect(Collectors.toMap(
                x -> x, value -> Permissions.getEnumByValue(objectsPermission.get(value))));
        if (BooleanUtils.isTrue(editAndViewAllDataPrivilege.get(SYSTEM_LEVEL_VIEW_ALL_DATA)) || BooleanUtils.isTrue(editAndViewAllDataPrivilege.get(objectViewAllKey))) {
            result.forEach((id, permissions) -> {
                if (Permissions.NO_PERMISSION.equals(permissions)) {
                    result.put(id, Permissions.READ_ONLY);
                }
            });
        }
        return result;
    }

    @Override
    public Boolean delDataRights(User user, String apiName) {
        DelDataRights.Arg arg = new DelDataRights.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setEntitys(Lists.newArrayList(apiName));
        Map<String, String> header = RestUtils.buildHeaders(user);
        DelDataRights.Result result = proxy.delDataRights(header, arg);

        if (!result.isSuccess()) {
            log.warn("delDataRights failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result.isSuccess();
    }

    @Override
    public boolean delFieldShare(User user, List<String> shareIds, String describeApiName, int status) {
        if (CollectionUtils.empty(shareIds)) {
            log.debug("delShareRules 共享规则id列表是空");
            return true;
        }
        DelEntityFieldShareModel.Arg arg = new DelEntityFieldShareModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setRuleCodes(shareIds);
        arg.setEntityId(describeApiName);
        arg.setStatus(status);
        Map<String, String> header = RestUtils.buildHeaders(user);
        DelEntityFieldShareModel.Result result = proxy.deleteEntityFieldShare(header, arg);

        if (!result.isSuccess()) {
            log.warn("deleteEntityFieldShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result.isSuccess();
    }

    @Override
    public boolean changeFieldShareStatus(User user, List<String> shareIds, int status) {
        if (CollectionUtils.empty(shareIds)) {
            log.debug("delShareRules 共享规则id列表是空");
            return true;
        }
        UpdateEntityFieldShareStatusModel.Arg arg = new UpdateEntityFieldShareStatusModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setRuleCodes(shareIds);
        arg.setStatus(status);
        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateEntityFieldShareStatusModel.Result result = proxy.updateEntityFieldShareStatus(header, arg);

        if (!result.isSuccess()) {
            log.warn("updateEntityFieldShareStatus failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result.isSuccess();
    }

    @Override
    public CreateEntityFieldShareModel.Result addFieldShare(User user, String describeApiName, String ruleName, List<Receive> receives, String ruleParse, List<Rule> rules) {
        return addFieldShareRule(user, describeApiName, ruleName, receives, ruleParse, rules, false);
    }

    @Override
    public CreateEntityFieldShareModel.Result addFieldShareRule(User user,
                                                                String describeApiName,
                                                                String ruleName,
                                                                List<Receive> receives,
                                                                String ruleParse,
                                                                List<Rule> rules,
                                                                boolean isCheckRule) {
        //检查数量限制
        int fieldShareCount = findFieldShareCount(user);
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo
                .builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.DATA_ROLE.getBizCode()));
        tenantLicenseInfo.checkConditionalDataShareCount(fieldShareCount);

        FieldShareRule fieldShareRule = new FieldShareRule();
        fieldShareRule.setEntityId(describeApiName);
        fieldShareRule.setRuleName(ruleName);
        fieldShareRule.setStatus(1);
        fieldShareRule.setReceives(receives);
        fieldShareRule.setRuleParse(ruleParse);
        fieldShareRule.setRules(rules);
        CreateEntityFieldShareModel.Arg arg = new CreateEntityFieldShareModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setEntityFieldShare(fieldShareRule);
        if (isCheckRule) {
            if (arg.getContext().getProperties() == null) {
                arg.getContext().setProperties(Maps.newHashMap());
            }
            arg.getContext().getProperties().put("isCheckFieldShareRule", "true");
        }
        Map<String, String> header = RestUtils.buildHeaders(user);
        CreateEntityFieldShareModel.Result ret = proxy.createEntityFieldShare(header, arg);
        if (!ret.isSuccess()) {
            log.warn("createEntityFieldShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(ret));
            if (isCheckRule) {
                throw new MetaDataBusinessException(getPromptMessageByCode(ret.getErrCode(), ret.getErrMessage()));
            } else {
                throwBusinessMessageByCode(user, ret.getErrCode(), ret.getErrMessage());
            }
        }
        return ret;
    }

    @Override
    public boolean updateFieldShare(User user, String id, String describeApiName, String ruleName, List<Receive> receives, String ruleParse, List<Rule> rules) {
        return updateFieldShareRule(user, id, describeApiName, ruleName, receives, ruleParse, rules, false);
    }

    @Override
    public boolean updateFieldShareRule(User user,
                                        String id,
                                        String describeApiName,
                                        String ruleName,
                                        List<Receive> receives,
                                        String ruleParse,
                                        List<Rule> rules,
                                        boolean isCheckRule) {
        FieldShareRule fieldShareRule = new FieldShareRule();
        fieldShareRule.setRuleCode(id);
        fieldShareRule.setEntityId(describeApiName);
        fieldShareRule.setRuleName(ruleName);
        fieldShareRule.setStatus(1);
        fieldShareRule.setReceives(receives);
        fieldShareRule.setRuleParse(ruleParse);
        fieldShareRule.setRules(rules);
        UpdateEntityFieldShareModel.Arg arg = new UpdateEntityFieldShareModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setEntityFieldShare(fieldShareRule);
        if (isCheckRule) {
            if (arg.getContext().getProperties() == null) {
                arg.getContext().setProperties(Maps.newHashMap());
            }
            arg.getContext().getProperties().put("isCheckFieldShareRule", "true");
        }
        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateEntityFieldShareModel.Result result = proxy.updateEntityFieldShare(header, arg);
        if (!result.isSuccess()) {
            log.warn("updateEntityFieldShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            if (isCheckRule) {
                throw new MetaDataBusinessException(getPromptMessageByCode(result.getErrCode(), result.getErrMessage()));
            } else {
                throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
            }
        }
        return result.isSuccess();
    }

    @Override
    public QueryAllEntityFieldShareModel.Result getAllFieldShareList(User user,
                                                                     List ruleCodes,
                                                                     Integer status,
                                                                     Integer permissionType,
                                                                     List receives,
                                                                     Map receivesWithType,
                                                                     Set<String> createIds,
                                                                     Set<String> modifyIds,
                                                                     Map<String, Long> createTimeRange,
                                                                     Map<String, Long> modifyTimeRange,
                                                                     String ruleName,
                                                                     Integer pageNumber,
                                                                     Integer pageSize,
                                                                     List<String> entices,
                                                                     Boolean outReceive) {
        QueryAllEntityFieldShareModel.Arg arg = new QueryAllEntityFieldShareModel.Arg();
        arg.setContext(buildAuthContext(user));

        arg.setEntityType(0);
        if (CollectionUtils.notEmpty(entices)) {
            arg.setEntityIds(Sets.newHashSet(entices));
        }

        if (status != null && status != -1) {
            arg.setStatus(status);
        }

        if (permissionType != null && permissionType != -1) {
            arg.setPermission(permissionType);
        }
        if (CollectionUtils.notEmpty(receives)) {
            arg.setReceives(receives);
        }
        if (CollectionUtils.notEmpty(receivesWithType)) {
            arg.setReceivesWithType(receivesWithType);
        }
        if (!Strings.isNullOrEmpty(ruleName)) {
            arg.setRuleName(ruleName);
        }
        if (CollectionUtils.notEmpty(ruleCodes)) {
            arg.setRuleCodes(ruleCodes);
        }

        if (CollectionUtils.notEmpty(createIds)) {
            arg.setCreateIds(createIds);
        }
        if (CollectionUtils.notEmpty(modifyIds)) {
            arg.setModifyIds(modifyIds);
        }
        if (MapUtils.isNotEmpty(createTimeRange)) {
            arg.setCreateTimeRange(createTimeRange);
        }
        if (MapUtils.isNotEmpty(modifyTimeRange)) {
            arg.setModifyTimeRange(modifyTimeRange);
        }

        Optional.ofNullable(arg.getReceivesWithType()).ifPresent(receivesId -> receivesId.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));

        arg.setPageInfo(buildPageInfo(pageSize, pageNumber));
        arg.setOutReceive(outReceive);
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryAllEntityFieldShareModel.Result result = proxy.queryAllEntityFieldShareList(header, arg);

        if (!result.isSuccess()) {
            log.warn("queryAllEntityFieldShareList failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result;
    }

    /**
     * 老对象特使迁移前的那十多个对象，并不是预置对象
     *
     * @param user
     * @param describeApiName  0 全部对象 -1 自定义对象 -2 老对象
     * @param shareIds         在describeApiName参数的基础上查 指定apiName
     * @param status
     * @param permissionType
     * @param receives
     * @param receivesWithType
     * @param ruleName
     * @param pageNumber
     * @param pageSize
     * @param entices
     * @return
     */
    @Override
    public QueryEntityFieldShareModel.Result getFieldShares(User user, String describeApiName, List shareIds, Integer status,
                                                            Integer permissionType, List receives, Map receivesWithType,
                                                            String ruleName, Integer pageNumber, Integer pageSize,
                                                            List<String> entices, Boolean outReceive) {
        QueryEntityFieldShareModel.Arg arg = new QueryEntityFieldShareModel.Arg();
        arg.setContext(buildAuthContext(user));

        //
        if (Strings.isNullOrEmpty(describeApiName) || "0".equals(describeApiName)) {
            arg.setEntityType(0);
            if (CollectionUtils.notEmpty(entices)) {
                arg.setEntityIds(Sets.newHashSet(entices));
            }
        } else if ("-1".equals(describeApiName)) {
            arg.setEntityType(1);
            if (CollectionUtils.notEmpty(entices)) {
                arg.setEntityIds(Sets.newHashSet(entices));
            }
        } else if ("-2".equals(describeApiName)) {
            arg.setEntityType(2);
        } else {
            arg.setEntityIds(Sets.newHashSet(describeApiName));
            // 兼容旧有数据权限接口
            arg.setEntityId(describeApiName);
        }

        if (status != null && status != -1) {
            arg.setStatus(status);
        }

        if (permissionType != null && permissionType != -1) {
            arg.setPermission(permissionType);
        }
        if (CollectionUtils.notEmpty(receives)) {
            arg.setReceives(receives);
        }
        if (CollectionUtils.notEmpty(receivesWithType)) {
            arg.setReceivesWithType(receivesWithType);
        }
        if (!Strings.isNullOrEmpty(ruleName)) {
            arg.setRuleName(ruleName);
        }
        if (CollectionUtils.notEmpty(shareIds)) {
            arg.setRuleCodes(shareIds);
        }

        Optional.ofNullable(arg.getReceivesWithType())
                .ifPresent(receivesId -> receivesId.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));

        arg.setPageInfo(buildPageInfo(pageSize, pageNumber));
        arg.setOutReceive(outReceive);
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityFieldShareModel.Result result = proxy.queryEntityFieldShare(header, arg);

        if (!result.isSuccess()) {
            log.warn("queryEntityFieldShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> queryDimensionIntersectionStatus(User user, Map<String, String> queryContent) {
        if (user == null || queryContent == null || StringUtils.isBlank(user.getTenantId())) {
            return Maps.newHashMap();
        }
        Map<String, String> header = RestUtils.buildHeaders(user);
        queryContent.put("tenantId", user.getTenantId());
        QueryDimensionIntersectionStatusModel.Result queryStatusResult = proxy.queryDimensionIntersectionStatus(header, queryContent);
        if (!queryStatusResult.isSuccess()) {
            log.warn("queryDimensionIntersectionStatus failed, arg:{}, result:{}", JSON.toJSONString(queryContent), JSON.toJSONString(queryStatusResult));
            throw new MetaDataBusinessException(getPromptMessageByCode(queryStatusResult.getErrCode(), queryStatusResult.getErrMessage()));
        }
        if (queryStatusResult.getResult() == null) {
            return Maps.newHashMap();
        }
        return queryStatusResult.getResult();
    }

    @Override
    public List<ObjectDataPermissionInfo> getCommonPrivilegeListResult(User user, List<IObjectDescribe> objectDescribeList) {
        List<IObjectDescribe> describeList = handelDescribeList(objectDescribeList);

        GetBaseDataPrivilegeRulesModel.Arg arg = new GetBaseDataPrivilegeRulesModel.Arg();
        arg.setContext(buildAuthContext(user));

        List<String> apiNameList = describeList.stream()
                //.filter(a->CollectionUtils.empty(a.getChildApiNames()))
                .map(IObjectDescribe::getApiName)
                .collect(Collectors.toList());

        //查询分管的对象
        boolean isAllSupport = false;
        Set<String> supportApiNames = Sets.newHashSet();

        if (dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(user.getTenantId()) && !user.isOutUser()) {
            ManageGroup manageGroup = manageGroupService.queryManageGroup(user, null, ManageGroupType.OBJECT, true);
            if (manageGroup == null) {
                return Lists.newArrayList();
            }

            isAllSupport = manageGroup.isAllSupport();
            if (manageGroup.getSupportApiNames() != null) {
                supportApiNames = manageGroup.getSupportApiNames();
            }

            if (!isAllSupport) {
                apiNameList = dataPrivilegeCommonService.checkEntityIdsByManageGroup(apiNameList, manageGroup);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(apiNameList)) {
                    return Lists.newArrayList();
                }
            }
        }

        //null 根据底层Auth服务定义，null代表获取全部
        arg.setEntitys(apiNameList);
        arg.setScope(null);
        arg.setPermission(null);
        Map<String, String> header = RestUtils.buildHeaders(user);
        GetBaseDataPrivilegeRulesModel.Result baseDataPrivilegeRules = proxy.getBaseDataPrivilegeRules(header, arg);

        if (!baseDataPrivilegeRules.isSuccess()) {
            log.warn("getBaseDataPrivilegeRules failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(baseDataPrivilegeRules));
            throwBusinessMessageByCode(user, baseDataPrivilegeRules.getErrCode(), baseDataPrivilegeRules.getErrMessage());
        }

        List<ObjectDataPermissionInfo> result;
        try {
            result = entityOpennessPojo2InfoOrderByDescribe(baseDataPrivilegeRules.getResult().getContent(), describeList);

            if (!dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(user.getTenantId()) || user.isOutUser() || isAllSupport || supportApiNames.contains(Utils.CUSTOMER_PAYMENT_API_NAME)) {
                // 补充回款
                fillPermissionInfo(result, Utils.CUSTOMER_PAYMENT_API_NAME);
            }
            if (!dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(user.getTenantId()) || user.isOutUser() || isAllSupport || supportApiNames.contains(Utils.PERSONNEL_OBJ_API_NAME)) {
                // 补充人员
                fillPermissionInfo(result, Utils.PERSONNEL_OBJ_API_NAME);
            }
        } catch (MetaDataException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        return result;
    }

    private void fillPermissionInfo(List<ObjectDataPermissionInfo> list, String apiName) {
        if (Objects.isNull(list)) {
            return;
        }

        Optional<ObjectDataPermissionInfo> any = list.stream().filter(
                a -> Objects.equals(a.getObjectDescribeApiName(), apiName)).findAny();
        if (!any.isPresent()) {
            ObjectDataPermissionInfo info = ObjectDataPermissionInfo.create(apiName,
                    I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(apiName)),
                    DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue());
            list.add(info);
        }
    }

    @Override
    public ObjectDataPermissionInfo getCommonPrivilege4DefObjResult(User user, IObjectDescribe objectDescribe) {
        String apiName = objectDescribe.getApiName();
        GetBaseDataPrivilegeRulesModel.Arg arg = new GetBaseDataPrivilegeRulesModel.Arg();
        arg.setContext(buildAuthContext(user));
        //null 根据底层Auth服务定义，null代表获取全部
        arg.setEntitys(Lists.newArrayList(apiName));
        arg.setScope(null);
        arg.setPermission(null);
        Map<String, String> header = RestUtils.buildHeaders(user);
        GetBaseDataPrivilegeRulesModel.Result baseDataPrivilegeRules = proxy.getBaseDataPrivilegeRules(header, arg);
        if (!baseDataPrivilegeRules.isSuccess()) {
            log.warn("getBaseDataPrivilegeRules failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(baseDataPrivilegeRules));
            throwBusinessMessageByCode(user, baseDataPrivilegeRules.getErrCode(), baseDataPrivilegeRules.getErrMessage());
        }
        List<ObjectDataPermissionInfo> result = null;
        try {
            result = changeEntityOpennessPojo2ObjectDataPermissionInfo(baseDataPrivilegeRules.getResult().getContent(),
                    Lists.newArrayList(objectDescribe));
        } catch (MetaDataException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (result == null || result.size() == 0) {
            return new ObjectDataPermissionInfo(apiName, apiName, DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.NO_PERMISSION.getValue());
        }
        ObjectDataPermissionInfo objectDataPermissionInfo = result.get(0);
        return objectDataPermissionInfo == null ?
                new ObjectDataPermissionInfo(apiName, apiName, DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.NO_PERMISSION.getValue()) :
                objectDataPermissionInfo;
    }

    @Override
    public void addCommonPrivilegeListResult(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos) {
        // 1.先查询
        Map<String, EntityOpennessPojo> entityOpennessPojoMapInDb = queryCommonPrivilegeByOpennessPojoList(user, objectDataPermissionInfos);
        // 2.新建
        createBaseDataPrivilege(user, objectDataPermissionInfos);
        // 3.根据diff的结果记日志
        logCommonPrivilege(user, objectDataPermissionInfos, entityOpennessPojoMapInDb, EventType.ADD, ActionType.Add);
    }

    private void createBaseDataPrivilege(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos) {
        CreateBaseDataPrivilegeRulesModel.Arg arg = new CreateBaseDataPrivilegeRulesModel.Arg();
        arg.setContext(buildAuthContext(user));
        List<EntityOpennessPojo> entityOpennessPojoList =
                changeObjectDataPermissionInfo2EntityOpennessPojo(objectDataPermissionInfos, user);
        arg.setEntityOpenness(entityOpennessPojoList);

        Map<String, String> header = RestUtils.buildHeaders(user);
        CreateBaseDataPrivilegeRulesModel.Result result = proxy.createBaseDataPrivilegeRules(header, arg);
        if (!result.isSuccess()) {
            log.warn("createBaseDataPrivilegeRules failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }
    }

    @Override
    public void initCommonPrivilegeListResult(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos) {
        // 1.先查询
        Map<String, EntityOpennessPojo> entityOpennessPojoMapInDb = queryCommonPrivilegeByOpennessPojoList(user, objectDataPermissionInfos);
        // 2.新建
        createBaseDataPrivilege(user, objectDataPermissionInfos);
        // 3.根据diff的结果记日志
        logCommonPrivilege(user, objectDataPermissionInfos, entityOpennessPojoMapInDb, EventType.MODIFY, ActionType.Recovery);
    }

    @Override
    public void updateCommonPrivilegeList(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos) {
        if (CollectionUtils.empty(objectDataPermissionInfos)) {
            return;
        }
        Map<String, EntityOpennessPojo> entityOpennessPojoMapInDb = queryCommonPrivilegeByOpennessPojoList(user, objectDataPermissionInfos);
        List<EntityOpennessPojo> entityOpennessPojoList = changeObjectDataPermissionInfo2EntityOpennessPojo(objectDataPermissionInfos, user);
        UpdateBaseDataPrivilegeRulesModel.Arg arg = new UpdateBaseDataPrivilegeRulesModel.Arg();
        arg.setEntityOpenness(entityOpennessPojoList);
        arg.setContext(buildAuthContext(user));

        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateBaseDataPrivilegeRulesModel.Result result = proxy.updateBaseDataPrivilegeRules(header, arg);
        if (!result.isSuccess()) {
            log.warn("updateBaseDataPrivilegeRules failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }
        logCommonPrivilege(user, objectDataPermissionInfos, entityOpennessPojoMapInDb, EventType.MODIFY, ActionType.Modify);
    }

    private void logCommonPrivilege(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos, Map<String,
            EntityOpennessPojo> entityOpennessPojoMapInDb, EventType eventType, ActionType actionType) {
        List<ObjectDataPermissionInfo> changeList = objectDataPermissionInfos.stream()
                .filter(x -> x.isChange(entityOpennessPojoMapInDb.get(x.getObjectDescribeApiName())))
                .collect(Collectors.toList());
        List<LogInfo.ObjectSnapshot> snapshots = changeList.stream()
                .map(x -> x.toSnapshot(entityOpennessPojoMapInDb.get(x.getObjectDescribeApiName())))
                .collect(Collectors.toList());
        logService.logDataPermission(user, eventType, actionType, snapshots);
    }

    private Map<String, EntityOpennessPojo> queryCommonPrivilegeByOpennessPojoList(User user, List<ObjectDataPermissionInfo> entityOpennessPojoList) {
        List<String> entitys = entityOpennessPojoList.stream().map(ObjectDataPermissionInfo::getObjectDescribeApiName).collect(Collectors.toList());
        GetBaseDataPrivilegeRulesModel.Arg arg = new GetBaseDataPrivilegeRulesModel.Arg();
        arg.setContext(buildAuthContext(user));
        //null 根据底层Auth服务定义，null代表获取全部
        arg.setEntitys(entitys);
        Map<String, String> header = RestUtils.buildHeaders(user);
        GetBaseDataPrivilegeRulesModel.Result baseDataPrivilegeRules = proxy.getBaseDataPrivilegeRules(header, arg);
        if (!baseDataPrivilegeRules.isSuccess()) {
            log.warn("getBaseDataPrivilegeRules failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(baseDataPrivilegeRules));
            throwValidateMessageByCode(user, baseDataPrivilegeRules.getErrCode(), baseDataPrivilegeRules.getErrMessage());
        }
        return baseDataPrivilegeRules.getContent().stream().collect(Collectors.toMap(EntityOpennessPojo::getEntityId, x -> x, (x, y) -> y));
    }

    @Override
    public List<String> addOrUpdateShareRules(User user, int permissionType, List<String> describeApiNameList,
                                              List<Integer> sourceCircleIDList, List<Integer> sourceEmployeeIDList,
                                              List<String> sourceUserGroupIDList, List<String> sourceRoleIDList, List<Integer> targetCircleIDList,
                                              List<Integer> targetEmployeeIDList, List<String> targetUserGroupIDList, List<String> targetRoleIDList) {
        if (CollectionUtils.empty(describeApiNameList)) {
            log.debug("addShareRules 对象的apiname列表是空");
            return null;
        }

        List<EntitySharePojo> shareList = Lists.newArrayList();

        List<EntitySharePojo> groupSharaPojoList = genarateSourceShareList(USER_GROUP_TYPE, sourceUserGroupIDList,
                describeApiNameList, targetCircleIDList, targetEmployeeIDList,
                targetUserGroupIDList, permissionType, user, targetRoleIDList);
        shareList.addAll(groupSharaPojoList);

        List<EntitySharePojo> roleSharePojoList = genarateSourceShareList(ROLE_TYPE, sourceRoleIDList,
                describeApiNameList, targetCircleIDList, targetEmployeeIDList,
                targetUserGroupIDList, permissionType, user, targetRoleIDList);
        shareList.addAll(roleSharePojoList);

        List<String> sourceUserIdStrList = sourceEmployeeIDList.stream().map(x -> String.valueOf(x)).collect(Collectors.toList());

        List<EntitySharePojo> userSharaPojoList = genarateSourceShareList(USER_TYPE, sourceUserIdStrList,
                describeApiNameList, targetCircleIDList, targetEmployeeIDList,
                targetUserGroupIDList, permissionType, user, targetRoleIDList);
        shareList.addAll(userSharaPojoList);

        List<String> sourceCircleIdStrList = sourceCircleIDList.stream().map(x -> String.valueOf(x)).collect(Collectors.toList());
        List<EntitySharePojo> circleSharaPojoList = genarateSourceShareList(DEPART_TYPE, sourceCircleIdStrList, describeApiNameList,
                targetCircleIDList, targetEmployeeIDList, targetUserGroupIDList, permissionType, user, targetRoleIDList);
        shareList.addAll(circleSharaPojoList);

        return handleCreateOrUpdateShare(user, shareList);
    }

    @Override
    public List<String> addOrUpdateShareRules(User user, DataSharing dataSharing, Set<String> queryAllEntityIds) {
        return addOrUpdateEntityShareRules(user, dataSharing, queryAllEntityIds, false);
    }

    @Override
    public List<String> addOrUpdateEntityShareRules(User user, DataSharing dataSharing, Set<String> queryAllEntityIds, boolean isApiOperation) {
        List<String> resultList = Lists.newCopyOnWriteArrayList();
        List<EntitySharePojo> shareList = dataSharing.generateEntityShareList();
        if (CollectionUtils.empty(shareList)) {
            return resultList;
        }

        if (isApiOperation && shareList.size() > 50) {
            throw new MetaDataBusinessException(I18nMessage.of("data.privilege.message.code.202501001",
                    I18N.text("data.privilege.message.code.202501001", "50"), "50"));
        }
        //API接口调用需要验证参数
        if (isApiOperation) {
            boolean isIncorrectData = false;
            for (EntitySharePojo v : shareList) {
                if (!checkEntitySharePojoInfo(user, v, queryAllEntityIds)) {
                    isIncorrectData = true;
                    break;
                }
            }
            if (isIncorrectData) {
                throw new MetaDataBusinessException(I18nMessage.of(I18NKey.PARAM_ERROR, I18N.text(I18NKey.PARAM_ERROR)));
            }
        }

        List<EntitySharePojo> entityShareList = queryEntityShare(user, shareList, dataSharing.getBasedType());

        CompositeSpecificationManager specificationManager = CompositeSpecificationManager.of(entityShareList);
        Map<Boolean, List<EntitySharePojo>> entityShareMap = shareList.stream().collect(Collectors.groupingBy(specificationManager::contains));
        EntitySharePojo.EntitySharePojoHelper entitySharePojoHelper = dataSharingProcessor.processorByDataSharing(dataSharing, user);
        List<EntitySharePojo> toUpdate = entityShareMap.get(true);
        List<EntitySharePojo> toCreate = entityShareMap.get(false);

        if (enableCheckEntityShareLicenseInfo(user.getTenantId()) && CollectionUtils.notEmpty(toCreate)) {
            int entityShareCount;
            int toCreateCount = 0;
            if (enableEntityShareCountByQueryScope(user.getTenantId())) {
                for (EntitySharePojo v : toCreate) {
                    if (StringUtils.isNotBlank(v.getReceiveTenantId())) {
                        continue;
                    }
                    toCreateCount++;
                }
            } else {
                toCreateCount = toCreate.size();
            }

            if (toCreateCount > 0) {
                if (enableEntityShareCountByQueryScope(user.getTenantId())) {
                    entityShareCount = findEntityShareCountByScope(user, queryAllEntityIds, 1) + toCreateCount;
                } else {
                    entityShareCount = findEntityShareCount(user, queryAllEntityIds) + toCreateCount;
                }

                TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo
                        .builder()
                        .user(user)
                        .licenseService(licenseService)
                        .build()
                        .init(Sets.newHashSet(ModulePara.ModuleBiz.DATA_ROLE.getBizCode()));
                tenantLicenseInfo.checkEntityShareCount(entityShareCount);
            }
        }

        if (isApiOperation) {
            //API接口调用需要同步处理
            if (CollectionUtils.notEmpty(toUpdate)) {
                // 对已经存在的规则进行更新操作
                List<String> updateResult = batchUpdateEntityShare(user, toUpdate, true);
                List<LogInfo.ObjectSnapshot> snapshots = toUpdate.stream().map(entitySharePojoHelper::toSnapshot).collect(Collectors.toList());
                logService.logDataPermission(user, EventType.MODIFY, ActionType.Modify, snapshots);
                resultList.addAll(updateResult);
            }

            if (CollectionUtils.notEmpty(toCreate)) {
                // 对剩余的规则进行新建操作
                List<String> createResult = batchCreateEntityShare(user, toCreate, true);
                List<LogInfo.ObjectSnapshot> snapshots = toCreate.stream().map(entitySharePojoHelper::toSnapshot).collect(Collectors.toList());
                logService.logDataPermission(user, EventType.ADD, ActionType.Add, snapshots);
                resultList.addAll(createResult);
            }
        } else {
            if (CollectionUtils.notEmpty(toUpdate)) {
                // 对已经存在的规则进行更新操作
                asyncTask(toUpdate, (sharePojos) -> {
                    List<String> updateResult = batchUpdateEntityShare(user, sharePojos, false);
                    List<LogInfo.ObjectSnapshot> snapshots = sharePojos.stream().map(entitySharePojoHelper::toSnapshot).collect(Collectors.toList());
                    logService.logDataPermission(user, EventType.MODIFY, ActionType.Modify, snapshots);
                    resultList.addAll(updateResult);
                });
            }

            if (CollectionUtils.notEmpty(toCreate)) {
                // 对剩余的规则进行新建操作
                asyncTask(toCreate, (sharePojos) -> {
                    List<String> createResult = batchCreateEntityShare(user, sharePojos, false);
                    List<LogInfo.ObjectSnapshot> snapshots = sharePojos.stream().map(entitySharePojoHelper::toSnapshot).collect(Collectors.toList());
                    logService.logDataPermission(user, EventType.ADD, ActionType.Add, snapshots);
                    resultList.addAll(createResult);
                });
            }
        }
        return resultList;
    }

    /**
     * 基于来源共享规则API接口参数验证并且设置默认值
     *
     * @param user
     * @param sharePojo
     * @param allEntityIds
     * @return
     */
    private boolean checkEntitySharePojoInfo(User user, EntitySharePojo sharePojo, Set<String> allEntityIds) {
        if (sharePojo == null) {
            return true;
        }
        if (user == null || user.getTenantId() == null) {
            return false;
        }
        if (!user.getTenantId().equals(sharePojo.getTenantId())) {
            return false;
        }
        if (allEntityIds == null || !allEntityIds.contains(sharePojo.getEntityId())) {
            return false;
        }
        if (!USER_TYPE.equals(sharePojo.getShareType()) && !USER_GROUP_TYPE.equals(sharePojo.getShareType()) && !DEPART_TYPE.equals(sharePojo.getShareType()) &&
                !ROLE_TYPE.equals(sharePojo.getShareType()) && !ORGANIZATION_TYPE.equals(sharePojo.getShareType())) {
            return false;
        }
        if (!USER_TYPE.equals(sharePojo.getReceiveType()) && !USER_GROUP_TYPE.equals(sharePojo.getReceiveType()) &&
                !DEPART_TYPE.equals(sharePojo.getReceiveType()) && !ROLE_TYPE.equals(sharePojo.getReceiveType()) &&
                !ORGANIZATION_TYPE.equals(sharePojo.getShareType())) {
            return false;
        }
        if (sharePojo.getPermission() == null || (sharePojo.getPermission() != 1 && sharePojo.getPermission() != 2)) {
            return false;
        }
        if (sharePojo.getReceiveDeptCascade() == null) {
            if (DEPART_TYPE.equals(sharePojo.getReceiveType()) || ORGANIZATION_TYPE.equals(sharePojo.getReceiveType())) {
                return false;
            }
        } else if (sharePojo.getReceiveDeptCascade() == 1) {
            if (!DEPART_TYPE.equals(sharePojo.getReceiveType()) && !ORGANIZATION_TYPE.equals(sharePojo.getReceiveType())) {
                return false;
            }
        } else {
            if (sharePojo.getReceiveDeptCascade() != 0) {
                return false;
            }
        }
        if (sharePojo.getBasedType() != 0 && sharePojo.getBasedType() != 1 && sharePojo.getBasedType() != 2) {
            return false;
        }
        if (sharePojo.getBasedType() == 1 && !DEPART_TYPE.equals(sharePojo.getShareType())) {
            return false;
        }
        if (sharePojo.getBasedType() == 2 && !ORGANIZATION_TYPE.equals(sharePojo.getShareType())) {
            return false;
        }
        sharePojo.setAppId("CRM");
        sharePojo.setReceiveTenantId(null);
        sharePojo.setStatus(1);
        sharePojo.setDelFlag(null);
        return true;
    }

    @Override
    public boolean updateEntityShareRulePermission(User user, List<String> entityShareIds, int permission, boolean isApiOperation) {
        if (CollectionUtils.empty(entityShareIds)) {
            log.debug("updateEntityShareRulePermission 共享规则id列表是空");
            return true;
        }
        List<EntitySharePojo> shareContent = getEntityShareByIds(user, entityShareIds);

        shareContent.removeIf(x -> (x.getStatus() == null || UpdateEntityShareStatusModel.ENABLE_STATUS == x.getStatus()));

        if (CollectionUtils.empty(shareContent)) {
            log.warn("没有查到可更新状态的数据,ei:{}, userId:{}, ids:{}", user.getTenantId(), user.getUserId(), entityShareIds);
            return true;
        }

        Set<String> ids = shareContent.stream().map(EntitySharePojo::getId).collect(Collectors.toSet());
        UpdateEntitySharePermissionModel.Arg arg = new UpdateEntitySharePermissionModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setShareIds(ids);
        arg.setPermission(permission);
        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateEntitySharePermissionModel.Result result = proxy.batchUpdateEntitySharePermission(header, arg);

        if (!result.isSuccess()) {
            log.warn("batchUpdateEntitySharePermission failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        logDataPermission(user, shareContent, EventType.MODIFY, ActionType.Modify);

        return result.isSuccess();
    }

    @Override
    public List<String> addOrUpdateShareRuleGroups(User user, DataShareRuleGroup dataShareRuleGroup) {
        EntityShareGroupModel.Arg arg = EntityShareGroupModel.Arg
                .builder()
                .groupId(dataShareRuleGroup.getGroupId())
                .describeApiNames(dataShareRuleGroup.getDescribeApiNames())
                .permissionType(dataShareRuleGroup.getPermissionType())
                .sourceDeptIds(dataShareRuleGroup.getSourceDeptIds())
                .sourceEmployeeIds(dataShareRuleGroup.getSourceEmployeeIds())
                .sourceUserGroupIds(dataShareRuleGroup.getSourceUserGroupIds())
                .sourceRoleIds(dataShareRuleGroup.getSourceRoleIds())
                .targetDeptIds(dataShareRuleGroup.getTargetDeptIds())
                .targetEmployeeIds(dataShareRuleGroup.getTargetEmployeeIds())
                .targetUserGroupIds(dataShareRuleGroup.getTargetUserGroupIds())
                .targetRoleIds(dataShareRuleGroup.getTargetRoleIds())
                .basedType(dataShareRuleGroup.getBasedType())
                .receiveDeptCascade(dataShareRuleGroup.getReceiveDeptCascade())
                .returnList(true)
                .build();
        arg.setContext(buildAuthContext(user));
        EntityShareGroupModel.Result result;
        Map<String, String> header = RestUtils.buildHeaders(user);

        if (enableCheckEntityShareLicenseInfo(user.getTenantId())) {
            TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo
                    .builder()
                    .user(user)
                    .licenseService(licenseService)
                    .build()
                    .init(Sets.newHashSet(ModulePara.ModuleBiz.DATA_ROLE.getBizCode()));
            tenantLicenseInfo.checkEntityShareCount(findEntityShareGroupCount(header, arg));
        }

        if (StringUtils.isBlank(dataShareRuleGroup.getGroupId())) {
            result = proxy.createEntityShareGroup(header, arg);
            if (!result.isSuccess()) {
                log.error("createEntityShareGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            }
        } else {
            result = proxy.updateEntityShareGroup(header, arg);
            if (!result.isSuccess()) {
                log.error("updateEntityShareGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            }
        }

        return Lists.newArrayList();
    }

    @Override
    public int findEntityShareCount(User user, Set<String> queryAllEntityIds) {
        return findEntityShareCountByScope(user, queryAllEntityIds, null);
    }

    @Override
    public int findEntityShareCountByScope(User user, Set<String> queryAllEntityIds, Integer queryScope) {
        QueryShareRuleCount.Arg arg = QueryShareRuleCount.Arg
                .builder()
                .appId(DefObjConstants.PACKAGE_NAME_CRM)
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .entityIds(queryAllEntityIds)
                .queryScope(queryScope)
                .build();

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryShareRuleCount.Result result = proxy.queryEntityShareCount(header, arg);

        if (result != null && result.getErrCode() != 0) {
            log.warn("queryEntityShareCount failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return Objects.isNull(result) ? 0 : result.getResult();
    }

    public int findEntityShareGroupCount(Map<String, String> header, EntityShareGroupModel.Arg arg) {
        QueryEntityFieldShareCount.Result result = proxy.queryEntityShareGroupCount(header, arg);

        if (result != null && result.getErrCode() != 0) {
            log.warn("queryEntityShareGroupCount failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            if (header != null && enableDataAuthSupportMultiLanguageMessage(header.get("x-fs-ei"))) {
                throw new MetaDataBusinessException(getPromptMessageByCode(result.getErrCode(), result.getErrMessage()));
            } else {
                throw new MetaDataBusinessException(result.getErrMessage());
            }
        }

        return Objects.isNull(result) ? 0 : result.getResult();
    }

    private List<EntitySharePojo> queryEntityShare(User user, List<EntitySharePojo> shareList, Integer baseType) {
        List<String> entitys = Lists.newArrayList();
        List<Integer> shareTypes = Lists.newArrayList();
        List<String> shareIds = Lists.newArrayList();
        List<Integer> shareReceiveTypes = Lists.newArrayList();
        List<String> shareReceiveIds = Lists.newArrayList();
        List<String> shareReceiveTenantIds = Lists.newArrayList();

        shareList.forEach(entitySharePojo -> {
            entitys.add(entitySharePojo.getEntityId());
            shareTypes.add(entitySharePojo.getShareType());
            shareIds.add(entitySharePojo.getShareId());
            shareReceiveTypes.add(entitySharePojo.getReceiveType());
            shareReceiveIds.add(entitySharePojo.getReceiveId());
            shareReceiveTenantIds.add(entitySharePojo.getReceiveTenantId());
        });

        // 构造查询参数，查询当前已经存在的规则
        QueryEntityShareModel.Arg queryArg = QueryEntityShareModel.Arg.builder()
                .entitys(entitys)
                .sourceTypes(shareTypes)
                .sources(shareIds)
                .receiveTypes(shareReceiveTypes)
                .sharesOrReceives(shareReceiveIds)
                .basedType(ObjectUtils.isEmpty(baseType) ? Integer.valueOf(0) : baseType)
                .build();
        queryArg.setContext(buildAuthContext(user));
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareModel.Result queryResult = proxy.queryEntityShare(header, queryArg);
        return queryResult.getResult().getContent();
    }

    private List<String> batchCreateEntityShare(User user, List<EntitySharePojo> sharePojos, boolean isApiOperation) {
        CreateEntityShareModel.Arg arg = new CreateEntityShareModel.Arg();
        AuthContext authContext;
        if (isApiOperation) {
            authContext = buildApiAuthContext(user);
        } else {
            authContext = buildAuthContext(user);
        }
        arg.setContext(authContext);
        arg.setShareList(sharePojos);
        Map<String, String> header = RestUtils.buildHeaders(user);
        CreateEntityShareModel.Result result = proxy.createEntityShare(header, arg);
        if (!result.isSuccess()) {
            log.error("createEntityShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            if (isApiOperation) {
                throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
            }
        }

        return CollectionUtils.nullToEmpty(result.getResult());
    }

    private List<String> batchUpdateEntityShare(User user, List<EntitySharePojo> sharePojos, boolean isApiOperation) {
        BatchUpdateEntityShareModel.Arg arg = new BatchUpdateEntityShareModel.Arg();
        AuthContext authContext;
        if (isApiOperation) {
            authContext = buildApiAuthContext(user);
        } else {
            authContext = buildAuthContext(user);
        }
        arg.setContext(authContext);
        arg.setEntityShares(sharePojos);
        Map<String, String> header = RestUtils.buildHeaders(user);
        BatchUpdateEntityShareModel.Result result = proxy.batchUpdateEntityShare(header, arg);
        if (!result.isSuccess()) {
            log.error("batchUpdateEntityShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            if (isApiOperation) {
                throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
            }
        }
        return CollectionUtils.nullToEmpty(result.getResult());
    }

    @Override
    public boolean delShareRuleGroups(User user, Set<String> sharedRuleGroupIds) {

        if (CollectionUtils.empty(sharedRuleGroupIds)) {
            log.warn("delShareRuleGroups 共享规则组id列表是空");
            return true;
        }

        DelEntityShareGroupModel.Arg arg = new DelEntityShareGroupModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setGroupIds(sharedRuleGroupIds);
        arg.setReturnList(true);
        Map<String, String> header = RestUtils.buildHeaders(user);
        DelEntityShareGroupModel.Result result = proxy.delEntityShareGroup(header, arg);

        if (!result.isSuccess()) {
            log.warn("delEntityShareGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result.isSuccess();

    }

    @Override
    public boolean delShareRules(User user, List<String> sharedRuleIds) {

        if (CollectionUtils.empty(sharedRuleIds)) {
            log.warn("delShareRules 共享规则id列表是空");
            return true;
        }
        List<EntitySharePojo> shareContent = getEntityShareByIds(user, sharedRuleIds);
        if (CollectionUtils.empty(shareContent)) {
            log.warn("没有查到可更新的数据,ei:{}, userId:{}, ids:{}", user.getTenantId(), user.getUserId(), sharedRuleIds);
            return true;
        }
        DelEntityShareModel.Arg arg = new DelEntityShareModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setShareIds(sharedRuleIds);
        Map<String, String> header = RestUtils.buildHeaders(user);
        DelEntityShareModel.Result result = proxy.delEntityShare(header, arg);

        if (!result.isSuccess()) {
            log.warn("delEntityShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }
        logDataPermission(user, shareContent, EventType.DELETE, ActionType.Delete);
        return result.isSuccess();

    }

    private void logDataPermission(User user, List<EntitySharePojo> shareContent, EventType eventType, ActionType actionType) {
        EntitySharePojo.EntitySharePojoHelper helper = dataSharingProcessor.processorByEntitySharePojo(shareContent, user);
        List<LogInfo.ObjectSnapshot> snapshots = shareContent.stream().map(helper::toSnapshot).collect(Collectors.toList());
        logService.logDataPermission(user, eventType, actionType, snapshots);
    }

    private List<EntitySharePojo> getEntityShareByIds(User user, List<String> sharedRuleIds) {
        QueryEntityShareModel.Arg queryArg = QueryEntityShareModel.Arg.createByIds(sharedRuleIds, buildAuthContext(user));
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareModel.Result entityShare = proxy.queryEntityShare(header, queryArg);
        if (!entityShare.isSuccess()) {
            log.error("queryEntityShare failed,arg:{},result:{}", JSON.toJSONString(queryArg), JSON.toJSONString(entityShare));
        }
        return CollectionUtils.nullToEmpty(entityShare.getContent());
    }

    private List<EntitySharePojo> getEntityShareByGroupIds(User user, Set<String> groupIds) {
        QueryEntityShareModel.Arg queryArg = QueryEntityShareModel.Arg.createByGroupIds(groupIds, buildAuthContext(user));
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareModel.Result entityShare = proxy.queryEntityShareByGroupIds(header, queryArg);
        if (!entityShare.isSuccess()) {
            log.error("getEntityShareByGroupIds failed,arg:{},result:{}", JSON.toJSONString(queryArg), JSON.toJSONString(entityShare));
        }
        return CollectionUtils.nullToEmpty(entityShare.getContent());
    }

    private List<EntityShareGroupPojo> getEntityShareGroupByIds(User user, List<String> sharedRuleGroupIds) {
        QueryEntityShareGroupModel.Arg queryArg = QueryEntityShareGroupModel.Arg.createByIds(sharedRuleGroupIds, buildAuthContext(user));
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareGroupModel.Result entityShareGroup = proxy.queryEntityShareGroup(header, queryArg);
        if (!entityShareGroup.isSuccess()) {
            log.error("queryEntityShareGroup failed,arg:{},result:{}", JSON.toJSONString(queryArg), JSON.toJSONString(entityShareGroup));
        }
        return CollectionUtils.nullToEmpty(entityShareGroup.getContent());
    }

    @Override
    public boolean enableOrDisableShareRule(User user, List<String> sharedRuleIds, int status) {
        if (CollectionUtils.empty(sharedRuleIds)) {
            log.debug("enableOrDisableShareRule 共享规则id列表是空");
            return true;
        }
        List<EntitySharePojo> shareContent = getEntityShareByIds(user, sharedRuleIds);
        shareContent.removeIf(x -> Objects.equals(status, x.getStatus()));
        if (CollectionUtils.empty(shareContent)) {
            log.warn("没有查到可更新状态的数据,ei:{}, userId:{}, ids:{}", user.getTenantId(), user.getUserId(), sharedRuleIds);
            return true;
        }
        List<String> ids = shareContent.stream().map(EntitySharePojo::getId).collect(Collectors.toList());
        UpdateEntityShareStatusModel.Arg arg = new UpdateEntityShareStatusModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setShareIds(ids);
        arg.setStatus(status);
        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateEntityShareStatusModel.Result result = proxy.updateEntityShareStatus(header, arg);

        if (!result.isSuccess()) {
            log.warn("updateEntityShareStatus failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        switch (status) {
            case UpdateEntityShareStatusModel.DISABLE_STATUS:
                logDataPermission(user, shareContent, EventType.DISABLE, ActionType.Disable);
                break;
            case UpdateEntityShareStatusModel.ENABLE_STATUS:
                logDataPermission(user, shareContent, EventType.ENABLE, ActionType.Enable);
                break;
            default:
                log.warn("status illegal, ei:{}, userId:{}, status:{}", user.getTenantId(), user.getUserId(), status);
        }

        return result.isSuccess();
    }

    @Override
    public boolean enableOrDisableShareRuleGroup(User user, List<String> sharedRuleGroupIds, int status) {
        if (CollectionUtils.empty(sharedRuleGroupIds)) {
            log.debug("enableOrDisableShareRuleGroup 共享规则组id列表是空");
            return true;
        }
        List<EntityShareGroupPojo> shareContent = getEntityShareGroupByIds(user, sharedRuleGroupIds);
        shareContent.removeIf(x -> Objects.equals(status, x.getStatus()));
        if (CollectionUtils.empty(shareContent)) {
            log.warn("没有查到可更新状态的数据,ei:{}, userId:{}, ids:{}", user.getTenantId(), user.getUserId(), sharedRuleGroupIds);
            return true;
        }

        Set<String> ids = shareContent.stream().map(EntityShareGroupPojo::getId).collect(Collectors.toSet());
        UpdateEntityShareGroupStatusModel.Arg arg = new UpdateEntityShareGroupStatusModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setGroupIds(ids);
        arg.setStatus(status);
        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateEntityShareGroupStatusModel.Result result = proxy.updateEntityShareGroupStatus(header, arg);

        if (!result.isSuccess()) {
            log.warn("updateEntityShareGroupStatus failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result.isSuccess();
    }

    @Override
    public CreateDimensionRuleGroupModel.Result createDimensionRuleGroup(User user,
                                                                         String entityId,
                                                                         String ruleParse,
                                                                         int ruleType,
                                                                         int permission,
                                                                         String remark,
                                                                         List<DimensionRulePojo> rules,
                                                                         List<DimensionRuleGroupReceivePojo> receives) {

        return createDimensionRuleGroupInfo(user, entityId, ruleParse, ruleType, permission, remark, Maps.newHashMap(), rules, receives);
    }

    @Override
    public CreateDimensionRuleGroupModel.Result createDimensionRuleGroupInfo(User user,
                                                                             String entityId,
                                                                             String ruleParse,
                                                                             int ruleType,
                                                                             int permission,
                                                                             String remark,
                                                                             Map<String, String> properties,
                                                                             List<DimensionRulePojo> rules,
                                                                             List<DimensionRuleGroupReceivePojo> receives) {
        DimensionRuleGroupPojo dimensionRuleGroupPojo = new DimensionRuleGroupPojo();
        dimensionRuleGroupPojo.setEntityId(entityId);
        dimensionRuleGroupPojo.setRuleParse(ruleParse);
        dimensionRuleGroupPojo.setRuleType(ruleType);
        dimensionRuleGroupPojo.setPermission(permission);
        dimensionRuleGroupPojo.setRemark(remark);
        dimensionRuleGroupPojo.setRules(rules);
        dimensionRuleGroupPojo.setReceives(receives);

        CreateDimensionRuleGroupModel.Arg arg = new CreateDimensionRuleGroupModel.Arg();
        AuthContext authContext = buildAuthContext(user);

        putContextProperties(authContext, properties);

        arg.setContext(authContext);
        arg.setDimensionRuleGroup(dimensionRuleGroupPojo);

        Map<String, String> header = RestUtils.buildHeaders(user);
        CreateDimensionRuleGroupModel.Result ret = proxy.createDimensionRuleGroup(header, arg);

        if (!ret.isSuccess()) {
            log.warn("createDimensionRuleGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(ret));
            if ("true".equals(getContextProperties(authContext, "isApiRequest"))) {
                throw new MetaDataBusinessException(ret.getErrMessage());
            } else {
                throw new MetaDataBusinessException(getPromptMessageByCode(ret.getErrCode(), ret.getErrMessage()));
            }
        }

        return ret;
    }

    @Override
    public UpdateDimensionRuleGroupModel.Result updateDimensionRuleGroup(User user,
                                                                         String ruleCode,
                                                                         String ruleParse,
                                                                         int permission,
                                                                         String remark,
                                                                         List<DimensionRulePojo> rules,
                                                                         List<DimensionRuleGroupReceivePojo> receives) {
        return updateDimensionRuleGroupInfo(user, ruleCode, ruleParse, permission, remark, Maps.newHashMap(), rules, receives);
    }

    @Override
    public UpdateDimensionRuleGroupModel.Result updateDimensionRuleGroupInfo(User user,
                                                                             String ruleCode,
                                                                             String ruleParse,
                                                                             int permission,
                                                                             String remark,
                                                                             Map<String, String> properties,
                                                                             List<DimensionRulePojo> rules,
                                                                             List<DimensionRuleGroupReceivePojo> receives) {

        DimensionRuleGroupPojo dimensionRuleGroupPojo = new DimensionRuleGroupPojo();
        dimensionRuleGroupPojo.setRuleCode(ruleCode);
        dimensionRuleGroupPojo.setRuleParse(ruleParse);
        dimensionRuleGroupPojo.setPermission(permission);
        dimensionRuleGroupPojo.setRemark(remark);
        dimensionRuleGroupPojo.setRules(rules);
        dimensionRuleGroupPojo.setReceives(receives);

        UpdateDimensionRuleGroupModel.Arg arg = new UpdateDimensionRuleGroupModel.Arg();
        AuthContext authContext = buildAuthContext(user);

        putContextProperties(authContext, properties);

        arg.setContext(authContext);
        arg.setDimensionRuleGroup(dimensionRuleGroupPojo);

        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateDimensionRuleGroupModel.Result ret = proxy.updateDimensionRuleGroup(header, arg);

        if (!ret.isSuccess()) {
            log.warn("updateDimensionRuleGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(ret));
            if ("true".equals(getContextProperties(authContext, "isApiRequest"))) {
                throw new MetaDataBusinessException(ret.getErrMessage());
            } else {
                throw new MetaDataBusinessException(getPromptMessageByCode(ret.getErrCode(), ret.getErrMessage()));
            }
        }

        return ret;
    }

    @Override
    public QueryDimensionRuleGroupModel.Result queryDimensionRuleGroup(User user,
                                                                       Set<String> receiveIds,
                                                                       Integer receiveType,
                                                                       String receiveTenantId,
                                                                       int permissionType,
                                                                       Map<String, Long> createTimeRange,
                                                                       Map<String, Long> modifyTimeRange,
                                                                       int sortType,
                                                                       int sortOrder,
                                                                       int pageNumber,
                                                                       int pageSize) {
        return queryDimensionRuleGroupInfo(user, receiveIds, receiveType, receiveTenantId, permissionType, Maps.newHashMap(), createTimeRange, modifyTimeRange, sortType, sortOrder, pageNumber, pageSize);
    }

    @Override
    public QueryDimensionRuleGroupModel.Result queryDimensionRuleGroupInfo(User user,
                                                                           Set<String> receiveIds,
                                                                           Integer receiveType,
                                                                           String receiveTenantId,
                                                                           int permissionType,
                                                                           Map<String, String> properties,
                                                                           Map<String, Long> createTimeRange,
                                                                           Map<String, Long> modifyTimeRange,
                                                                           int sortType,
                                                                           int sortOrder,
                                                                           int pageNumber,
                                                                           int pageSize) {

        QueryDimensionRuleGroupModel.Arg arg = new QueryDimensionRuleGroupModel.Arg();

        AuthContext authContext = buildAuthContext(user);

        putContextProperties(authContext, properties);

        arg.setContext(authContext);

        arg.setReceiveIds(receiveIds);
        arg.setReceiveType(receiveType);
        arg.setReceiveTenantId(receiveTenantId);
        arg.setPermissionType(permissionType);
        arg.setCreateTimeRange(createTimeRange);
        arg.setModifyTimeRange(modifyTimeRange);
        arg.setSortType(sortType);
        arg.setSortOrder(sortOrder);
        arg.setPageInfo(buildPageInfo(pageSize, pageNumber));

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryDimensionRuleGroupModel.Result result = proxy.queryDimensionRuleGroupList(header, arg);

        if (!result.isSuccess()) {
            log.warn("queryDimensionRuleGroupList failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            if ("true".equals(getContextProperties(authContext, "isApiRequest"))) {
                throw new MetaDataBusinessException(result.getErrMessage());
            } else {
                throw new MetaDataBusinessException(getPromptMessageByCode(result.getErrCode(), result.getErrMessage()));
            }
        }

        return result;
    }

    @Override
    public QueryDimensionRuleCodeListModel.Result queryDimensionRuleCodeList(User user, Set<String> receiveIds, Integer receiveType, String receiveTenantId) {
        QueryDimensionRuleCodeListModel.Arg arg = new QueryDimensionRuleCodeListModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setReceiveIds(receiveIds);
        arg.setReceiveType(receiveType);
        arg.setReceiveTenantId(receiveTenantId);

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryDimensionRuleCodeListModel.Result ret = proxy.queryDimensionRuleCodeList(header, arg);

        if (!ret.isSuccess()) {
            log.warn("queryDimensionRuleCodeList failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(ret));
            throw new MetaDataBusinessException(getPromptMessageByCode(ret.getErrCode(), ret.getErrMessage()));
        }

        return ret;
    }

    @Override
    public boolean deleteDimensionRuleGroup(User user, Set<String> ruleCodes) {

        if (CollectionUtils.empty(ruleCodes)) {
            log.debug("deleteDimensionRuleGroup ruleCodes is empty");
            return true;
        }
        DeleteDimensionRuleGroupModel.Arg arg = new DeleteDimensionRuleGroupModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setRuleCodes(ruleCodes);

        Map<String, String> header = RestUtils.buildHeaders(user);
        DeleteDimensionRuleGroupModel.Result result = proxy.batchDeleteDimensionGroup(header, arg);

        if (!result.isSuccess()) {
            log.warn("deleteDimensionRuleGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throw new MetaDataBusinessException(getPromptMessageByCode(result.getErrCode(), result.getErrMessage()));
        }

        return result.isSuccess();
    }

    @Override
    public boolean deleteDimensionRuleGroupInfo(User user, Set<String> ruleCodes, Map<String, String> properties) {
        if (CollectionUtils.empty(ruleCodes)) {
            log.debug("deleteDimensionRuleGroup ruleCodes is empty");
            return true;
        }
        DeleteDimensionRuleGroupModel.Arg arg = new DeleteDimensionRuleGroupModel.Arg();

        AuthContext authContext = buildAuthContext(user);
        putContextProperties(authContext, properties);
        arg.setContext(authContext);
        arg.setRuleCodes(ruleCodes);

        Map<String, String> header = RestUtils.buildHeaders(user);
        DeleteDimensionRuleGroupModel.Result result = proxy.batchDeleteDimensionGroup(header, arg);

        if (!result.isSuccess()) {
            log.warn("deleteDimensionRuleGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            if ("true".equals(getContextProperties(authContext, "isApiRequest"))) {
                throw new MetaDataBusinessException(result.getErrMessage());
            } else {
                throw new MetaDataBusinessException(getPromptMessageByCode(result.getErrCode(), result.getErrMessage()));
            }
        }

        return result.isSuccess();
    }

    @Override
    public QueryEntityShareModel.Result getEntitySharePojoResult(GetShareRules.Arg arg, List<String> describeApiNameList, User user) {
        QueryEntityShareModel.Arg queryArg = new QueryEntityShareModel.Arg();
        queryArg.setContext(buildAuthContext(user));
        if (arg.getPermissionType() == -1) {
            queryArg.setPermission(null);
        } else {
            queryArg.setPermission(arg.getPermissionType());
        }

        if (arg.getStatus() == -1) {
            queryArg.setStatus(null);
        } else {
            queryArg.setStatus(arg.getStatus());
        }

        Integer keywordScope = arg.getKeywordScope();
        if (!Strings.isNullOrEmpty(arg.getKeyword())) {
            Set<String> keywordList = Sets.newHashSet(arg.getKeyword());
            Map<Integer, Set<String>> map = Maps.newHashMap();
            map.put(arg.getKeywordType(), keywordList);
            if (keywordScope == -1) {
                queryArg.setSharesOrReceivesId(map);
            } else if (keywordScope == 1) {
                queryArg.setSharesId(map);
            } else if (keywordScope == 2) {
                queryArg.setReceivesId(map);
            }
        }
        queryArg.setEntitys(describeApiNameList);
        queryArg.setPage(buildPageInfo(arg.getPageSize(), arg.getPageNumber()));
        // 数据来源和共享范围
        Optional.ofNullable(arg.getReceives()).ifPresent(receives -> receives.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));
        Optional.ofNullable(arg.getSources()).ifPresent(receives -> receives.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));
        queryArg.setReceivesId(mergeMap(arg.getReceives(), queryArg.getReceivesId()));
        queryArg.setSharesId(mergeMap(arg.getSources(), queryArg.getSharesId()));
        queryArg.setOutReceive(arg.getOutReceive());
        queryArg.setBasedType(arg.getBasedType());
        queryArg.setEntityShareType(arg.getEntityShareType());

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareModel.Result result = proxy.queryEntityShare(header, queryArg);
        if (!result.isSuccess()) {
            log.warn("queryEntityShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result;
    }

    @Override
    public QueryAllEntityShareModel.Result getAllEntitySharePojoList(GetAllShareRules.Arg arg, List<String> describeApiNameList, User user) {
        QueryAllEntityShareModel.Arg queryArg = new QueryAllEntityShareModel.Arg();
        queryArg.setContext(buildAuthContext(user));
        if (arg.getPermissionType() == -1) {
            queryArg.setPermission(null);
        } else {
            queryArg.setPermission(arg.getPermissionType());
        }

        if (arg.getStatus() == -1) {
            queryArg.setStatus(null);
        } else {
            queryArg.setStatus(arg.getStatus());
        }

        Integer keywordScope = arg.getKeywordScope();
        if (!Strings.isNullOrEmpty(arg.getKeyword())) {
            Set<String> keywordList = Sets.newHashSet(arg.getKeyword());
            Map<Integer, Set<String>> map = Maps.newHashMap();
            map.put(arg.getKeywordType(), keywordList);
            if (keywordScope == -1) {
                queryArg.setSharesOrReceivesId(map);
            } else if (keywordScope == 1) {
                queryArg.setSharesId(map);
            } else if (keywordScope == 2) {
                queryArg.setReceivesId(map);
            }
        }
        queryArg.setEntitys(describeApiNameList);
        queryArg.setPage(buildPageInfo(arg.getPageSize(), arg.getPageNumber()));
        // 数据来源和共享范围
        Optional.ofNullable(arg.getReceives()).ifPresent(receives -> receives.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));
        Optional.ofNullable(arg.getSources()).ifPresent(receives -> receives.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));
        queryArg.setReceivesId(mergeMap(arg.getReceives(), queryArg.getReceivesId()));
        queryArg.setSharesId(mergeMap(arg.getSources(), queryArg.getSharesId()));
        queryArg.setCreateIds(arg.getCreateIds());
        queryArg.setModifyIds(arg.getModifyIds());
        queryArg.setCreateTimeRange(arg.getCreateTimeRange());
        queryArg.setModifyTimeRange(arg.getModifyTimeRange());
        queryArg.setOutReceive(arg.getOutReceive());
        queryArg.setBasedType(arg.getBasedType());
        queryArg.setEntityShareType(arg.getEntityShareType());

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryAllEntityShareModel.Result result = proxy.queryAllEntityShareList(header, queryArg);
        if (!result.isSuccess()) {
            log.warn("queryAllEntityShareList failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result;
    }

    @Override
    public QueryAllEntityShareModel.Result getAllEntitySharePojoListByIds(GetAllShareRules.Arg arg, List<String> describeApiNameList, User user) {
        QueryAllEntityShareModel.Arg queryArg = new QueryAllEntityShareModel.Arg();
        queryArg.setContext(buildAuthContext(user));
        queryArg.setEntitys(describeApiNameList);
        queryArg.setOutReceive(arg.getOutReceive());

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryAllEntityShareModel.Result result = proxy.queryAllEntityShareList(header, queryArg);
        if (!result.isSuccess()) {
            log.warn("getAllEntitySharePojoListByIds failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result;
    }

    @Override
    public QueryEntityShareGroupModel.Result getEntityShareGroupPojoResult(GetShareRuleGroups.Arg arg, List<String> describeApiNameList, User user) {
        QueryEntityShareGroupModel.Arg queryArg = new QueryEntityShareGroupModel.Arg();
        queryArg.setContext(buildAuthContext(user));
        if (arg.getPermissionType() == null || arg.getPermissionType() == -1) {
            queryArg.setPermission(null);
        } else {
            queryArg.setPermission(arg.getPermissionType());
        }

        if (arg.getStatus() == null || arg.getStatus() == -1) {
            queryArg.setStatus(null);
        } else {
            queryArg.setStatus(arg.getStatus());
        }

        queryArg.setEntityIds(describeApiNameList);
        queryArg.setPage(buildPageInfo(arg.getPageSize(), arg.getPageNumber()));
        // 数据来源和共享范围
        Optional.ofNullable(arg.getReceives()).ifPresent(receives -> receives.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));
        Optional.ofNullable(arg.getSources()).ifPresent(receives -> receives.entrySet().removeIf(n -> CollectionUtils.empty(n.getValue())));
        queryArg.setReceiveIds(arg.getReceives());
        queryArg.setShareIds(arg.getSources());
        queryArg.setOutReceive(arg.getOutReceive());
        queryArg.setBasedType(arg.getBasedType());
        queryArg.setEntityShareType(arg.getEntityShareType());

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareGroupModel.Result result = proxy.queryEntityShareGroup(header, queryArg);
        if (!result.isSuccess()) {
            log.warn("queryEntityShareGroup failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }
        return result;
    }

    private Map<Integer, Set<String>> mergeMap(Map<Integer, Set<String>> sources, Map<Integer, Set<String>> sharesId) {
        if (CollectionUtils.empty(sources)) {
            return sharesId;
        }
        if (CollectionUtils.empty(sharesId)) {
            return sources;
        }
        // 兼容旧有的keyWord
        sharesId.forEach((key, value) -> {
            if (CollectionUtils.notEmpty(value)) {
                Set<String> sourceSet = sources.getOrDefault(key, Sets.newHashSet());
                sourceSet.addAll(value);
                sources.put(key, sourceSet);
            }
        });
        return sources;
    }

    private BasePageInfoDataPrivilege buildPageInfo(int pageSize, int pageNumber) {
        BasePageInfoDataPrivilege page = new BasePageInfoDataPrivilege();
        page.setPageSize(pageSize);
        page.setCurrentPage(pageNumber);
        return page;
    }

    @Override
    public QueryTemporaryPrivilegeList.Result getTemporaryPrivilegeList(User user, String describeApiName, String dataId,
                                                                        Integer pageSize, Integer pageNumber,
                                                                        String userId, String scene) {
        QueryTemporaryPrivilegeList.Arg queryArg = new QueryTemporaryPrivilegeList.Arg();
        PageInfo pageInfo = new PageInfo();
        queryArg.setContext(buildAuthContext(user));
        pageInfo.setCurrentPage(pageNumber);
        pageInfo.setPageSize(pageSize);
        queryArg.setPageInfo(pageInfo);
        queryArg.setEntityId(describeApiName);
        //queryArg.setPermission(2);
        //queryArg.setScene("workflow");
        if (!Strings.isNullOrEmpty(dataId)) {
            queryArg.setDataIds(Sets.newHashSet(dataId));
        }
        if (!Strings.isNullOrEmpty(userId)) {
            queryArg.setOwners(Sets.newHashSet(userId));
        }
        if (!Strings.isNullOrEmpty(scene)) {
            queryArg.setScene(scene);
        }
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryTemporaryPrivilegeList.Result result = proxy.queryTemporaryPrivilegeList(header, queryArg);
        if (Objects.isNull(result)) {
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("queryTemporaryPrivilegeList failed,arg:{},result:{}", JSON.toJSONString(queryArg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        result.fillSceneName();
        return result;

    }

    @Override
    public UpdateTemporaryRights.Result updateTemporaryRights(User user, String describeApiName, String dataId, Set<String> ownerId, String scene) {
        UpdateTemporaryRights.Arg updateArg = new UpdateTemporaryRights.Arg();
        updateArg.setContext(buildAuthContext(user));
        updateArg.setEntityId(describeApiName);
        updateArg.setDataId(dataId);
        updateArg.setOwners(ownerId);
        updateArg.setScene(scene);
        Map<String, String> header = RestUtils.buildHeaders(user);
        UpdateTemporaryRights.Result result = proxy.updateTemporaryRights(header, updateArg);

        if (!result.isSuccess()) {
            log.warn("updateTemporaryRights failed,arg:{},result:{}", JSON.toJSONString(updateArg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result;
    }

    @Override
    public DeleteTemporaryRights.Result deleteTemporaryRights(User user) {
        DeleteTemporaryRights.Arg deleteArg = new DeleteTemporaryRights.Arg();
        deleteArg.setContext(buildAuthContext(user));
        Map<String, String> header = RestUtils.buildHeaders(user);
        DeleteTemporaryRights.Result result = proxy.deleteTemporaryRights(header, deleteArg);

        if (!result.isSuccess()) {
            log.warn("deleteTemporaryRights failed,arg:{},result:{}", JSON.toJSONString(deleteArg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result;
    }

    public DeleteTemporaryRights.Result deleteTemporaryRights(User user, String sourceId, String dataId, String owner, String apiName) {
        DeleteTemporaryRights.Arg deleteArg = new DeleteTemporaryRights.Arg();
        deleteArg.setContext(buildAuthContext(user));
        deleteArg.setSourceId(sourceId);
        deleteArg.setDataId(dataId);
        deleteArg.setOwner(owner);
        deleteArg.setEntityId(apiName);
        Map<String, String> header = RestUtils.buildHeaders(user);
        DeleteTemporaryRights.Result result = proxy.deleteTemporaryRightsData(header, deleteArg);
        if (!result.isSuccess()) {
            log.warn("deleteTemporaryRights failed,arg:{},result:{}", JSON.toJSONString(deleteArg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }
        return result;
    }

    @Override
    public DeleteTemporaryRights.Result batchDeleteTemporaryRights(User user, Set<String> temporaryRightsIds) {
        BatchDeleteTemporaryRights.Arg deleteArg = new BatchDeleteTemporaryRights.Arg();
        deleteArg.setContext(buildAuthContext(user));
        deleteArg.setTemporaryRightsIds(temporaryRightsIds);
        Map<String, String> header = RestUtils.buildHeaders(user);
        DeleteTemporaryRights.Result result = proxy.batchDeleteTemporaryRightsData(header, deleteArg);
        if (!result.isSuccess()) {
            log.warn("batchDeleteTemporaryRights failed,arg:{},result:{}", JSON.toJSONString(deleteArg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }
        return result;
    }

    @Override
    public BatchDeleteTemporaryPrivilegeModel.Result batchDeleteTemporaryPrivilege(User user, BatchDeleteTemporaryPrivilege.Arg arg, String lang) {
        BatchDeleteTemporaryPrivilegeModel.Arg deleteArg = new BatchDeleteTemporaryPrivilegeModel.Arg();

        deleteArg.setTenantId(user.getTenantId());
        deleteArg.setOperatorId(user.getUserId());
        deleteArg.setLang(lang);
        if (CollectionUtils.notEmpty(arg.getUserIds())) {
            deleteArg.setUserIds(Sets.newHashSet(arg.getUserIds()));
        }
        if (CollectionUtils.notEmpty(arg.getOutUserIds())) {
            deleteArg.setOutUserIds(Sets.newHashSet(arg.getOutUserIds()));
        }
        if (CollectionUtils.notEmpty(arg.getDescribeApiNames())) {
            deleteArg.setDescribeApiNames(Sets.newHashSet(arg.getDescribeApiNames()));
        }
        if (CollectionUtils.notEmpty(arg.getScenes())) {
            deleteArg.setScenes(Sets.newHashSet(arg.getScenes()));
        }
        if (CollectionUtils.notEmpty(arg.getDataIds())) {
            deleteArg.setDataIds(Sets.newHashSet(arg.getDataIds()));
        }
        if (MapUtils.isNotEmpty(arg.getCreateTimeRange())) {
            deleteArg.setCreateTimeRange(arg.getCreateTimeRange());
        }
        if (MapUtils.isNotEmpty(arg.getExpiryTimeRange())) {
            deleteArg.setExpiryTimeRange(arg.getExpiryTimeRange());
        }
        Map<String, String> header = RestUtils.buildHeaders(user);

        BatchDeleteTemporaryPrivilegeModel.Result result = proxy.createBatchDeleteTemporaryRightsTask(header, deleteArg);
        if (!result.isSuccess()) {
            log.warn("batchDeleteTemporaryPrivilege failed,arg:{},result:{}", JSON.toJSONString(deleteArg), JSON.toJSONString(result));
            throw new MetaDataBusinessException(result.getErrMessage());
        }
        return result;
    }

    @Override
    public QueryTemporaryRightsList.Result queryTemporaryRights(User user, String describeApiName, List<String> entityIdList, String dataId, Integer pageSize,
                                                                Integer pageNumber, String userId, String scene) {
        return queryTemporaryRightsByTimeRange(user, describeApiName, entityIdList, dataId, pageSize, pageNumber, userId, scene, null, null);
    }

    @Override
    public QueryTemporaryRightsList.Result queryTemporaryRightsByTimeRange(User user,
                                                                           String describeApiName,
                                                                           List<String> entityIdList,
                                                                           String dataId,
                                                                           Integer pageSize,
                                                                           Integer pageNumber,
                                                                           String userId,
                                                                           String scene,
                                                                           List<QueryTemporaryRightsTimeRange> createTimeRange,
                                                                           List<QueryTemporaryRightsTimeRange> expiryTimeRange) {
        QueryTemporaryRightsList.Arg arg = QueryTemporaryRightsList.Arg.builder().scene(scene).owner(userId).pageInfo(PageInfo.builder().pageSize(pageSize).currentPage(pageNumber).build()).build();

        if (enableTemporaryPrivilegeListSupportTimeRange(user.getTenantId())) {
            if (CollectionUtils.notEmpty(createTimeRange)) {
                arg.setCreateTimeRange(convertTimeRangeMapByList(createTimeRange));
            }

            if (CollectionUtils.notEmpty(expiryTimeRange)) {
                arg.setExpiryTimeRange(convertTimeRangeMapByList(expiryTimeRange));
            }
        }

        if (dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(user.getTenantId())) {
            arg.setEntityIdList(entityIdList);
        } else {
            arg.setEntityId(describeApiName);
        }
        arg.setContext(buildAuthContext(user));

        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryTemporaryRightsList.Result result = proxy.queryTemporaryRights(header, arg);
        if (Objects.isNull(result)) {
            return null;
        }
        if (!result.isSuccess()) {
            log.warn("queryTemporaryRights failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        result.fillSceneName();
        return result;
    }

    private Map<String, Map<String, Long>> convertTimeRangeMapByList(List<QueryTemporaryRightsTimeRange> timeRangeList) {
        Map<String, Map<String, Long>> result = Maps.newHashMap();

        for (QueryTemporaryRightsTimeRange timeRange : timeRangeList) {
            String operator = timeRange.getOperator();
            if (StringUtils.isBlank(operator)) {
                continue;
            }

            Long queryTime = timeRange.getQueryTime();
            Long startTime = timeRange.getStartTime();
            Long endTime = timeRange.getEndTime();

            Map<String, Long> timeMap = Maps.newHashMap();

            if ("IS".equals(operator)) {
                timeMap.put("queryTime", queryTime);
            } else if ("ISN".equals(operator)) {
                timeMap.put("queryTime", queryTime);
            } else if ("EQ".equals(operator)) {
                if (queryTime == null) {
                    continue;
                }
                timeMap.put("queryTime", queryTime);
            } else if ("N".equals(operator)) {
                if (queryTime == null) {
                    continue;
                }
                timeMap.put("queryTime", queryTime);
            } else if ("LT".equals(operator)) {
                if (queryTime == null) {
                    continue;
                }
                timeMap.put("endTime", queryTime);
            } else if ("GT".equals(operator)) {
                if (queryTime == null) {
                    continue;
                }
                timeMap.put("startTime", queryTime);
            } else if ("LTE".equals(operator)) {
                if (queryTime == null) {
                    continue;
                }
                timeMap.put("endTime", queryTime);
            } else if ("GTE".equals(operator)) {
                if (queryTime == null) {
                    continue;
                }
                timeMap.put("startTime", queryTime);
            } else if ("BETWEEN".equals(operator)) {
                if (startTime == null || endTime == null || startTime >= endTime) {
                    continue;
                }
                timeMap.put("startTime", startTime);
                timeMap.put("endTime", endTime);
            } else {
                continue;
            }

            result.put(operator, timeMap);
        }
        return result;
    }

    @Override
    public void enableTemporaryRights(User user, String describeApiName, boolean enable, String appId) {
        // 呼叫中心不需要同步历史数据
        if (CALL_CENTER.equals(appId)) {
            return;
        }
        TemporaryRights.EnableTemporaryRights enableTemporaryRights = TemporaryRights.EnableTemporaryRights.builder()
                .tenantId(user.getTenantId()).userId(user.getUserId()).appId(DefObjConstants.PACKAGE_NAME_CRM)
                .enable(enable).entityId(describeApiName).build();
        MessageContent<TemporaryRights.EnableTemporaryRights> messageContent = MessageContent.of(ENABLE, enableTemporaryRights);
        caTemporaryPrivilegeMQSender.sendMessage(JSON.toJSONString(messageContent).getBytes(), user.getTenantId().hashCode());
        log.info("sendMessage message=>{}, operate=>{}", JSON.toJSONString(messageContent), ENABLE);

    }

    @Override
    public void asyncUpdateTemporaryRights(User user, String describeApiName, TemporaryRights.RuleConfig beforeRule,
                                           TemporaryRights.RuleConfig afterRule, String appId) {
        if (CALL_CENTER.equals(appId)) {
            return;
        }
        TemporaryRights.UpdateTemporaryRights updateTemporaryRights = TemporaryRights.UpdateTemporaryRights.builder()
                .tenantId(user.getTenantId()).userId(user.getUserId()).appId(DefObjConstants.PACKAGE_NAME_CRM)
                .entityId(describeApiName).beforeRule(beforeRule).afterRule(afterRule).build();
        MessageContent<TemporaryRights.UpdateTemporaryRights> messageContent = MessageContent.of(UPDATE, updateTemporaryRights);
        caTemporaryPrivilegeMQSender.sendMessage(JSON.toJSONString(messageContent).getBytes(), user.getTenantId().hashCode());
        log.info("sendMessage message=>{}, operate=>{}", JSON.toJSONString(messageContent), UPDATE);
    }

    @Override
    public void asyncDeleteTemporaryRights(User user, String describeApiName, String appId) {
        if (CALL_CENTER.equals(appId)) {
            return;
        }
        TemporaryRights.DeleteTemporaryRights deleteTemporaryRights = TemporaryRights.DeleteTemporaryRights.builder()
                .tenantId(user.getTenantId()).userId(user.getUserId()).appId(DefObjConstants.PACKAGE_NAME_CRM)
                .entityId(describeApiName).build();
        MessageContent<TemporaryRights.DeleteTemporaryRights> messageContent = MessageContent.of(DELETE, deleteTemporaryRights);
        caTemporaryPrivilegeMQSender.sendMessage(JSON.toJSONString(messageContent).getBytes(), user.getTenantId().hashCode());
        log.info("sendMessage message=>{}, operate=>{}", JSON.toJSONString(messageContent), DELETE);
    }


    public int findFieldShareCount(User user) {
        QueryEntityFieldShareCount.Arg arg = QueryEntityFieldShareCount.Arg.builder()
                .appId(DefObjConstants.PACKAGE_NAME_CRM)
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .build();
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityFieldShareCount.Result result = proxy.queryEntityFieldShareCount(header, arg);

        if (result != null && result.getErrCode() != 0) {
            log.warn("queryEntityFieldShareCount failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return Objects.isNull(result) ? 0 : result.getResult();
    }

    private AuthContext buildAuthContext(User user) {
        return AuthContext.buildByUser(user);
    }

    private AuthContext buildApiAuthContext(User user) {
        AuthContext authContext = buildAuthContext(user);
        if (authContext.getProperties() != null) {
            authContext.getProperties().put("isApiRequest", "true");
        } else {
            Map<String, String> currentProperties = Maps.newHashMap();
            currentProperties.put("isApiRequest", "true");
            authContext.setProperties(currentProperties);
        }
        return authContext;
    }

    private String getContextProperties(AuthContext context, String propertyKey) {
        if (context == null || context.getProperties() == null || StringUtils.isBlank(propertyKey)) {
            return null;
        }

        return context.getProperties().get(propertyKey);
    }

    private void putContextProperties(AuthContext context, Map<String, String> propertyMap) {
        if (context == null || MapUtils.isEmpty(propertyMap)) {
            return;
        }

        if (context.getProperties() == null) {
            context.setProperties(Maps.newHashMap());
        }

        if ("true".equals(propertyMap.get("isApiRequest"))) {
            context.getProperties().put("isApiRequest", "true");
        }
        if (propertyMap.containsKey("requestLang")) {
            context.getProperties().put("requestLang", propertyMap.get("requestLang"));
        }
    }

    private List<ObjectDataPermissionInfo> changeEntityOpennessPojo2ObjectDataPermissionInfo(List<EntityOpennessPojo> entityOpennessPojoList,
                                                                                             List<IObjectDescribe> objectDescribeList) {
        List<ObjectDataPermissionInfo> result = Lists.newArrayList();
        Map<String, String> apiName2DisplayNameMap = Maps.newHashMap();
        for (IObjectDescribe describe : objectDescribeList) {
            apiName2DisplayNameMap.put(describe.getApiName(), describe.getDisplayName());
        }
        apiName2DisplayNameMap.put(Utils.BPM_INSTANCE_API_NAME, I18N.text(I18NKey.BUSINESS_PROCESS_INSTANCE));
        apiName2DisplayNameMap.putIfAbsent(Utils.APPROVAL_INSTANCE_API_NAME, I18N.text(I18NKey.APPROVAL_INSTANCE));
        for (EntityOpennessPojo entityOpennessPojo : entityOpennessPojoList) {
            if (apiName2DisplayNameMap.keySet().contains(entityOpennessPojo.getEntityId())) {
                result.add(new ObjectDataPermissionInfo(
                        entityOpennessPojo.getEntityId(),
                        apiName2DisplayNameMap.get(entityOpennessPojo.getEntityId()),
                        getPermissionFromEntityOpennessPojo(entityOpennessPojo)
                ));
            }
        }
        return result;
    }

    private List<ObjectDataPermissionInfo> entityOpennessPojo2InfoOrderByDescribe(List<EntityOpennessPojo> entityOpennessPojoList,
                                                                                  List<IObjectDescribe> objectDescribeList) {
        List<ObjectDataPermissionInfo> result = Lists.newArrayList();

        Map<String, EntityOpennessPojo> entityMap = entityOpennessPojoList.stream().collect(Collectors.toMap(EntityOpennessPojo::getEntityId, x -> x, (x, y) -> y));
        objectDescribeList.forEach(describe -> {
            if (entityMap.containsKey(describe.getApiName())) {
                result.add(new ObjectDataPermissionInfo(
                        describe.getApiName(),
                        describe.getDisplayName(),
                        getPermissionFromEntityOpennessPojo(entityMap.get(describe.getApiName()))));
            }
        });
        return result;
    }

    private List<IObjectDescribe> handelDescribeList(List<IObjectDescribe> objectDescribeList) {
        //去掉不限制的对象
        List<IObjectDescribe> describeList = objectDescribeList.stream()
                .filter(a -> !Objects.equals(a.getApiName(), Utils.APPROVAL_TASK_API_NAME)).collect(Collectors.toList());
        Map<Boolean, List<IObjectDescribe>> describeGroupingByDefineType =
                describeList.stream().collect(Collectors.groupingBy(x -> ObjectDescribeExt.of(x).isCustomObject()));
        List<IObjectDescribe> results = Lists.newArrayList();

        // 将流程实例放在预置对象和自定义对象之间
        results.addAll(describeGroupingByDefineType.getOrDefault(false, Collections.emptyList()));
        results.add(buildObjectDescribe(Utils.BPM_INSTANCE_API_NAME, I18NExt.getOrDefault(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.BPM_INSTANCE_API_NAME), Utils.BPM_INSTANCE_DISPLAY_NAME)));
        results.add(buildObjectDescribe(Utils.APPROVAL_INSTANCE_API_NAME, I18NExt.getOrDefault(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.APPROVAL_INSTANCE_API_NAME), Utils.APPROVAL_INSTANCE_DISPLAY_NAME)));
        results.addAll(describeGroupingByDefineType.getOrDefault(true, Collections.emptyList()));
        return results;
    }

    private IObjectDescribe buildObjectDescribe(String apiName, String displayName) {
        IObjectDescribe describe = new ObjectDescribe();
        describe.setDisplayName(displayName);
        describe.setApiName(apiName);
        return describe;
    }

    private String getPermissionFromEntityOpennessPojo(EntityOpennessPojo entityOpennessPojo) {
        return entityOpennessPojo.getPermissionFromEntityOpennessPojo().getValue();
    }


    private List<EntityOpennessPojo> changeObjectDataPermissionInfo2EntityOpennessPojo(List<ObjectDataPermissionInfo> objectDataPermissionInfos, User user) {
        // 底层Auth服务定义的常量如下
        // scope   对象权限范围 全公司0，私有2
        // permission  操作权限 1只读 2读写
        HashMap<String, Integer> objectDataPermission2PermissionScope = Maps.newHashMap();
        HashMap<String, Integer> objectDataPermission2Permission = Maps.newHashMap();

        objectDataPermission2PermissionScope.put(
                DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue(),
                EntityOpennessPojo.SCOPE_PRIVATE);
        objectDataPermission2PermissionScope.put(
                DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READONLY.getValue(),
                EntityOpennessPojo.SCOPE_ALL);
        objectDataPermission2PermissionScope.put(
                DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READ_WRITE_DELETE.getValue(),
                EntityOpennessPojo.SCOPE_ALL);

        objectDataPermission2Permission.put(
                DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue(),
                EntityOpennessPojo.PERMISSION_READ_OR_WRITE);
        objectDataPermission2Permission.put(
                DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READONLY.getValue(),
                EntityOpennessPojo.PERMISSION_READONLY);
        objectDataPermission2Permission.put(
                DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READ_WRITE_DELETE.getValue(),
                EntityOpennessPojo.PERMISSION_READ_OR_WRITE);

        List<EntityOpennessPojo> result = Lists.newArrayList();
        for (ObjectDataPermissionInfo objectDataPermissionInfo : objectDataPermissionInfos) {
            if (Objects.equals(objectDataPermissionInfo.getObjectDescribeApiName(), ObjectDescribeExt.PERSONNEL_OBJ_API_NAME)) {
                //人员对象固定私有
                objectDataPermissionInfo.setPermissionType("1");
            }
            result.add(EntityOpennessPojo.builder()
                    .tenantId(user.getTenantId())
                    .appId(DefObjConstants.PACKAGE_NAME_CRM)
                    .creator(user.getUserId())
                    .modifier(user.getUserId())
                    .createTime(System.currentTimeMillis())
                    .modifyTime(System.currentTimeMillis())
                    .permission(objectDataPermission2Permission.get(objectDataPermissionInfo.getPermissionType()))
                    .scope(objectDataPermission2PermissionScope.get(objectDataPermissionInfo.getPermissionType()))
                    .entityId(objectDataPermissionInfo.getObjectDescribeApiName())
                    .build());
        }
        return result;
    }

    private List<EntitySharePojo> genarateSourceShareList(int shareType, List<String> sourceIDList,
                                                          List<String> describeApiNameList, List<Integer> targetCircleIDList,
                                                          List<Integer> targetEmployeeIDList, List<String> targetUserGroupIDList,
                                                          int permissionType, User user, List<String> targetRoleIDList) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(sourceIDList)) {
            return shareList;
        }

        for (String sourceId : sourceIDList) {
            for (String apiName : describeApiNameList) {
                List<String> tagertUserIdStrList = targetEmployeeIDList.stream().map(x -> String.valueOf(x)).collect(Collectors.toList());
                List<String> tagerCirleIdStrList = targetCircleIDList.stream().map(x -> String.valueOf(x)).collect(Collectors.toList());
                List<EntitySharePojo> userSharaList = genarateTargetShareList(permissionType, tagertUserIdStrList, shareType, USER_TYPE, sourceId, apiName, user);
                List<EntitySharePojo> circleSharaList = genarateTargetShareList(permissionType, tagerCirleIdStrList, shareType, DEPART_TYPE, sourceId, apiName, user);
                List<EntitySharePojo> groupSharaList = genarateTargetShareList(permissionType, targetUserGroupIDList, shareType, USER_GROUP_TYPE, sourceId, apiName, user);
                List<EntitySharePojo> roleSharaList = genarateTargetShareList(permissionType, targetRoleIDList, shareType, ROLE_TYPE, sourceId, apiName, user);
                shareList.addAll(userSharaList);
                shareList.addAll(circleSharaList);
                shareList.addAll(groupSharaList);
                shareList.addAll(roleSharaList);
            }
        }
        return shareList;
    }

    private List<EntitySharePojo> genarateTargetShareList(int permissionType, List<String> targetIDList, int shareType,
                                                          Integer receiveType, String sourceId, String apiName, User user) {
        List<EntitySharePojo> shareList = Lists.newArrayList();
        if (CollectionUtils.empty(targetIDList)) {
            return shareList;
        }
        for (String targetID : targetIDList) {
            EntitySharePojo entitySharePojo = EntitySharePojo.builder()
                    .tenantId(user.getTenantId())
                    .appId("CRM")
                    .creator(user.getUserId())
                    .entityId(apiName)
                    .permission(permissionType)
                    .shareId(sourceId)
                    .shareType(shareType)
                    .receiveId(targetID)
                    .receiveType(receiveType)
                    .status(1)
                    .build();

            shareList.add(entitySharePojo);
        }
        return shareList;
    }

    /**
     * 重复的共享规则做符覆盖处理
     *
     * @param shareList
     * @return
     */
    private List<String> handleDuplicateShare(User user, List<EntitySharePojo> shareList) {
        List<String> entitys = Lists.newArrayList();
        List<Integer> shareTypes = Lists.newArrayList();
        List<String> shareIds = Lists.newArrayList();
        List<Integer> shareReceiveTypes = Lists.newArrayList();
        List<String> shareReceiveIds = Lists.newArrayList();

        Map<String, EntitySharePojo> rulePojo = new HashMap<>();

        StringBuilder temp = new StringBuilder();

        if (!CollectionUtils.empty(shareList)) {
            Iterator<EntitySharePojo> iter = shareList.iterator();
            while (iter.hasNext()) {
                EntitySharePojo pojo = iter.next();
                temp.append(pojo.getEntityId()).append('_').append(pojo.getShareType()).append('_').append(pojo.getShareId()).append('_').append(pojo.getReceiveType()).append('_').append(pojo.getReceiveId());
                rulePojo.put(temp.toString(), pojo);
                entitys.add(pojo.getEntityId());
                shareTypes.add(pojo.getShareType());
                shareIds.add(pojo.getShareId());
                shareReceiveTypes.add(pojo.getReceiveType());
                shareReceiveIds.add(pojo.getReceiveId());
                temp.delete(0, temp.length());
            }
        }

        List<String> needDelRule = Lists.newArrayList();

        QueryEntityShareModel.Arg queryArg = new QueryEntityShareModel.Arg();

        queryArg.setContext(buildAuthContext(user));
        queryArg.setEntitys(entitys);
        queryArg.setSourceTypes(shareTypes);
        queryArg.setSources(shareIds);
        queryArg.setReceiveTypes(shareReceiveTypes);
        queryArg.setSharesOrReceives(shareReceiveIds);
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareModel.Result queryResult = proxy.queryEntityShare(header, queryArg);
        List<EntitySharePojo> entityShareList = queryResult.getResult().getContent();
        if (CollectionUtils.notEmpty(entityShareList)) {
            entityShareList.forEach(entityShare -> {
                temp.append(entityShare.getEntityId()).append('_').append(entityShare.getShareType()).append('_').append(entityShare.getShareId()).append('_').append(entityShare.getReceiveType()).append('_').append(entityShare.getReceiveId());
                //规则重复校验(重复直接覆盖)
                if (rulePojo.get(temp.toString()) != null) {
                    needDelRule.add(entityShare.getId());
                }
                temp.delete(0, temp.length());
            });
        }

        Collection<EntitySharePojo> sharePojos = rulePojo.values(); //去除添加重复
        if (CollectionUtils.notEmpty(needDelRule)) {
            DelEntityShareModel.Arg delArg = new DelEntityShareModel.Arg();
            delArg.setContext(buildAuthContext(user));
            delArg.setShareIds(needDelRule);
            DelEntityShareModel.Result delResult = proxy.delEntityShare(header, delArg);
        }
        CreateEntityShareModel.Arg arg = new CreateEntityShareModel.Arg();
        arg.setContext(buildAuthContext(user));
        arg.setShareList(shareList);
        CreateEntityShareModel.Result result = proxy.createEntityShare(header, arg);

        if (!result.isSuccess()) {
            log.warn("createEntityShare failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }

        return result.getResult();
    }

    /**
     * 将前端传来的数据分组为待更新的和待创建的
     * <p>
     * 当前方式存在的问题，高并发时，待更新和待创建的列表并不是最新的，导致数据重复或操作失败
     *
     * @param user
     * @param shareList
     * @return
     */
    private List<String> handleCreateOrUpdateShare(User user, List<EntitySharePojo> shareList) {
        List<String> resultList = Lists.newCopyOnWriteArrayList();
        if (CollectionUtils.empty(shareList)) {
            return resultList;
        }

        List<String> entitys = Lists.newArrayList();
        List<Integer> shareTypes = Lists.newArrayList();
        List<String> shareIds = Lists.newArrayList();
        List<Integer> shareReceiveTypes = Lists.newArrayList();
        List<String> shareReceiveIds = Lists.newArrayList();

        Map<String, EntitySharePojo> rulePojoMap = new HashMap<>();

        shareList.forEach(entitySharePojo -> {
            String str = String.join("_", entitySharePojo.getEntityId(),
                    String.valueOf(entitySharePojo.getShareType()),
                    entitySharePojo.getShareId(), String.valueOf(entitySharePojo.getReceiveType()),
                    entitySharePojo.getReceiveId());
            rulePojoMap.put(str, entitySharePojo);

            entitys.add(entitySharePojo.getEntityId());
            shareTypes.add(entitySharePojo.getShareType());
            shareIds.add(entitySharePojo.getShareId());
            shareReceiveTypes.add(entitySharePojo.getReceiveType());
            shareReceiveIds.add(entitySharePojo.getReceiveId());
        });

        // 构造查询参数，查询当前已经存在的规则
        QueryEntityShareModel.Arg queryArg =
                QueryEntityShareModel.Arg.builder().entitys(entitys).sourceTypes(shareTypes).sources(shareIds)
                        .receiveTypes(shareReceiveTypes).sharesOrReceives(shareReceiveIds).build();
        queryArg.setContext(buildAuthContext(user));
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryEntityShareModel.Result queryResult = proxy.queryEntityShare(header, queryArg);

        List<EntitySharePojo> entityShareList = queryResult.getResult().getContent();
        if (CollectionUtils.notEmpty(entityShareList)) {
            List<EntitySharePojo> toUpdate = Lists.newArrayList();
            entityShareList.forEach(entitySharePojo -> {
                String str = String.join("_", entitySharePojo.getEntityId(),
                        String.valueOf(entitySharePojo.getShareType()),
                        entitySharePojo.getShareId(), String.valueOf(entitySharePojo.getReceiveType()),
                        entitySharePojo.getReceiveId());

                EntitySharePojo sharePojo = rulePojoMap.get(str);
                if (Objects.nonNull(sharePojo)) {
                    // 在shareList中删掉需要被更新的
                    shareList.remove(sharePojo);
                    sharePojo.setId(entitySharePojo.getId());
                    toUpdate.add(sharePojo);
                }
            });
            // 对已经存在的规则进行更新操作
            asyncTask(toUpdate, (t) -> {
                BatchUpdateEntityShareModel.Arg arg = new BatchUpdateEntityShareModel.Arg();
                arg.setContext(buildAuthContext(user));
                arg.setEntityShares(t);
                BatchUpdateEntityShareModel.Result result = proxy.batchUpdateEntityShare(header, arg);
                if (!result.isSuccess()) {
                    log.warn("batchUpdateEntityShare failed,arg:{},result:{}", JSON.toJSONString(arg),
                            JSON.toJSONString(result));
                    throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
                }

                if (CollectionUtils.notEmpty(result.getResult())) {
                    resultList.addAll(result.getResult());
                }
            });
        }

        if (CollectionUtils.empty(shareList)) {
            return resultList;
        }
        // 对剩余的规则进行新建操作
        asyncTask(shareList, (t) -> {
            CreateEntityShareModel.Arg arg = new CreateEntityShareModel.Arg();
            arg.setContext(buildAuthContext(user));
            arg.setShareList(t);
            CreateEntityShareModel.Result result = proxy.createEntityShare(header, arg);
            if (!result.isSuccess()) {
                log.warn("createEntityShare failed,arg:{},result:{}", JSON.toJSONString(arg),
                        JSON.toJSONString(result));
                throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
            }

            if (CollectionUtils.notEmpty(result.getResult())) {
                resultList.addAll(result.getResult());
            }
        });

        return resultList;
    }

    @Override
    public OutDataPrivilege getOutDataPrivilege(User user, String appId, String objectAPIName) {
        GetObjectOutDataPrivilege.Arg arg = GetObjectOutDataPrivilege.Arg.builder()
                .appId(appId)
                .objectApiName(objectAPIName)
                .upstreamTenantId(user.getTenantId())
                .outTenantId(Long.valueOf(user.getOutTenantId()))
                .outUserId(Long.valueOf(user.getOutUserId()))
                .build();

        int outPrivilege = outDataPrivilegeProxy.getObjectOutDataPrivilege(arg);
        return OutDataPrivilege.valueOf(outPrivilege);
    }

    @Override
    public Map<String, Object> obtainDataAuth(QueryDataAuth.Arg arg) {
        User user = User.builder().tenantId(arg.getTenantId()).build();
        Map<String, String> header = RestUtils.buildHeaders(user);
        QueryDataAuthList.Result result = proxy.obtainDataAuth(header, arg);
        if (!result.isSuccess()) {
            log.warn("obtainDataAuth failed,arg:{},result:{}", JSON.toJSONString(arg), JSON.toJSONString(result));
            throwBusinessMessageByCode(user, result.getErrCode(), result.getErrMessage());
        }
        return result.getResult();
    }

    @Override
    public Map<String, Set<String>> queryTeamRoleDescribeList(User user) {
        QueryTeamRoleDescribeListModel.Arg request = new QueryTeamRoleDescribeListModel.Arg();
        request.setContext(buildAuthContext(user));

        Map<String, String> header = RestUtils.buildHeaders(user);

        QueryTeamRoleDescribeListModel.Result response = proxy.queryTeamRoleDescribeList(header, request);

        if (!response.isSuccess()) {
            throw new MetaDataBusinessException(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));
        }

        return response.getResult();
    }

    @Override
    public Object queryTeamRoleMaxNumber(User user) {
        QueryTeamRoleMaxNumberModel.Arg request = new QueryTeamRoleMaxNumberModel.Arg();
        request.setContext(buildAuthContext(user));

        Map<String, String> header = RestUtils.buildHeaders(user);

        QueryTeamRoleMaxNumberModel.Result response = proxy.queryTeamRoleMaxNumber(header, request);

        if (!response.isSuccess()) {
            throw new MetaDataBusinessException(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));
        }
        return response.getResult();
    }

    @Override
    public CreateTeamRole.Result createTeamRole(User user, CreateTeamRole.Arg arg) {
        CreateTeamRoleModel.Arg request = new CreateTeamRoleModel.Arg();
        request.setContext(buildAuthContext(user));
        request.setRoleName(arg.getRoleName());
        request.setEntityIds(arg.getEntityIds());
        request.setDescription(arg.getDescription());

        Map<String, String> header = RestUtils.buildHeaders(user);

        CreateTeamRoleModel.Result response = proxy.createTeamRole(header, request);

        CreateTeamRole.Result result = new CreateTeamRole.Result();
        result.setSuccess(response.isSuccess());
        result.setErrCode(response.getErrCode());
        result.setErrMessage(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));

        return result;
    }

    @Override
    public QueryTeamRole.Result queryTeamRole(User user, QueryTeamRole.Arg arg, String lang) {
        QueryTeamRoleModel.Arg request = new QueryTeamRoleModel.Arg();
        request.setContext(buildAuthContext(user));
        request.setLang(lang);

        Map<String, String> header = RestUtils.buildHeaders(user);

        QueryTeamRoleModel.Result response = proxy.queryTeamRole(header, request);

        if (!response.isSuccess()) {
            throw new MetaDataBusinessException(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));
        }

        List<TeamRolePojo> teamRolePojoList = response.getResult();

        if (CollectionUtils.empty(teamRolePojoList)) {
            teamRolePojoList = Lists.newArrayList();
        }

        if (enableUpdateTranslationByEditTeamRole(user.getTenantId())) {
            for (TeamRolePojo teamRolePojo : teamRolePojoList) {
                if (StringUtils.isBlank(teamRolePojo.getRoleNameTransKey())) {
                    continue;
                }
                String roleNameTranslation = I18N.text(teamRolePojo.getRoleNameTransKey());
                if (StringUtils.isBlank(roleNameTranslation)) {
                    continue;
                }
                teamRolePojo.setRoleName(roleNameTranslation);
            }
        }

        QueryTeamRole.Result result = new QueryTeamRole.Result();
        result.setTeamRoleList(teamRolePojoList);

        return result;
    }

    @Override
    public UpdateTeamRole.Result updateTeamRole(User user, UpdateTeamRole.Arg arg, String lang) {
        UpdateTeamRoleModel.Arg request = new UpdateTeamRoleModel.Arg();
        request.setContext(buildAuthContext(user));
        request.setRoleName(arg.getRoleName());
        request.setRoleType(arg.getRoleType());
        request.setEntityIds(arg.getEntityIds());
        request.setDescription(arg.getDescription());
        request.setLang(lang);

        Map<String, String> header = RestUtils.buildHeaders(user);

        UpdateTeamRoleModel.Result response = proxy.updateTeamRole(header, request);

        if (enableUpdateTranslationByEditTeamRole(user.getTenantId()) && response.isSuccess()) {
            updateTeamRoleNameTranslate(user, user.getTenantId(), arg.getRoleType(), lang, arg.getRoleName());
        }

        UpdateTeamRole.Result result = new UpdateTeamRole.Result();
        result.setSuccess(response.isSuccess());
        result.setErrCode(response.getErrCode());
        result.setErrMessage(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));

        return result;
    }

    @Override
    public UpdateTeamRoleStatus.Result updateTeamRoleStatus(User user, UpdateTeamRoleStatus.Arg arg) {
        UpdateTeamRoleStatusModel.Arg request = new UpdateTeamRoleStatusModel.Arg();
        request.setContext(buildAuthContext(user));
        request.setRoleType(arg.getRoleType());
        request.setStatus(arg.getStatus());

        Map<String, String> header = RestUtils.buildHeaders(user);

        UpdateTeamRoleStatusModel.Result response = proxy.updateTeamRoleStatus(header, request);

        UpdateTeamRoleStatus.Result result = new UpdateTeamRoleStatus.Result();
        result.setSuccess(response.isSuccess());
        result.setErrCode(response.getErrCode());
        result.setErrMessage(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));

        return result;
    }

    @Override
    public UpdateTeamRoleStatus.Result deleteTeamRole(User user, UpdateTeamRoleStatus.Arg arg) {
        UpdateTeamRoleStatusModel.Arg request = new UpdateTeamRoleStatusModel.Arg();
        request.setContext(buildAuthContext(user));
        request.setRoleType(arg.getRoleType());

        Map<String, String> header = RestUtils.buildHeaders(user);

        UpdateTeamRoleStatusModel.Result response = proxy.deleteTeamRole(header, request);

        UpdateTeamRoleStatus.Result result = new UpdateTeamRoleStatus.Result();
        result.setSuccess(response.isSuccess());
        result.setErrCode(response.getErrCode());
        result.setErrMessage(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));

        return result;
    }

    private void updateTeamRoleNameTranslate(User user, String tenantId, String roleType, String transLang, String transValue) {
        if (StringUtils.isAnyBlank(tenantId, roleType, transValue)) {
            log.info("skip updateTeamRoleNameTranslate, tenantId:{}, roleType:{}, transValue:{}", tenantId, roleType, transValue);
            return;
        }

        if ("1".equals(roleType)) {
            return;
        }

        Set<String> allTeamRoleType = queryAllTeamRoleType(user);
        if (!allTeamRoleType.contains(roleType)) {
            return;
        }

        if (StringUtils.isBlank(transLang)) {
            transLang = "zh_CN";
        }
        List<Localization> localizationList = Lists.newArrayList();
        Localization teamRoleTransNameValue = Localization
                .builder()
                .key("data.auth.team.role.trans.key." + roleType)
                .tenantId(Long.parseLong(tenantId))
                .tags(Lists.newArrayList("server"))
                .build();
        I18nClient.getInstance().build(teamRoleTransNameValue, transLang, transValue);
        localizationList.add(teamRoleTransNameValue);

        if (CollectionUtils.notEmpty(localizationList)) {
            I18nClient.getInstance().save(Long.parseLong(tenantId), localizationList, false);
        }
    }

    private Set<String> queryAllTeamRoleType(User user) {
        if (user == null) {
            return Sets.newHashSet();
        }

        QueryTeamRoleModel.Arg request = new QueryTeamRoleModel.Arg();
        request.setContext(buildAuthContext(user));

        Map<String, String> header = RestUtils.buildHeaders(user);

        QueryTeamRoleModel.Result response = proxy.queryTeamRole(header, request);

        if (!response.isSuccess()) {
            throw new MetaDataBusinessException(getPromptMessageByCode(response.getErrCode(), response.getErrMessage()));
        }

        List<TeamRolePojo> teamRolePojoList = response.getResult();

        if (CollectionUtils.empty(teamRolePojoList)) {
            return Sets.newHashSet();
        }

        return teamRolePojoList.stream().map(TeamRolePojo::getRoleType).collect(Collectors.toSet());
    }

    /**
     * ParallelTask工具的任务队列上限是200个，每个任务的数据是100条。处理entitySharePojoList的上线是20000条
     *
     * @param entitySharePojoList
     * @param consumer
     */
    private void asyncTask(List<EntitySharePojo> entitySharePojoList, Consumer<List<EntitySharePojo>> consumer) {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        if (entitySharePojoList.size() > 100) {
            List<List<EntitySharePojo>> partitionList = Lists.partition(entitySharePojoList, 100);
            partitionList.forEach(x -> parallelTask.submit(() -> consumer.accept(x)));
        } else {
            parallelTask.submit(() -> consumer.accept(entitySharePojoList));
        }

        try {
            parallelTask.await(6, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("parallelTask failed,entitySharePojoList:{}", entitySharePojoList);
        }
    }

    private void throwBusinessMessageByCode(User user, int code, String message) {
        if (user == null) {
            throw new MetaDataBusinessException(message);
        }
        if (enableDataAuthSupportMultiLanguageMessage(user.getTenantId())) {
            throw new MetaDataBusinessException(getPromptMessageByCode(code, message));
        } else {
            throw new MetaDataBusinessException(message);
        }
    }

    private void throwValidateMessageByCode(User user, int code, String message) {
        if (user == null) {
            throw new ValidateException(message);
        }
        if (enableDataAuthSupportMultiLanguageMessage(user.getTenantId())) {
            throw new ValidateException(getPromptMessageByCode(code, message));
        } else {
            throw new ValidateException(message);
        }
    }

    private String getPromptMessageByCode(int code, String message) {
        if (code == 0) {
            return message;
        }
        String promptMessage = I18N.text("data.auth.prompt.message.key." + code);
        if (StringUtils.isNotBlank(promptMessage)) {
            return promptMessage;
        }
        return message;
    }
}