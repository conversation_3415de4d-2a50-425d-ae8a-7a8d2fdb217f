package com.facishare.paas.appframework.metadata.domain;

import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.Action;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;
import java.util.*;

/**
 * Unit tests for SimpleDomainPluginDescribe
 * GenerateByAI: Comprehensive tests for SimpleDomainPluginDescribe data class including business logic methods
 */
public class SimpleDomainPluginDescribeTest {

    private SimpleDomainPluginDescribe describe;
    private String testApiName;
    private Map<String, Action> testActions;
    private DomainPluginParam testParams;
    private List<String> testRecordTypeList;

    @BeforeEach
    public void setUp() {
        testApiName = "testPlugin";
        testActions = new HashMap<>();
        testParams = new DomainPluginParam();
        testRecordTypeList = Arrays.asList("type1", "type2");
        
        describe = new SimpleDomainPluginDescribe();
        describe.setApiName(testApiName);
        describe.setActions(testActions);
        describe.setParams(testParams);
        describe.setRecordTypeList(testRecordTypeList);
    }

    @Test
    public void testGettersAndSetters() {
        // Test getters and setters
        SimpleDomainPluginDescribe describe = new SimpleDomainPluginDescribe();
        
        describe.setApiName(testApiName);
        assertEquals(testApiName, describe.getApiName());
        
        describe.setActions(testActions);
        assertEquals(testActions, describe.getActions());
        
        describe.setParams(testParams);
        assertEquals(testParams, describe.getParams());
        
        describe.setRecordTypeList(testRecordTypeList);
        assertEquals(testRecordTypeList, describe.getRecordTypeList());
    }

    @Test
    public void testSupportIdempotentWithValidAction() {
        // Test supportIdempotent with valid action that supports idempotent
        Action action = new Action();
        action.setSupportIdempotent(true);
        testActions.put("testKey", action);
        describe.setActions(testActions);
        
        assertTrue(describe.supportIdempotent("testKey"));
    }

    @Test
    public void testSupportIdempotentWithActionNotSupportingIdempotent() {
        // Test supportIdempotent with action that doesn't support idempotent
        Action action = new Action();
        action.setSupportIdempotent(false);
        testActions.put("testKey", action);
        describe.setActions(testActions);
        
        assertFalse(describe.supportIdempotent("testKey"));
    }

    @Test
    public void testSupportIdempotentWithNullSupportIdempotent() {
        // Test supportIdempotent with null supportIdempotent
        Action action = new Action();
        action.setSupportIdempotent(null);
        testActions.put("testKey", action);
        describe.setActions(testActions);
        
        assertFalse(describe.supportIdempotent("testKey"));
    }

    @Test
    public void testSupportIdempotentWithNonExistentKey() {
        // Test supportIdempotent with non-existent key
        Action action = new Action();
        action.setSupportIdempotent(true);
        testActions.put("existingKey", action);
        describe.setActions(testActions);
        
        assertFalse(describe.supportIdempotent("nonExistentKey"));
    }

    @Test
    public void testSupportIdempotentWithEmptyActions() {
        // Test supportIdempotent with empty actions map
        describe.setActions(new HashMap<>());
        
        assertFalse(describe.supportIdempotent("anyKey"));
    }

    @Test
    public void testSupportIdempotentWithNullActions() {
        // Test supportIdempotent with null actions
        describe.setActions(null);
        
        assertFalse(describe.supportIdempotent("anyKey"));
    }

    @Test
    public void testGetRestApiUrlWithValidAction() {
        // Test getRestApiUrl with valid action
        String testUrl = "http://test.api.url";
        Action action = new Action();
        action.setRestApiUrl(testUrl);
        testActions.put("testKey", action);
        describe.setActions(testActions);
        
        assertEquals(testUrl, describe.getRestApiUrl("testKey"));
    }

    @Test
    public void testGetRestApiUrlWithNullUrl() {
        // Test getRestApiUrl with null URL
        Action action = new Action();
        action.setRestApiUrl(null);
        testActions.put("testKey", action);
        describe.setActions(testActions);
        
        assertNull(describe.getRestApiUrl("testKey"));
    }

    @Test
    public void testGetRestApiUrlWithNonExistentKey() {
        // Test getRestApiUrl with non-existent key
        Action action = new Action();
        action.setRestApiUrl("http://test.url");
        testActions.put("existingKey", action);
        describe.setActions(testActions);
        
        assertNull(describe.getRestApiUrl("nonExistentKey"));
    }

    @Test
    public void testGetRestApiUrlWithEmptyActions() {
        // Test getRestApiUrl with empty actions map
        describe.setActions(new HashMap<>());
        
        assertNull(describe.getRestApiUrl("anyKey"));
    }

    @Test
    public void testGetRestApiUrlWithNullActions() {
        // Test getRestApiUrl with null actions
        describe.setActions(null);
        
        assertNull(describe.getRestApiUrl("anyKey"));
    }

    @Test
    public void testMultipleActionsInMap() {
        // Test with multiple actions in the map
        Action action1 = new Action();
        action1.setSupportIdempotent(true);
        action1.setRestApiUrl("http://api1.url");
        
        Action action2 = new Action();
        action2.setSupportIdempotent(false);
        action2.setRestApiUrl("http://api2.url");
        
        testActions.put("key1", action1);
        testActions.put("key2", action2);
        describe.setActions(testActions);
        
        assertTrue(describe.supportIdempotent("key1"));
        assertFalse(describe.supportIdempotent("key2"));
        assertEquals("http://api1.url", describe.getRestApiUrl("key1"));
        assertEquals("http://api2.url", describe.getRestApiUrl("key2"));
    }

    @Test
    public void testSettersWithNullValues() {
        // Test setters with null values
        describe.setApiName(null);
        assertNull(describe.getApiName());
        
        describe.setActions(null);
        assertNull(describe.getActions());
        
        describe.setParams(null);
        assertNull(describe.getParams());
        
        describe.setRecordTypeList(null);
        assertNull(describe.getRecordTypeList());
    }

    @Test
    public void testSettersWithEmptyCollections() {
        // Test setters with empty collections
        describe.setActions(new HashMap<>());
        assertTrue(describe.getActions().isEmpty());
        
        describe.setRecordTypeList(new ArrayList<>());
        assertTrue(describe.getRecordTypeList().isEmpty());
    }

    @Test
    public void testEqualsAndHashCode() {
        // Test equals and hashCode methods (provided by Lombok @Data)
        SimpleDomainPluginDescribe describe1 = new SimpleDomainPluginDescribe();
        describe1.setApiName(testApiName);
        describe1.setActions(testActions);
        describe1.setParams(testParams);
        describe1.setRecordTypeList(testRecordTypeList);
        
        SimpleDomainPluginDescribe describe2 = new SimpleDomainPluginDescribe();
        describe2.setApiName(testApiName);
        describe2.setActions(testActions);
        describe2.setParams(testParams);
        describe2.setRecordTypeList(testRecordTypeList);
        
        assertEquals(describe1, describe2);
        assertEquals(describe1.hashCode(), describe2.hashCode());
    }

    @Test
    public void testNotEquals() {
        // Test inequality
        SimpleDomainPluginDescribe describe1 = new SimpleDomainPluginDescribe();
        describe1.setApiName(testApiName);
        
        SimpleDomainPluginDescribe describe2 = new SimpleDomainPluginDescribe();
        describe2.setApiName("differentApiName");
        
        assertNotEquals(describe1, describe2);
        assertNotEquals(describe1, null);
        assertNotEquals(describe1, "not a describe object");
    }

    @Test
    public void testToString() {
        // Test toString method (provided by Lombok @Data)
        String toString = describe.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("SimpleDomainPluginDescribe"));
        assertTrue(toString.contains(testApiName));
    }

    @Test
    public void testWithSpecialCharactersInApiName() {
        // Test with special characters in API name
        String specialApiName = "api@#$%^&*()_+-=";
        describe.setApiName(specialApiName);
        assertEquals(specialApiName, describe.getApiName());
    }

    @Test
    public void testWithLargeRecordTypeList() {
        // Test with large record type list
        List<String> largeList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            largeList.add("recordType" + i);
        }
        describe.setRecordTypeList(largeList);
        assertEquals(100, describe.getRecordTypeList().size());
        assertEquals("recordType0", describe.getRecordTypeList().get(0));
        assertEquals("recordType99", describe.getRecordTypeList().get(99));
    }
}
