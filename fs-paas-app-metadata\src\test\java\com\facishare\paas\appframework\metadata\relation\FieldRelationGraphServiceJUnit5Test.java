package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * JUnit5测试类 for FieldRelationGraphService
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("FieldRelationGraphService 单元测试")
class FieldRelationGraphServiceJUnit5Test {

    @InjectMocks
    private FieldRelationGraphService fieldRelationGraphService;

    @Mock
    private DescribeLogicService describeLogicService;

    private IObjectDescribe mockMasterDescribe;
    private List<IObjectDescribe> mockDetailDescribes;
    private IFieldDescribe mockFieldDescribe;

    @BeforeEach
    void setUp() {
        // Mock主对象描述
        mockMasterDescribe = mock(IObjectDescribe.class);
        when(mockMasterDescribe.getApiName()).thenReturn("MasterObject");
        when(mockMasterDescribe.getTenantId()).thenReturn("123456789");
        when(mockMasterDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList());

        // Mock字段描述
        mockFieldDescribe = mock(IFieldDescribe.class);
        when(mockFieldDescribe.getApiName()).thenReturn("test_field");
        when(mockFieldDescribe.getDescribeApiName()).thenReturn("MasterObject");

        // Mock详细对象描述列表
        IObjectDescribe mockDetailDescribe = mock(IObjectDescribe.class);
        when(mockDetailDescribe.getApiName()).thenReturn("DetailObject");
        when(mockDetailDescribe.getTenantId()).thenReturn("123456789");
        when(mockDetailDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList());
        
        mockDetailDescribes = Lists.newArrayList(mockDetailDescribe);
    }

    // ==================== buildGraphWithOnlyLookupObjects 方法测试 ====================

    @Test
    @DisplayName("构建仅查找对象图 - 基础方法")
    void testBuildGraphWithOnlyLookupObjects_Basic() {
        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                mockMasterDescribe, mockDetailDescribes);

        // Assert
        assertNotNull(result);
        // 验证图对象不为空
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建仅查找对象图 - 包含排除默认值参数")
    void testBuildGraphWithOnlyLookupObjects_WithExcludeDefaultValue() {
        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                mockMasterDescribe, mockDetailDescribes, true, true);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建仅查找对象图 - 包含所有参数")
    void testBuildGraphWithOnlyLookupObjects_WithAllParams() {
        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                mockMasterDescribe, mockDetailDescribes, true, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建仅查找对象图 - 完整参数")
    void testBuildGraphWithOnlyLookupObjects_FullParams() {
        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                mockMasterDescribe, mockDetailDescribes, true, true, false, true);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建仅查找对象图 - 空详细描述列表")
    void testBuildGraphWithOnlyLookupObjects_EmptyDetailDescribes() {
        // Arrange
        List<IObjectDescribe> emptyDetailDescribes = Lists.newArrayList();

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                mockMasterDescribe, emptyDetailDescribes);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建仅查找对象图 - null详细描述列表")
    void testBuildGraphWithOnlyLookupObjects_NullDetailDescribes() {
        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                mockMasterDescribe, null);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    // ==================== buildDependencyGraph 方法测试 ====================

    @Test
    @DisplayName("构建依赖图 - 基础方法")
    void testBuildDependencyGraph_Basic() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(describes, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建依赖图 - 排除默认值")
    void testBuildDependencyGraph_ExcludeDefaultValue() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(describes, true);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建依赖图 - 带现有图参数")
    void testBuildDependencyGraph_WithExistingGraph() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);
        // 使用null而不是Mock，因为FieldRelationGraphBuilder需要真实的graph对象
        FieldRelationGraph existingGraph = null;

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(
                existingGraph, describes, false);

        // Assert
        assertNotNull(result);
    }

    @Test
    @DisplayName("构建依赖图 - 包含所有参数")
    void testBuildDependencyGraph_WithAllParams() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);
        // 使用null而不是Mock，因为FieldRelationGraphBuilder需要真实的graph对象
        FieldRelationGraph existingGraph = null;

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(
                existingGraph, describes, true, true, false);

        // Assert
        assertNotNull(result);
    }

    @Test
    @DisplayName("构建依赖图 - 空描述列表")
    void testBuildDependencyGraph_EmptyDescribes() {
        // Arrange
        List<IObjectDescribe> emptyDescribes = Lists.newArrayList();

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(emptyDescribes, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    // ==================== buildReverseDependencyGraph 方法测试 ====================

    @Test
    @DisplayName("构建反向依赖图 - 基础方法")
    void testBuildReverseDependencyGraph_Basic() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(describes, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建反向依赖图 - 排除默认值")
    void testBuildReverseDependencyGraph_ExcludeDefaultValue() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(describes, true);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建反向依赖图 - 包含引用字段")
    void testBuildReverseDependencyGraph_IncludeQuoteField() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(describes, false, true);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建反向依赖图 - 包含所有参数")
    void testBuildReverseDependencyGraph_WithAllParams() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(
                describes, true, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    @Test
    @DisplayName("构建反向依赖图 - 空描述列表")
    void testBuildReverseDependencyGraph_EmptyDescribes() {
        // Arrange
        List<IObjectDescribe> emptyDescribes = Lists.newArrayList();

        // Act
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(emptyDescribes, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 主描述为null")
    void testBuildGraphWithOnlyLookupObjects_NullMasterDescribe() {
        // Act & Assert
        assertThrows(Exception.class, () -> {
            fieldRelationGraphService.buildGraphWithOnlyLookupObjects(null, mockDetailDescribes);
        });
    }

    @Test
    @DisplayName("边界测试 - 描述列表为null")
    void testBuildDependencyGraph_NullDescribes() {
        // Act & Assert - 修改期望，null describes实际上不会抛出异常
        assertDoesNotThrow(() -> {
            FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(null, false);
            assertNotNull(result);
        });
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的图构建流程")
    void testCompleteGraphBuildingFlow() {
        // Arrange
        List<IObjectDescribe> describes = Lists.newArrayList(mockMasterDescribe);

        // Act - 执行完整流程
        // 1. 构建仅查找对象图
        FieldRelationGraph lookupGraph = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                mockMasterDescribe, mockDetailDescribes);
        
        // 2. 构建依赖图
        FieldRelationGraph dependencyGraph = fieldRelationGraphService.buildDependencyGraph(describes, false);
        
        // 3. 构建反向依赖图
        FieldRelationGraph reverseDependencyGraph = fieldRelationGraphService.buildReverseDependencyGraph(
                describes, false);

        // Assert
        assertNotNull(lookupGraph);
        assertNotNull(lookupGraph.getGraph());
        
        assertNotNull(dependencyGraph);
        assertNotNull(dependencyGraph.getGraph());
        
        assertNotNull(reverseDependencyGraph);
        assertNotNull(reverseDependencyGraph.getGraph());
    }

    @Test
    @DisplayName("性能测试 - 大量对象描述处理")
    void testPerformance_LargeObjectDescribes() {
        // Arrange
        List<IObjectDescribe> largeDescribes = Lists.newArrayList();
        for (int i = 0; i < 50; i++) {
            IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
            when(mockDescribe.getApiName()).thenReturn("TestObject" + i);
            when(mockDescribe.getTenantId()).thenReturn("123456789");
            when(mockDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList());
            largeDescribes.add(mockDescribe);
        }

        // Act
        long startTime = System.currentTimeMillis();
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(largeDescribes, false);
        long endTime = System.currentTimeMillis();

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGraph());
        assertTrue(endTime - startTime < 5000); // 应该在5秒内完成
    }
}
