package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SearchTemplateExt的JUnit 5测试类
 * 测试搜索模板扩展功能
 *
 * GenerateByAI
 * 测试内容描述：测试搜索模板的创建、配置和业务逻辑
 */
@DisplayName("SearchTemplateExt JUnit5 测试")
class SearchTemplateExtJUnit5Test extends LightweightJUnit5Test {

    private SearchTemplateExt searchTemplateExt;
    private ISearchTemplate testSearchTemplate;

    @BeforeEach
    void setUp() {
        // 创建测试用的搜索模板
        testSearchTemplate = new SearchTemplate();
        testSearchTemplate.setApiName("test_template");
        testSearchTemplate.setLabel("测试模板");
        testSearchTemplate.setObjectDescribeApiName("TestObject");
        testSearchTemplate.setType(SearchTemplateExt.CUSTOM_SCENE);
        testSearchTemplate.setTenantId("test_tenant");
        testSearchTemplate.setUserId("test_user");
        
        searchTemplateExt = SearchTemplateExt.of(testSearchTemplate);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - ISearchTemplate参数
     */
    @Test
    @DisplayName("静态方法 - of工厂方法ISearchTemplate参数")
    void testOf_WithISearchTemplate() {
        // Act: 使用of方法创建实例
        SearchTemplateExt result = SearchTemplateExt.of(testSearchTemplate);
        
        // Assert: 验证工厂方法
        assertNotNull(result);
        assertSame(testSearchTemplate, result.getSearchTemplate());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法 - 避免双重包装
     */
    @Test
    @DisplayName("静态方法 - of工厂方法避免双重包装")
    void testOf_AvoidDoubleWrapping() {
        // Arrange: 创建SearchTemplateExt实例
        SearchTemplateExt originalExt = SearchTemplateExt.of(testSearchTemplate);
        
        // Act: 再次使用of方法包装
        SearchTemplateExt result = SearchTemplateExt.of(originalExt);
        
        // Assert: 验证避免双重包装
        assertNotNull(result);
        assertSame(testSearchTemplate, result.getSearchTemplate());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本属性访问
     */
    @Test
    @DisplayName("基本属性 - 属性访问")
    void testBasicProperties() {
        // Act & Assert: 验证基本属性
        assertEquals("test_template", searchTemplateExt.getApiName());
        assertEquals("测试模板", searchTemplateExt.getLabel());
        assertEquals("TestObject", searchTemplateExt.getObjectDescribeApiName());
        assertEquals(SearchTemplateExt.CUSTOM_SCENE, searchTemplateExt.getType());
        assertEquals("test_tenant", searchTemplateExt.getTenantId());
        assertEquals("test_user", searchTemplateExt.getUserId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setTagWidth方法
     */
    @Test
    @DisplayName("标签配置 - setTagWidth方法")
    void testSetTagWidth() {
        // Act: 设置标签宽度
        searchTemplateExt.setTagWidth(200);
        
        // Assert: 验证设置结果
        assertEquals(200, searchTemplateExt.get("tag_width"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setTagAutoWrapLine方法
     */
    @Test
    @DisplayName("标签配置 - setTagAutoWrapLine方法")
    void testSetTagAutoWrapLine() {
        // Act: 设置标签自动换行
        searchTemplateExt.setTagAutoWrapLine(true);
        
        // Assert: 验证设置结果
        assertEquals(true, searchTemplateExt.get("tag_auto_wrap_line"));
        
        // Act: 设置为false
        searchTemplateExt.setTagAutoWrapLine(false);
        
        // Assert: 验证设置结果
        assertEquals(false, searchTemplateExt.get("tag_auto_wrap_line"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCustomScene方法 - 自定义场景
     */
    @Test
    @DisplayName("场景类型 - isCustomScene方法自定义场景")
    void testIsCustomScene_True() {
        // Arrange: 设置为自定义场景
        testSearchTemplate.setType(SearchTemplateExt.CUSTOM_SCENE);
        
        // Act: 执行isCustomScene方法
        boolean result = searchTemplateExt.isCustomScene();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isCustomScene方法 - 非自定义场景
     */
    @Test
    @DisplayName("场景类型 - isCustomScene方法非自定义场景")
    void testIsCustomScene_False() {
        // Arrange: 设置为默认场景
        testSearchTemplate.setType(SearchTemplateExt.DEFAULT_SCENE);
        
        // Act: 执行isCustomScene方法
        boolean result = searchTemplateExt.isCustomScene();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDefaultScene方法 - 默认场景
     */
    @Test
    @DisplayName("场景类型 - isDefaultScene方法默认场景")
    void testIsDefaultScene_True() {
        // Arrange: 设置为默认场景
        testSearchTemplate.setType(SearchTemplateExt.DEFAULT_SCENE);
        
        // Act: 执行isDefaultScene方法
        boolean result = searchTemplateExt.isDefaultScene();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isDefaultScene方法 - 非默认场景
     */
    @Test
    @DisplayName("场景类型 - isDefaultScene方法非默认场景")
    void testIsDefaultScene_False() {
        // Arrange: 设置为自定义场景
        testSearchTemplate.setType(SearchTemplateExt.CUSTOM_SCENE);
        
        // Act: 执行isDefaultScene方法
        boolean result = searchTemplateExt.isDefaultScene();
        
        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isUseFieldList方法 - 使用字段列表
     */
    @Test
    @DisplayName("字段列表 - isUseFieldList方法使用字段列表")
    void testIsUseFieldList_True() {
        // Arrange: 设置使用字段列表
        searchTemplateExt.set("use_field_list", true);
        
        // Act: 执行isUseFieldList方法
        boolean result = searchTemplateExt.isUseFieldList();
        
        // Assert: 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isUseFieldList方法 - 不使用字段列表
     */
    @Test
    @DisplayName("字段列表 - isUseFieldList方法不使用字段列表")
    void testIsUseFieldList_False() {
        // Arrange: 设置不使用字段列表（应该设置fieldListType为USE_LAYOUT_FIELD）
        searchTemplateExt.setFieldListType(SearchTemplateExt.USE_LAYOUT_FIELD); // 修正：使用正确的字段和值

        // Act: 执行isUseFieldList方法
        boolean result = searchTemplateExt.isUseFieldList();

        // Assert: 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldList方法 - 有字段列表
     */
    @Test
    @DisplayName("字段列表 - getFieldList方法有字段列表")
    void testGetFieldList_WithFields() {
        // Arrange: 设置字段列表
        @SuppressWarnings("rawtypes")
        List<Map> fieldList = Lists.newArrayList();
        Map<String, Object> field1 = Maps.newHashMap();
        field1.put("api_name", "field1");
        field1.put("label", "字段1");
        fieldList.add(field1);

        Map<String, Object> field2 = Maps.newHashMap();
        field2.put("api_name", "field2");
        field2.put("label", "字段2");
        fieldList.add(field2);

        searchTemplateExt.setFieldList(fieldList);

        // Act: 执行getFieldList方法
        @SuppressWarnings("rawtypes")
        List<Map> result = searchTemplateExt.getFieldList();

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("field1", result.get(0).get("api_name"));
        assertEquals("field2", result.get(1).get("api_name"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getFieldList方法 - 空字段列表
     */
    @Test
    @DisplayName("字段列表 - getFieldList方法空字段列表")
    void testGetFieldList_EmptyFields() {
        // Arrange: 设置空字段列表
        searchTemplateExt.setFieldList(Lists.newArrayList());

        // Act: 执行getFieldList方法
        @SuppressWarnings("rawtypes")
        List<Map> result = searchTemplateExt.getFieldList();

        // Assert: 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试copyToSetDefault方法
     */
    @Test
    @DisplayName("复制方法 - copyToSetDefault方法")
    void testCopyToSetDefault() {
        // Arrange: 设置扩展属性
        String extendAttribute = "test_extend_attribute";
        testSearchTemplate.setExtendAttribute(extendAttribute);
        testSearchTemplate.setId("original_id");

        // Act: 执行copyToSetDefault方法
        ISearchTemplate result = searchTemplateExt.copyToSetDefault();

        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("TestObject", result.getObjectDescribeApiName());
        assertEquals("original_id", result.getId());
        assertTrue(result.getIsDefault());
        assertEquals(extendAttribute, result.getExtendAttribute());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试常量值
     */
    @Test
    @DisplayName("常量验证 - 常量值验证")
    void testConstants() {
        // Assert: 验证重要常量
        assertEquals("default", SearchTemplateExt.DEFAULT_SCENE); // 修正：实际值是"default"
        assertEquals("custom", SearchTemplateExt.CUSTOM_SCENE); // 修正：实际值是"custom"
        assertEquals("tag", SearchTemplateExt.TAG_AS_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试委托模式的方法调用
     */
    @Test
    @DisplayName("委托模式 - 委托方法调用")
    void testDelegateMethodCalls() {
        // Act & Assert: 验证委托方法调用
        assertEquals("test_template", searchTemplateExt.getApiName());
        assertEquals("测试模板", searchTemplateExt.getLabel());
        assertEquals("TestObject", searchTemplateExt.getObjectDescribeApiName());
        
        // 测试设置方法
        searchTemplateExt.setLabel("新的测试模板");
        assertEquals("新的测试模板", searchTemplateExt.getLabel());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试不同场景类型
     */
    @ParameterizedTest
    @ValueSource(strings = {"default", "custom", "tenant"}) // 修正：使用实际的常量值
    @DisplayName("场景类型 - 不同场景类型测试")
    void testVariousSceneTypes(String sceneType) {
        // Arrange: 创建不同类型的搜索模板
        testSearchTemplate.setType(sceneType);

        // Act & Assert: 验证场景类型
        assertEquals(sceneType, searchTemplateExt.getType());

        if ("default".equals(sceneType)) { // 修正：使用实际的常量值
            assertTrue(searchTemplateExt.isDefaultScene());
            assertFalse(searchTemplateExt.isCustomScene());
        } else if ("custom".equals(sceneType)) { // 修正：使用实际的常量值
            assertFalse(searchTemplateExt.isDefaultScene());
            assertTrue(searchTemplateExt.isCustomScene());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - null值处理
     */
    @Test
    @DisplayName("边界条件 - null值处理")
    void testNullValues() {
        // Arrange: 创建包含null值的搜索模板
        testSearchTemplate.setLabel(null);
        testSearchTemplate.setObjectDescribeApiName(null);
        
        // Act & Assert: 验证null值处理
        assertNull(searchTemplateExt.getLabel());
        assertNull(searchTemplateExt.getObjectDescribeApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Act: 多次获取相同的数据
        String apiName1 = searchTemplateExt.getApiName();
        String apiName2 = searchTemplateExt.getApiName();
        String type1 = searchTemplateExt.getType();
        String type2 = searchTemplateExt.getType();
        
        // Assert: 验证数据一致性
        assertEquals(apiName1, apiName2);
        assertEquals(type1, type2);
        assertSame(searchTemplateExt.getSearchTemplate(), searchTemplateExt.getSearchTemplate());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试线程安全性
     */
    @Test
    @DisplayName("线程安全 - 多线程访问")
    void testThreadSafety() throws InterruptedException {
        // Act: 在多线程中访问
        Thread thread1 = new Thread(() -> {
            searchTemplateExt.setTagWidth(100);
            assertNotNull(searchTemplateExt.getApiName());
        });
        
        Thread thread2 = new Thread(() -> {
            searchTemplateExt.setTagAutoWrapLine(true);
            assertNotNull(searchTemplateExt.getType());
        });
        
        // Assert: 验证线程安全
        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread1.join();
            thread2.join();
        });
    }
}
