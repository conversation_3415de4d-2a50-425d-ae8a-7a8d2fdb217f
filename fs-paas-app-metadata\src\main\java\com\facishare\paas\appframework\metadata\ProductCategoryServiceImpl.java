package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2019-01-22.
 */
@Service
public class ProductCategoryServiceImpl implements ProductCategoryService {
    @Autowired
    private MetaDataFindService metaDataFindService;

    private final Set<Operator> CATEGORY_NEED_TRANSFER_OPERATORS = Sets.newHashSet(Operator.EQ, Operator.IN, Operator.IS, Operator.N, Operator.NIN, Operator.ISN, Operator.HASANYOF, Operator.NHASANYOF);
    private final Set<Operator> CATEGORY_NEED_TRANSFER_2_IN_OPERATORS = Sets.newHashSet(Operator.EQ, Operator.IN, Operator.IS, Operator.HASANYOF);
    private final List<String> QUERY_FIELDS = Lists.newArrayList(ObjectDataExt.ID, ObjectDataExt.NAME, "code", "order_field", "pid");
    private static final int LIMIT_SIZE = 1000;
    private static final int LOOP_COUNT = 30;

    @Override
    public List<ProductAllCategoriesModel.CategoryPojo> getProductAllCategories(String tenantId, String userId) {
        User user = new User(tenantId, userId);
        List<IObjectData> productCategoryObj = findAllCategory(user);
        List<ProductAllCategoriesModel.CategoryPojo> categoryObjectList = productCategoryObj.stream().map(o -> new ProductAllCategoriesModel.CategoryPojo(o.getId(),
                o.get("code", String.class),
                o.getName(),
                o.get("order_field", String.class),
                o.get("pid", String.class),
                Boolean.FALSE,
                Lists.newArrayList())).collect(Collectors.toList());
        ProductAllCategoriesModel.CategoryPojo categoryObject = new ProductAllCategoriesModel.CategoryPojo();
        List<ProductAllCategoriesModel.CategoryPojo> rootCategoryObjectList = Lists.newArrayList();
        Map<String, List<ProductAllCategoriesModel.CategoryPojo>> parentChildMap = Maps.newHashMap();
        for (ProductAllCategoriesModel.CategoryPojo categoryPojo : categoryObjectList) {
            if (Strings.isNullOrEmpty(categoryPojo.getParentId())) {
                rootCategoryObjectList.add(categoryPojo);
            } else {
                parentChildMap.computeIfAbsent(categoryPojo.getParentId(), k -> new ArrayList<>()).add(categoryPojo);
            }
        }
        categoryObject.setChildren(rootCategoryObjectList);
        dealCurrentLevel(categoryObject, parentChildMap);
        return categoryObject.getChildren();
    }

    @Override
    public Set<String> getCategoryChildrenCategoryCodesContainSelf(String tenantId, String userId, String categoryCode) {
        if (categoryCode == null) {
            return Sets.newHashSetWithExpectedSize(0);
        }

        List<ProductAllCategoriesModel.CategoryPojo> productAllCategories = getProductAllCategories(tenantId, userId);
        return getCategoryChildrenCategoryCodesContainSelf(categoryCode, productAllCategories);
    }

    @Override
    public Set<String> getCategoryChildrenCategoryCodesContainSelf(String categoryCode, List<ProductAllCategoriesModel.CategoryPojo> productAllCategories) {
        if (categoryCode == null) {
            return Sets.newHashSetWithExpectedSize(0);
        }

        Set<String> categoryCodes = Sets.newHashSet(categoryCode);

        ProductAllCategoriesModel.CategoryPojo targetCategory = getTargetCategory(productAllCategories, categoryCode);
        fillAllChildrenCategoryCodes(targetCategory, categoryCodes);

        return categoryCodes;
    }

    /**
     * 根据targetCategoryCode找到目标的分类节点
     *
     * @param categories         分类列表信息
     * @param targetCategoryCode 目标分类的code
     * @return
     */
    private ProductAllCategoriesModel.CategoryPojo getTargetCategory(List<ProductAllCategoriesModel.CategoryPojo> categories, String targetCategoryCode) {
        if (CollectionUtils.empty(categories)) {
            return null;
        } else {
            for (ProductAllCategoriesModel.CategoryPojo category : categories) {
                if (Objects.equals(targetCategoryCode, category.getCategoryCode())) {
                    return category;
                } else {
                    ProductAllCategoriesModel.CategoryPojo targetCategory = getTargetCategory(category.getChildren(), targetCategoryCode);
                    if (targetCategory != null) {
                        return targetCategory;
                    }
                }
            }
            return null;
        }
    }

    /**
     * categoryCodes集合中填充目标分类下的所有子分类
     *
     * @param category      分类信息
     * @param categoryCodes 分类的code
     */
    private void fillAllChildrenCategoryCodes(ProductAllCategoriesModel.CategoryPojo category, Set<String> categoryCodes) {
        if (category == null) {
            return;
        }

        categoryCodes.add(category.getCategoryCode());
        List<ProductAllCategoriesModel.CategoryPojo> children = category.getChildren();

        if (CollectionUtils.notEmpty(children)) {
            for (ProductAllCategoriesModel.CategoryPojo child : children) {
                fillAllChildrenCategoryCodes(child, categoryCodes);
            }
        }
    }

    private SearchTemplateQuery buildQuery() {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(LIMIT_SIZE);
        List<IFilter> filterList = Lists.newArrayList();
        IFilter deleteFilter = new Filter();
        deleteFilter.setFieldName(ObjectDataExt.IS_DELETED);
        deleteFilter.setOperator(Operator.EQ);
        deleteFilter.setFieldValues(Lists.newArrayList("0"));
        filterList.add(deleteFilter);
        searchQuery.setFilters(filterList);
        List<OrderBy> orders = Lists.newArrayList();
        orders.add(new OrderBy(ObjectDataExt.ID, Boolean.TRUE));
        searchQuery.setOrders(orders);
        return searchQuery;
    }

    private List<IObjectData> findAllCategory(User user) {
        SearchTemplateQuery searchQuery = buildQuery();
        List<IObjectData> objectDataList = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(ObjectDataExt.ID);
        filter.setOperator(Operator.GT);
        int loopCount = 0;
        //最多循环30次
        while (loopCount < LOOP_COUNT) {
            QueryResult<IObjectData> productCategoryObj = metaDataFindService.findBySearchQueryWithFieldsIgnoreAll(user,
                    "ProductCategoryObj", searchQuery, QUERY_FIELDS);
            objectDataList.addAll(productCategoryObj.getData());
            if (CollectionUtils.empty(productCategoryObj.getData()) || productCategoryObj.getData().size() < LIMIT_SIZE) {
                break;
            }
            List<IFilter> filters = searchQuery.getFilters();
            filters.removeIf(f -> ObjectDataExt.ID.equals(f.getFieldName()));
            filter.setFieldValues(Lists.newArrayList(productCategoryObj.getData().get(LIMIT_SIZE - 1).getId()));
            filters.add(filter);
            loopCount++;
        }
        return objectDataList;
    }

    private void dealCurrentLevel(ProductAllCategoriesModel.CategoryPojo categoryObject, Map<String, List<ProductAllCategoriesModel.CategoryPojo>> parentChildMap) {
        List<ProductAllCategoriesModel.CategoryPojo> children = categoryObject.getChildren();
        if (CollectionUtils.notEmpty(children)) {
            for (ProductAllCategoriesModel.CategoryPojo child : children) {
                List<ProductAllCategoriesModel.CategoryPojo> collect = parentChildMap.get(child.getCategoryId());
                if (CollectionUtils.notEmpty(collect)) {
                    try {
                        collect.sort(Comparator.comparingInt(o -> {
                            try {
                                return Integer.parseInt(o.getCategoryOrder());
                            } catch (NumberFormatException e) {
                                return 0;
                            }
                        }));
                        child.setChildren(collect);
                    } catch (Exception ignored) {
                    }
                }
                dealCurrentLevel(child, parentChildMap);
            }
        }
    }

    @Override
    public void handleCategoryWhere(String tenantId, String userId, List<Wheres> wheres) {
        for (Wheres where : wheres) {
            if (CollectionUtils.notEmpty(where.getFilters()))
                handleCategoryFilters(tenantId, userId, where.getFilters());
        }

    }

    @Override
    public void handleCategoryFilters(String tenantId, String userId, List<IFilter> filters) {
        for (IFilter filter : filters) {
            if ("category".equals(filter.getFieldName()) && CATEGORY_NEED_TRANSFER_OPERATORS.contains(filter.getOperator())) {
                List<String> fieldValues = filter.getFieldValues();
                Set<String> categoryValues = Sets.newHashSet();

                if (CollectionUtils.notEmpty(fieldValues)) {
                    List<ProductAllCategoriesModel.CategoryPojo> productAllCategories = getProductAllCategories(tenantId, userId);
                    for (String fieldValue : fieldValues) {
                        if (categoryValues.contains(fieldValue)) {
                            continue;
                        }
                        Set<String> categoryChildrenCategoryCodes = getChildrenCategoryContainSelf(productAllCategories, fieldValue);
                        categoryValues.addAll(categoryChildrenCategoryCodes);
                    }
                }

                if (CATEGORY_NEED_TRANSFER_2_IN_OPERATORS.contains(filter.getOperator())) {
                    filter.setOperator(Operator.IN);
                } else {
                    filter.setOperator(Operator.NIN);
                }
                filter.setFieldValues(Lists.newArrayList(categoryValues));
            }
        }
    }

    private Set<String> getChildrenCategoryContainSelf(List<ProductAllCategoriesModel.CategoryPojo> productAllCategories,
                                                       String categoryCode) {
        if (CollectionUtils.empty(productAllCategories) || categoryCode == null) {
            return Sets.newHashSetWithExpectedSize(0);
        }
        return getCategoryChildrenCategoryCodesContainSelf(categoryCode, productAllCategories);
    }
}
