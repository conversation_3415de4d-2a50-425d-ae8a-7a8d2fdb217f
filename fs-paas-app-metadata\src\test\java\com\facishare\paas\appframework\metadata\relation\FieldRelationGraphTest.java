package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.facishare.paas.appframework.common.graph.ValueGraph;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FieldRelationGraphTest {

    @Mock(lenient = true)
    private ValueGraph<FieldNode, RelateEdge> mockValueGraph;

    @Mock(lenient = true)
    private FieldNode mockFieldNode;

    @Mock(lenient = true)
    private FieldNode mockFieldNode2;

    private Map<String, IObjectDescribe> describeMap;
    private FieldRelationGraph fieldRelationGraph;

    @BeforeEach
    void setUp() {
        describeMap = Maps.newHashMap();
        IObjectDescribe describe1 = new ObjectDescribe();
        describe1.setApiName("Object1");
        describe1.setTenantId("74255");
        describeMap.put("Object1", describe1);

        IObjectDescribe describe2 = new ObjectDescribe();
        describe2.setApiName("Object2");
        describe2.setTenantId("74255");
        describeMap.put("Object2", describe2);

        // 配置Mock行为
        when(mockFieldNode.getObjectApiName()).thenReturn("Object1");
        when(mockFieldNode.getFieldApiName()).thenReturn("field1");
        when(mockFieldNode2.getObjectApiName()).thenReturn("Object2");
        when(mockFieldNode2.getFieldApiName()).thenReturn("field2");

        Set<FieldNode> nodes = Sets.newHashSet(mockFieldNode, mockFieldNode2);
        when(mockValueGraph.nodes()).thenReturn(nodes);

        fieldRelationGraph = FieldRelationGraph.of(mockValueGraph, describeMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldRelationGraph.of静态工厂方法创建对象
     */
    @Test
    @DisplayName("创建对象 - 使用of静态工厂方法")
    void testOf_创建对象() {
        // 执行被测试方法
        FieldRelationGraph graph = FieldRelationGraph.of(mockValueGraph, describeMap);

        // 验证结果
        assertNotNull(graph);
        assertEquals(mockValueGraph, graph.getGraph());
        assertEquals(describeMap, graph.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试transpose方法创建转置图
     */
    @Test
    @DisplayName("转置图 - 正常场景")
    void testTranspose_正常场景() {
        try (MockedStatic<com.facishare.paas.appframework.common.graph.Graphs> mockedGraphs = mockStatic(com.facishare.paas.appframework.common.graph.Graphs.class)) {
            // 准备测试数据
            ValueGraph<FieldNode, RelateEdge> transposedGraph = mock(ValueGraph.class);

            // 配置Mock行为
            mockedGraphs.when(() -> com.facishare.paas.appframework.common.graph.Graphs.transpose(mockValueGraph)).thenReturn(transposedGraph);

            // 执行被测试方法
            FieldRelationGraph result = fieldRelationGraph.transpose();

            // 验证结果
            assertNotNull(result);
            assertEquals(transposedGraph, result.getGraph());
            assertEquals(describeMap, result.getDescribeMap());

            // 验证Mock交互
            mockedGraphs.verify(() -> com.facishare.paas.appframework.common.graph.Graphs.transpose(mockValueGraph));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试reachableNodes方法获取可达节点
     */
    @Test
    @DisplayName("可达节点 - 正常场景")
    void testReachableNodes_正常场景() {
        try (MockedStatic<com.facishare.paas.appframework.common.graph.Graphs> mockedGraphs = mockStatic(com.facishare.paas.appframework.common.graph.Graphs.class)) {
            // 准备测试数据
            Set<FieldNode> reachableNodesWithSelf = Sets.newHashSet(mockFieldNode, mockFieldNode2);

            // 配置Mock行为
            mockedGraphs.when(() -> com.facishare.paas.appframework.common.graph.Graphs.reachableNodes(mockValueGraph, mockFieldNode))
                    .thenReturn(reachableNodesWithSelf);

            // 执行被测试方法
            Set<FieldNode> result = fieldRelationGraph.reachableNodes(mockFieldNode);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size()); // 应该排除自身节点
            assertTrue(result.contains(mockFieldNode2));
            assertFalse(result.contains(mockFieldNode)); // 自身节点应该被移除

            // 验证Mock交互
            mockedGraphs.verify(() -> com.facishare.paas.appframework.common.graph.Graphs.reachableNodes(mockValueGraph, mockFieldNode));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNode方法获取指定节点
     */
    @Test
    @DisplayName("获取节点 - 正常场景")
    void testGetNode_正常场景() {
        // 执行被测试方法
        Optional<FieldNode> result = fieldRelationGraph.getNode("Object1", "field1");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(mockFieldNode, result.get());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNode方法获取不存在的节点
     */
    @Test
    @DisplayName("获取节点 - 节点不存在")
    void testGetNode_节点不存在() {
        // 执行被测试方法
        Optional<FieldNode> result = fieldRelationGraph.getNode("NonExistentObject", "nonExistentField");

        // 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelateNodes方法获取关联节点
     */
    @Test
    @DisplayName("获取关联节点 - 正常场景")
    void testGetRelateNodes_正常场景() {
        // 准备测试数据
        Set<String> expectedObjectApiNames = Sets.newHashSet("Object1", "Object2");

        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.getRelateNodes(mockFieldNode, expectedObjectApiNames);

        // 验证结果
        assertNotNull(result);
        // 由于这是一个复杂的内部实现，我们主要验证方法不抛异常且返回非null结果
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelateNodes方法带tenantId参数的重载版本
     */
    @Test
    @DisplayName("获取关联节点 - 带tenantId参数")
    void testGetRelateNodes_带tenantId参数() {
        // 准备测试数据
        Set<String> describeApiNames = Sets.newHashSet("Object1", "Object2");
        String tenantId = "74255";

        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.getRelateNodes(mockFieldNode, describeApiNames, tenantId);

        // 验证结果
        assertNotNull(result);
        // 由于这个方法涉及灰度配置和复杂的内部逻辑，我们主要验证方法不抛异常
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDescribeMap方法获取描述映射
     */
    @Test
    @DisplayName("获取描述映射 - 正常场景")
    void testGetDescribeMap_正常场景() {
        // 执行被测试方法
        Map<String, IObjectDescribe> result = fieldRelationGraph.getDescribeMap();

        // 验证结果
        assertNotNull(result);
        assertEquals(describeMap, result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("Object1"));
        assertTrue(result.containsKey("Object2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法获取底层图结构
     */
    @Test
    @DisplayName("获取图结构 - 正常场景")
    void testGetGraph_正常场景() {
        // 执行被测试方法
        ValueGraph<FieldNode, RelateEdge> result = fieldRelationGraph.getGraph();

        // 验证结果
        assertNotNull(result);
        assertEquals(mockValueGraph, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNode方法在节点存在时的场景
     */
    @Test
    @DisplayName("获取节点 - 节点存在")
    void testGetNode_节点存在() {
        // 配置Mock行为
        when(mockValueGraph.nodes()).thenReturn(Sets.newHashSet(mockFieldNode));
        when(mockFieldNode.getObjectApiName()).thenReturn("Object1");
        when(mockFieldNode.getFieldApiName()).thenReturn("field1");

        // 执行被测试方法
        Optional<FieldNode> result = fieldRelationGraph.getNode("Object1", "field1");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(mockFieldNode, result.get());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelateNodes方法在空集合参数时的场景
     */
    @Test
    @DisplayName("获取关联节点 - 空集合参数")
    void testGetRelateNodes_空集合参数() {
        // 准备测试数据
        Set<String> emptyObjectApiNames = Sets.newHashSet();

        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.getRelateNodes(mockFieldNode, emptyObjectApiNames);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRelateNodes方法在null参数时的场景
     */
    @Test
    @DisplayName("获取关联节点 - null参数")
    void testGetRelateNodes_null参数() {
        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.getRelateNodes(mockFieldNode, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带tenantId参数的getRelateNodes方法在null tenantId时的场景
     */
    @Test
    @DisplayName("获取关联节点 - null tenantId")
    void testGetRelateNodes_null_tenantId() {
        // 准备测试数据
        Set<String> describeApiNames = Sets.newHashSet("Object1");

        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.getRelateNodes(mockFieldNode, describeApiNames, null);

        // 验证结果
        assertNotNull(result);
        // 当tenantId为null时，应该回退到不带tenantId的方法
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of静态工厂方法在describeMap为空时的场景
     */
    @Test
    @DisplayName("创建对象 - 空描述映射")
    void testOf_空描述映射() {
        // 准备测试数据
        Map<String, IObjectDescribe> emptyDescribeMap = Maps.newHashMap();

        // 执行被测试方法
        FieldRelationGraph graph = FieldRelationGraph.of(mockValueGraph, emptyDescribeMap);

        // 验证结果
        assertNotNull(graph);
        assertEquals(mockValueGraph, graph.getGraph());
        assertEquals(emptyDescribeMap, graph.getDescribeMap());
        assertTrue(graph.getDescribeMap().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNode方法的性能场景 - 大量节点查找
     */
    @Test
    @DisplayName("获取节点 - 基本功能测试")
    void testGetNode_基本功能测试() {
        // 准备测试数据 - 创建一个真实的FieldRelationGraph实例
        Set<FieldNode> nodes = Sets.newHashSet();
        FieldNode targetNode = mock(FieldNode.class);
        when(targetNode.getObjectApiName()).thenReturn("TestObject");
        when(targetNode.getFieldApiName()).thenReturn("testField");
        nodes.add(targetNode);

        when(mockValueGraph.nodes()).thenReturn(nodes);

        // 创建一个新的FieldRelationGraph实例，这样nodeTable会被正确填充
        FieldRelationGraph testGraph = FieldRelationGraph.of(mockValueGraph, describeMap);

        // 执行被测试方法 - 查找存在的节点
        Optional<FieldNode> result = testGraph.getNode("TestObject", "testField");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("TestObject", result.get().getObjectApiName());
        assertEquals("testField", result.get().getFieldApiName());

        // 测试查找不存在的节点
        Optional<FieldNode> notFoundResult = testGraph.getNode("NonExistent", "field");
        assertFalse(notFoundResult.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nodes方法通过委托获取所有节点
     */
    @Test
    @DisplayName("获取所有节点 - 委托方法")
    void testNodes_委托方法() {
        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.nodes();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(mockFieldNode));
        assertTrue(result.contains(mockFieldNode2));

        // 验证Mock交互
        verify(mockValueGraph, atLeastOnce()).nodes();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试predecessors方法通过委托获取前驱节点
     */
    @Test
    @DisplayName("获取前驱节点 - 委托方法")
    void testPredecessors_委托方法() {
        // 准备测试数据
        Set<FieldNode> expectedPredecessors = Sets.newHashSet(mockFieldNode2);

        // 配置Mock行为
        when(mockValueGraph.predecessors(mockFieldNode)).thenReturn(expectedPredecessors);

        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.predecessors(mockFieldNode);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPredecessors, result);

        // 验证Mock交互
        verify(mockValueGraph).predecessors(mockFieldNode);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试successors方法通过委托获取后继节点
     */
    @Test
    @DisplayName("获取后继节点 - 委托方法")
    void testSuccessors_委托方法() {
        // 准备测试数据
        Set<FieldNode> expectedSuccessors = Sets.newHashSet(mockFieldNode2);

        // 配置Mock行为
        when(mockValueGraph.successors(mockFieldNode)).thenReturn(expectedSuccessors);

        // 执行被测试方法
        Set<FieldNode> result = fieldRelationGraph.successors(mockFieldNode);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedSuccessors, result);

        // 验证Mock交互
        verify(mockValueGraph).successors(mockFieldNode);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建FieldRelationGraph时传入null参数的异常处理
     */
    @Test
    @DisplayName("异常处理 - 传入null ValueGraph")
    void testOf_传入null_ValueGraph() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            FieldRelationGraph.of(null, describeMap);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建FieldRelationGraph时传入null describeMap的处理
     */
    @Test
    @DisplayName("边界条件 - 传入null describeMap")
    void testOf_传入null_describeMap() {
        // 执行被测试方法
        FieldRelationGraph graph = FieldRelationGraph.of(mockValueGraph, null);

        // 验证结果
        assertNotNull(graph);
        assertEquals(mockValueGraph, graph.getGraph());
        assertNull(graph.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试reachableNodes方法在没有可达节点时的场景
     */
    @Test
    @DisplayName("可达节点 - 没有可达节点")
    void testReachableNodes_没有可达节点() {
        try (MockedStatic<com.facishare.paas.appframework.common.graph.Graphs> mockedGraphs = mockStatic(com.facishare.paas.appframework.common.graph.Graphs.class)) {
            // 准备测试数据 - 只包含自身节点
            Set<FieldNode> onlySelfNode = Sets.newHashSet(mockFieldNode);

            // 配置Mock行为
            mockedGraphs.when(() -> com.facishare.paas.appframework.common.graph.Graphs.reachableNodes(mockValueGraph, mockFieldNode))
                    .thenReturn(onlySelfNode);

            // 执行被测试方法
            Set<FieldNode> result = fieldRelationGraph.reachableNodes(mockFieldNode);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty()); // 移除自身节点后应该为空
        }
    }
}
