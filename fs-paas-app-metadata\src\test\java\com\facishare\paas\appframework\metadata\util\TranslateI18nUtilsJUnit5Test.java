package com.facishare.paas.appframework.metadata.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for TranslateI18nUtils
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("TranslateI18nUtils 单元测试")
class TranslateI18nUtilsJUnit5Test {

    // ==================== 常量测试 ====================

    @Test
    @DisplayName("常量值验证")
    void testConstants() {
        assertEquals("trans_tag_manager_tag_group_", TranslateI18nUtils.TRANS_TAG_MANAGER_TAG_GROUP);
        assertEquals("trans_tag_manager_sub_tag_", TranslateI18nUtils.TRANS_TAG_MANAGER_SUB_TAG);
        assertEquals("ButtonFormVariable", TranslateI18nUtils.NEW_BUTTON_FIELD);
    }

    // ==================== getTransTagManagerTagGroup 方法测试 ====================

    @Test
    @DisplayName("获取标签管理器标签组 - 正常情况")
    void testGetTransTagManagerTagGroup_Success() {
        // Arrange
        String groupApiName = "test_group";
        String expectedKey = "trans_tag_manager_tag_group_test_group";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器标签组 - 复杂组名")
    void testGetTransTagManagerTagGroup_ComplexGroupName() {
        // Arrange
        String groupApiName = "complex_business_group_v2";
        String expectedKey = "trans_tag_manager_tag_group_complex_business_group_v2";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器标签组 - 空字符串组名")
    void testGetTransTagManagerTagGroup_EmptyGroupName() {
        // Arrange
        String groupApiName = "";
        String expectedKey = "trans_tag_manager_tag_group_";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器标签组 - null组名")
    void testGetTransTagManagerTagGroup_NullGroupName() {
        // Arrange
        String groupApiName = null;
        String expectedKey = "trans_tag_manager_tag_group_null";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器标签组 - 特殊字符组名")
    void testGetTransTagManagerTagGroup_SpecialCharacters() {
        // Arrange
        String groupApiName = "group-with.special_chars@123";
        String expectedKey = "trans_tag_manager_tag_group_group-with.special_chars@123";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerTagGroup(groupApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    // ==================== getTransTagManagerSubTag 方法测试 ====================

    @Test
    @DisplayName("获取标签管理器子标签 - 正常情况")
    void testGetTransTagManagerSubTag_Success() {
        // Arrange
        String tagApiName = "test_tag";
        String expectedKey = "trans_tag_manager_sub_tag_test_tag";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器子标签 - 复杂标签名")
    void testGetTransTagManagerSubTag_ComplexTagName() {
        // Arrange
        String tagApiName = "customer_priority_tag_high";
        String expectedKey = "trans_tag_manager_sub_tag_customer_priority_tag_high";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器子标签 - 空字符串标签名")
    void testGetTransTagManagerSubTag_EmptyTagName() {
        // Arrange
        String tagApiName = "";
        String expectedKey = "trans_tag_manager_sub_tag_";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器子标签 - null标签名")
    void testGetTransTagManagerSubTag_NullTagName() {
        // Arrange
        String tagApiName = null;
        String expectedKey = "trans_tag_manager_sub_tag_null";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取标签管理器子标签 - 特殊字符标签名")
    void testGetTransTagManagerSubTag_SpecialCharacters() {
        // Arrange
        String tagApiName = "tag_with-special.chars#456";
        String expectedKey = "trans_tag_manager_sub_tag_tag_with-special.chars#456";

        // Act
        String result = TranslateI18nUtils.getTransTagManagerSubTag(tagApiName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    // ==================== getButtonParamKey 方法测试 ====================

    @Test
    @DisplayName("获取按钮参数键 - 正常情况")
    void testGetButtonParamKey_Success() {
        // Arrange
        String tenantId = "123456789";
        String objApi = "TestObject";
        String buttonApi = "save_button";
        String paramKey = "confirm_message";
        String expectedKey = "ButtonFormVariable.123456789.TestObject.save_button.confirm_message";

        // Act
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取按钮参数键 - 复杂参数")
    void testGetButtonParamKey_ComplexParameters() {
        // Arrange
        String tenantId = "tenant_987654321";
        String objApi = "CustomerManagementObject";
        String buttonApi = "advanced_search_button";
        String paramKey = "search_criteria_validation";
        String expectedKey = "ButtonFormVariable.tenant_987654321.CustomerManagementObject.advanced_search_button.search_criteria_validation";

        // Act
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取按钮参数键 - 空字符串参数")
    void testGetButtonParamKey_EmptyParameters() {
        // Arrange
        String tenantId = "";
        String objApi = "";
        String buttonApi = "";
        String paramKey = "";
        String expectedKey = "ButtonFormVariable....";

        // Act
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取按钮参数键 - null参数")
    void testGetButtonParamKey_NullParameters() {
        // Arrange
        String tenantId = null;
        String objApi = null;
        String buttonApi = null;
        String paramKey = null;
        String expectedKey = "ButtonFormVariable.null.null.null.null";

        // Act
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    @Test
    @DisplayName("获取按钮参数键 - 混合null和非null参数")
    void testGetButtonParamKey_MixedNullParameters() {
        // Arrange & Act & Assert
        String result1 = TranslateI18nUtils.getButtonParamKey("123", null, "button", "param");
        assertEquals("ButtonFormVariable.123.null.button.param", result1);

        String result2 = TranslateI18nUtils.getButtonParamKey(null, "Object", null, "param");
        assertEquals("ButtonFormVariable.null.Object.null.param", result2);

        String result3 = TranslateI18nUtils.getButtonParamKey("123", "Object", "button", null);
        assertEquals("ButtonFormVariable.123.Object.button.null", result3);
    }

    @Test
    @DisplayName("获取按钮参数键 - 特殊字符参数")
    void testGetButtonParamKey_SpecialCharacters() {
        // Arrange
        String tenantId = "tenant-123.456";
        String objApi = "Object@Special#Chars";
        String buttonApi = "button_with-dots.and_underscores";
        String paramKey = "param$with%special&chars";
        String expectedKey = "ButtonFormVariable.tenant-123.456.Object@Special#Chars.button_with-dots.and_underscores.param$with%special&chars";

        // Act
        String result = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // Assert
        assertNotNull(result);
        assertEquals(expectedKey, result);
    }

    // ==================== 组合测试 ====================

    @Test
    @DisplayName("组合测试 - 不同方法的键格式一致性")
    void testCombined_KeyFormatConsistency() {
        // Arrange
        String groupName = "test_group";
        String tagName = "test_tag";
        String tenantId = "123456";
        String objApi = "TestObject";
        String buttonApi = "test_button";
        String paramKey = "test_param";

        // Act
        String tagGroupKey = TranslateI18nUtils.getTransTagManagerTagGroup(groupName);
        String subTagKey = TranslateI18nUtils.getTransTagManagerSubTag(tagName);
        String buttonParamKey = TranslateI18nUtils.getButtonParamKey(tenantId, objApi, buttonApi, paramKey);

        // Assert - 验证键格式一致性
        assertTrue(tagGroupKey.startsWith("trans_tag_manager_tag_group_"));
        assertTrue(tagGroupKey.endsWith(groupName));
        
        assertTrue(subTagKey.startsWith("trans_tag_manager_sub_tag_"));
        assertTrue(subTagKey.endsWith(tagName));
        
        assertTrue(buttonParamKey.startsWith("ButtonFormVariable."));
        assertTrue(buttonParamKey.contains(tenantId));
        assertTrue(buttonParamKey.contains(objApi));
        assertTrue(buttonParamKey.contains(buttonApi));
        assertTrue(buttonParamKey.endsWith(paramKey));

        // 验证键的分段数量
        // 标签组键和子标签键都以下划线结尾，所以包含下划线
        assertTrue(tagGroupKey.contains("_"));
        assertTrue(subTagKey.contains("_"));
        assertEquals(5, buttonParamKey.split("\\.", -1).length);
    }

    @Test
    @DisplayName("组合测试 - 键的唯一性验证")
    void testCombined_KeyUniqueness() {
        // Arrange
        String commonName = "common_name";

        // Act
        String tagGroupKey = TranslateI18nUtils.getTransTagManagerTagGroup(commonName);
        String subTagKey = TranslateI18nUtils.getTransTagManagerSubTag(commonName);
        String buttonParamKey = TranslateI18nUtils.getButtonParamKey(commonName, commonName, commonName, commonName);

        // Assert - 验证不同方法生成的键是唯一的
        assertNotEquals(tagGroupKey, subTagKey);
        assertNotEquals(tagGroupKey, buttonParamKey);
        assertNotEquals(subTagKey, buttonParamKey);

        // 验证键的业务语义
        assertTrue(tagGroupKey.contains("tag_group"), "标签组键应包含tag_group标识");
        assertTrue(subTagKey.contains("sub_tag"), "子标签键应包含sub_tag标识");
        assertTrue(buttonParamKey.contains("ButtonFormVariable"), "按钮参数键应包含ButtonFormVariable标识");
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 极长字符串")
    void testEdgeCase_VeryLongStrings() {
        // Arrange
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longString.append("very_long_string_segment_").append(i).append("_");
        }
        String longName = longString.toString();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String tagGroupKey = TranslateI18nUtils.getTransTagManagerTagGroup(longName);
            String subTagKey = TranslateI18nUtils.getTransTagManagerSubTag(longName);
            String buttonParamKey = TranslateI18nUtils.getButtonParamKey(longName, longName, longName, longName);

            // 验证结果不为空且包含长字符串
            assertNotNull(tagGroupKey);
            assertNotNull(subTagKey);
            assertNotNull(buttonParamKey);
            
            assertTrue(tagGroupKey.contains(longName));
            assertTrue(subTagKey.contains(longName));
            assertTrue(buttonParamKey.contains(longName));
        });
    }

    @Test
    @DisplayName("边界测试 - 国际化字符")
    void testEdgeCase_InternationalCharacters() {
        // Arrange
        String chineseName = "中文标签";
        String japaneseName = "日本語タグ";
        String arabicName = "العربية";
        String emojiName = "😀🎉🚀";

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String chineseTagGroupKey = TranslateI18nUtils.getTransTagManagerTagGroup(chineseName);
            String japaneseSubTagKey = TranslateI18nUtils.getTransTagManagerSubTag(japaneseName);
            String arabicButtonParamKey = TranslateI18nUtils.getButtonParamKey(arabicName, arabicName, arabicName, arabicName);
            String emojiButtonParamKey = TranslateI18nUtils.getButtonParamKey(emojiName, emojiName, emojiName, emojiName);

            // 验证结果包含国际化字符
            assertTrue(chineseTagGroupKey.contains(chineseName));
            assertTrue(japaneseSubTagKey.contains(japaneseName));
            assertTrue(arabicButtonParamKey.contains(arabicName));
            assertTrue(emojiButtonParamKey.contains(emojiName));
        });
    }

    // ==================== 性能测试 ====================

    @Test
    @DisplayName("性能测试 - 大量键生成")
    void testPerformance_MassiveKeyGeneration() {
        // Arrange
        int testCount = 10000;
        String[] names = new String[testCount];
        
        for (int i = 0; i < testCount; i++) {
            names[i] = "name_" + i;
        }

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            String tagGroupKey = TranslateI18nUtils.getTransTagManagerTagGroup(names[i]);
            String subTagKey = TranslateI18nUtils.getTransTagManagerSubTag(names[i]);
            String buttonParamKey = TranslateI18nUtils.getButtonParamKey(names[i], names[i], names[i], names[i]);
            
            // 简单验证
            assertNotNull(tagGroupKey);
            assertNotNull(subTagKey);
            assertNotNull(buttonParamKey);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：30000次操作应该在1秒内完成
        assertTrue(duration < 1000, "Performance test failed: took " + duration + "ms for " + (testCount * 3) + " operations");
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的国际化键构建流程")
    void testIntegration_CompleteI18nKeyBuildingFlow() {
        // Arrange - 模拟真实业务场景
        String businessGroupName = "customer_management_group";
        String businessTagName = "high_priority_customer";
        String businessTenantId = "company_123456";
        String businessObjectApi = "CustomerObject";
        String businessButtonApi = "create_customer_button";
        String businessParamKey = "validation_message";

        // Act - 执行完整的键构建流程
        // 1. 构建标签管理相关键
        String tagGroupKey = TranslateI18nUtils.getTransTagManagerTagGroup(businessGroupName);
        String subTagKey = TranslateI18nUtils.getTransTagManagerSubTag(businessTagName);
        
        // 2. 构建按钮参数键
        String buttonParamKey = TranslateI18nUtils.getButtonParamKey(
                businessTenantId, businessObjectApi, businessButtonApi, businessParamKey);

        // Assert - 验证完整流程结果
        // 验证标签管理键
        assertEquals("trans_tag_manager_tag_group_customer_management_group", tagGroupKey);
        assertEquals("trans_tag_manager_sub_tag_high_priority_customer", subTagKey);
        
        // 验证按钮参数键
        assertEquals("ButtonFormVariable.company_123456.CustomerObject.create_customer_button.validation_message", buttonParamKey);
        
        // 验证键的业务语义
        assertTrue(tagGroupKey.contains("customer_management_group"), "标签组键应包含业务组名称");
        assertTrue(subTagKey.contains("high_priority_customer"), "子标签键应包含业务标签名称");
        assertTrue(buttonParamKey.contains("company_123456"), "按钮参数键应包含租户ID");
        assertTrue(buttonParamKey.contains("CustomerObject"), "按钮参数键应包含对象API名称");
        assertTrue(buttonParamKey.contains("create_customer_button"), "按钮参数键应包含按钮API名称");
        assertTrue(buttonParamKey.contains("validation_message"), "按钮参数键应包含参数键名称");
        
        // 验证键的唯一性
        java.util.Set<String> uniqueKeys = new java.util.HashSet<>();
        uniqueKeys.add(tagGroupKey);
        uniqueKeys.add(subTagKey);
        uniqueKeys.add(buttonParamKey);
        assertEquals(3, uniqueKeys.size(), "所有生成的键应该是唯一的");
        
        // 验证键的格式规范
        assertFalse(tagGroupKey.contains(".."), "标签组键不应包含连续的分隔符");
        assertFalse(subTagKey.contains(".."), "子标签键不应包含连续的分隔符");
        assertFalse(buttonParamKey.contains(".."), "按钮参数键不应包含连续的分隔符");
    }
}
