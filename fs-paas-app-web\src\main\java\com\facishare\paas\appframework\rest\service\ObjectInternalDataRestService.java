package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.rest.dto.data.FindById;
import com.facishare.paas.appframework.rest.dto.data.FindByIds;
import com.facishare.paas.appframework.rest.dto.data.FindRecordName;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.RichText;
import com.facishare.paas.metadata.impl.NameCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

@Slf4j
@Component
@ServiceModule("rest_internal_data")
public class ObjectInternalDataRestService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    InfraServiceFacade infraServiceFacade;
    @Autowired
    private DataSnapshotLogicService dataSnapshotLogicService;

    public FindById.Result findById(FindById.Arg arg, RequestContext requestContext) {
        StopWatch stopWatch = StopWatch.create("InternalDataRest.findById");

        FindById.Result result = new FindById.Result();
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescAPIName());
        stopWatch.lap("findObject");

        // 处理三角级联关系
        ObjectDescribeExt.of(describe).fillParentLookup();
        ObjectDescribeExt.of(describe).fillCascadeDetailLookup();

        IObjectData found;
        if (arg.isIncludeDeleted()) {
            found = serviceFacade.findObjectDataIgnoreStatus(requestContext.getUser(), arg.getDataId(), arg.getDescAPIName());
        } else {
            IActionContext context = ActionContextExt.of(requestContext.getUser()).setSkipRelevantTeam(RequestUtil.skipRelevantTeam()).disableDeepQuote().getContext();
            found = serviceFacade.getObjectDataIncludeDeleted(context, arg.getDataId(), arg.getDescAPIName());
        }
        stopWatch.lap("findObjectDataIncludeDeleted");

        if (!arg.isUseDbCalculateValue()) {
            //实时计算所有统计字段
            if (AppFrameworkConfig.isGrayCalculateCountWhenFindData(requestContext.getTenantId(), describe.getApiName())) {
                List<Count> countList = ObjectDescribeExt.of(describe).getCountFields();
                serviceFacade.calculateCountField(Lists.newArrayList(found), describe, countList);
                stopWatch.lap("calculateCountField");
            }

            //实时计算所有计算字段
            infraServiceFacade.bulkCalculate(describe, Lists.newArrayList(found));
            stopWatch.lap("bulkCalculate");
        }

        fillExtendFieldInfo(stopWatch, arg, requestContext, found, describe);

        if (arg.isUseSnapshotForApproval()) {
            ObjectDataSnapshot snapshot = dataSnapshotLogicService.findAndMergeSnapshot(requestContext.getTenantId(),
                    arg.getDescAPIName(), arg.getDataId(), ApprovalFlow.getCode(), RequestUtil.getOtherBizId());
            stopWatch.lap("findAndMergeSnapshot");
            //计算变更之后的统计字段和计算字段
            IObjectData snapshotData = serviceFacade.calculateForSnapshot(requestContext.getUser(), describe, found, snapshot);
            stopWatch.lap("calculateForSnapshot");
            //跟数据库里的数据做一次diff，diff结果供审批流展示
            Map<String, Object> diffMap = ObjectDataExt.of(found).diff(snapshotData, describe);
            //补充查找关联、人员、部门、引用字段的显示信息
            fillExtendFieldInfo(stopWatch, arg, requestContext, ObjectDataExt.of(diffMap).getObjectData(), describe);
            ObjectDataExt.of(diffMap).handleMultiLangField(describe);
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXTRACT_COOPERATIVE_RICH_TEXT_ABSTRACT_GRAY, requestContext.getTenantId())) {
                List<RichText> richTextFields = ObjectDescribeExt.of(describe).getCooperativeRichTextFields();
                ObjectDataExt.of(diffMap).extractCooperativeRichTextAbstractWithFields(richTextFields);
            }
            result.setSnapshot(diffMap);
        }

        //格式化数据
        if (arg.isFormatData()) {
            ObjectDataFormatter.builder().describe(describe).dataList(Lists.newArrayList(found)).build().format();
        }
        result.setObjectDataDocument(ObjectDataDocument.of(found));

        if (arg.isIncludeDescribe()) {
            if (arg.isIncludeStatistics()) {
                infraServiceFacade.computeCalculateRelation(describe, null);
                stopWatch.lap("computeCalculateRelation");
            }
            result.setDescribeDocument(ObjectDescribeDocument.of(describe));
        }

        stopWatch.logSlow(200);

        return result;
    }

    private void fillExtendFieldInfo(StopWatch stopWatch, FindById.Arg arg, RequestContext requestContext,
                                     IObjectData data, IObjectDescribe describe) {
        //填充lookup字段的__r的值
        if (arg.isIncludeLookup() || arg.isFillExtendField()) {
            serviceFacade.fillObjectDataWithRefObject(describe, Lists.newArrayList(data), requestContext.getUser(),
                    null, true);
            stopWatch.lap("fillObjectDataWithRefObject");
            serviceFacade.parsePaymentObjOrderNames(describe, Lists.newArrayList(data), requestContext.getUser(), false);
            stopWatch.lap("parsePaymentObjOrderNames");
        }

        //填充处理引用字段的值
        if (arg.isIncludeQuoteValue() || arg.isFillExtendField()) {
            infraServiceFacade.fillQuoteFieldValue(requestContext.getUser(), Lists.newArrayList(data), describe, false);
            stopWatch.lap("fillQuoteFieldValue");
        }

        if (arg.isFillExtendField()) {
            serviceFacade.fillCountryAreaLabel(describe, Lists.newArrayList(data), requestContext.getUser());
            stopWatch.lap("fillCountryAreaLabel");
            serviceFacade.fillUserInfo(describe, Lists.newArrayList(data), requestContext.getUser());
            stopWatch.lap("fillUserInfo");
            serviceFacade.fillDepartmentInfo(describe, Lists.newArrayList(data), requestContext.getUser());
            stopWatch.lap("fillDepartmentInfo");
            serviceFacade.fillDataVisibilityRange(requestContext.getUser(), describe, Lists.newArrayList(data));
            stopWatch.lap("fillDataVisibilityRange");
            serviceFacade.fillCurrencyFieldInfo(describe, Lists.newArrayList(data), requestContext.getUser());
            stopWatch.lap("fillCurrencyFieldInfo");
            serviceFacade.fillDimensionFieldValue(requestContext.getUser(), describe, Lists.newArrayList(data));
            stopWatch.lap("fillDimensionFieldValue");
        }

        if (arg.isFillMaskField()) {
            serviceFacade.fillMaskFieldValue(requestContext.getUser(), Lists.newArrayList(data), describe, false);
            stopWatch.lap("fillMaskFieldValue");
        }
    }

    public FindRecordName.Result findRecordName(FindRecordName.Arg arg, RequestContext requestContext) {
        FindRecordName.Result result = new FindRecordName.Result();
        IActionContext context = ActionContextExt.of(requestContext.getUser(),
                requestContext).getContext();
        List<INameCache> nameList = serviceFacade.findRecordName(context, arg.getDescAPIName(), arg.getIdList());
        List<ObjectDataDocument> data = new ArrayList<>();
        nameList.forEach(x -> data.add(ObjectDataDocument.of(((NameCache) x).getContainerDocument())));
        result.setObjectData(data);

        return result;
    }

    public FindByIds.Result findDataByIds(FindByIds.Arg arg, RequestContext requestContext) {
        FindByIds.Result result = new FindByIds.Result();
        //查询描述不做拷贝
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_COPY_DESCRIBE_WHEN_QUERY_DATA_EI, requestContext.getTenantId())) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, true);
        }
        IObjectDescribe describe = serviceFacade.findObject(requestContext.getTenantId(), arg.getDescAPIName());

        List<String> idList = arg.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            throw new ValidateException(I18nMessage.of(I18NKey.ID_LIST_INVALID, I18N.text(I18NKey.ID_LIST_INVALID)));
        }

        ActionContextExt context = ActionContextExt.of(requestContext.getUser(), requestContext).setSkipRelevantTeam(RequestUtil.skipRelevantTeam()).disableDeepQuote();
        List<IObjectData> foundList = serviceFacade.findObjectDataByIds(context.getContext(), idList,
                arg.getDescAPIName());

        //实时计算所有计算字段
        if (!arg.isUseDbCalculateValue()) {
            infraServiceFacade.bulkCalculate(describe, foundList);
        }

        //填充lookup字段的__r的值
        if (arg.isIncludeLookup()) {
            serviceFacade.fillObjectDataWithRefObject(describe, foundList, requestContext.getUser(),
                    null, true);
        }

        //填充处理引用字段的值
        if (arg.isIncludeQuoteValue()) {
            infraServiceFacade.fillQuoteFieldValue(requestContext.getUser(), foundList, describe, false);
        }

        List<ObjectDataDocument> data = new ArrayList<>();
        foundList.forEach(x -> data.add(ObjectDataDocument.of((getData(x, describe)))));
        result.setObjectData(data);
        return result;
    }

    @SuppressWarnings("unchecked")
    private Map getData(IObjectData found, IObjectDescribe describe) {
        Map doc = ObjectDataExt.of(found).toMap();

        List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
        fieldDescribes.forEach(x -> {
            if (Objects.equals(x.isActive(), Boolean.FALSE)) {
                doc.remove(x.getApiName());
            } else if (!doc.containsKey(x.getApiName())) {
                doc.put(x.getApiName(), null);
            }
        });

        return doc;
    }

}
