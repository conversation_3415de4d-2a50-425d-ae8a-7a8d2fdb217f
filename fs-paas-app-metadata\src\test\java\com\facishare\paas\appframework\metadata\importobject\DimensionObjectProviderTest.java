package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.metadata.util.OrganizationConfigUtil;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DimensionObjectProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IUniqueRule uniqueRule;

    private DimensionObjectProvider dimensionObjectProvider;
    private String testTenantId;

    @BeforeEach
    void setUp() {
        dimensionObjectProvider = new DimensionObjectProvider();
        testTenantId = "74255";
        when(objectDescribe.getTenantId()).thenReturn(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的维度对象API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的维度对象API名称")
    void testGetObjectCode_返回正确的维度对象API名称() {
        // 执行被测试方法
        String result = dimensionObjectProvider.getObjectCode();

        // 验证结果
        assertEquals(Utils.DIMENSION_OBJ_API_NAME, result);
        assertEquals("DimensionObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDuplicateJudgmentType方法返回不支持新建导入类型
     */
    @Test
    @DisplayName("getDuplicateJudgmentType - 返回不支持新建导入类型")
    void testGetDuplicateJudgmentType_返回不支持新建导入类型() {
        // 执行被测试方法
        int result = dimensionObjectProvider.getDuplicateJudgmentType(objectDescribe);

        // 验证结果
        assertEquals(DefaultObjectImportProvider.UNSUPPORT_INSERT_IMPORT, result);
        assertEquals(3, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当灰度配置开启时，getImportType方法返回默认导入类型
     */
    @Test
    @DisplayName("getImportType - 灰度开启时返回默认导入类型")
    void testGetImportType_灰度开启时返回默认导入类型() {
        try (MockedStatic<OrganizationConfigUtil> mockedOrganizationConfigUtil = mockStatic(OrganizationConfigUtil.class)) {
            // 配置Mock行为
            mockedOrganizationConfigUtil.when(() -> OrganizationConfigUtil.isImportDimensionGray(testTenantId))
                    .thenReturn(true);

            // 执行被测试方法
            ImportType result = dimensionObjectProvider.getImportType(objectDescribe, uniqueRule);

            // 验证结果
            assertEquals(ImportType.DEFAULT, result);
            assertEquals(1, result.getType());

            // 验证Mock交互
            mockedOrganizationConfigUtil.verify(() -> OrganizationConfigUtil.isImportDimensionGray(testTenantId));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当灰度配置关闭时，getImportType方法返回不支持新建导入类型
     */
    @Test
    @DisplayName("getImportType - 灰度关闭时返回不支持新建导入类型")
    void testGetImportType_灰度关闭时返回不支持新建导入类型() {
        try (MockedStatic<OrganizationConfigUtil> mockedOrganizationConfigUtil = mockStatic(OrganizationConfigUtil.class)) {
            // 配置Mock行为
            mockedOrganizationConfigUtil.when(() -> OrganizationConfigUtil.isImportDimensionGray(testTenantId))
                    .thenReturn(false);

            // 执行被测试方法
            ImportType result = dimensionObjectProvider.getImportType(objectDescribe, uniqueRule);

            // 验证结果
            assertEquals(ImportType.UNSUPPORT_INSERT_IMPORT, result);
            assertEquals(3, result.getType());

            // 验证Mock交互
            mockedOrganizationConfigUtil.verify(() -> OrganizationConfigUtil.isImportDimensionGray(testTenantId));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMatchingTypesByInsert方法返回包含NAME的匹配类型列表
     */
    @Test
    @DisplayName("getMatchingTypesByInsert - 返回包含NAME的匹配类型列表")
    void testGetMatchingTypesByInsert_返回包含NAME的匹配类型列表() {
        // 执行被测试方法
        List<MatchingType> result = dimensionObjectProvider.getMatchingTypesByInsert(objectDescribe);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MatchingType.NAME, result.get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMatchingTypesByUpdate方法返回包含NAME的匹配类型列表
     */
    @Test
    @DisplayName("getMatchingTypesByUpdate - 返回包含NAME的匹配类型列表")
    void testGetMatchingTypesByUpdate_返回包含NAME的匹配类型列表() {
        // 执行被测试方法
        List<MatchingType> result = dimensionObjectProvider.getMatchingTypesByUpdate(objectDescribe);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MatchingType.NAME, result.get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当tenantId为null时，getImportType方法的处理
     */
    @Test
    @DisplayName("getImportType - tenantId为null时的处理")
    void testGetImportType_tenantId为null时的处理() {
        try (MockedStatic<OrganizationConfigUtil> mockedOrganizationConfigUtil = mockStatic(OrganizationConfigUtil.class)) {
            // 准备测试数据
            when(objectDescribe.getTenantId()).thenReturn(null);

            // 配置Mock行为
            mockedOrganizationConfigUtil.when(() -> OrganizationConfigUtil.isImportDimensionGray(null))
                    .thenReturn(false);

            // 执行被测试方法
            ImportType result = dimensionObjectProvider.getImportType(objectDescribe, uniqueRule);

            // 验证结果
            assertEquals(ImportType.UNSUPPORT_INSERT_IMPORT, result);

            // 验证Mock交互
            mockedOrganizationConfigUtil.verify(() -> OrganizationConfigUtil.isImportDimensionGray(null));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，各方法的异常处理
     */
    @Test
    @DisplayName("各方法 - objectDescribe为null时的异常处理")
    void test各方法_objectDescribe为null时的异常处理() {
        // 测试getDuplicateJudgmentType
        int duplicateJudgmentType = dimensionObjectProvider.getDuplicateJudgmentType(null);
        assertEquals(DefaultObjectImportProvider.UNSUPPORT_INSERT_IMPORT, duplicateJudgmentType);

        // 测试getMatchingTypesByInsert
        List<MatchingType> insertMatchingTypes = dimensionObjectProvider.getMatchingTypesByInsert(null);
        assertNotNull(insertMatchingTypes);
        assertEquals(1, insertMatchingTypes.size());
        assertEquals(MatchingType.NAME, insertMatchingTypes.get(0));

        // 测试getMatchingTypesByUpdate
        List<MatchingType> updateMatchingTypes = dimensionObjectProvider.getMatchingTypesByUpdate(null);
        assertNotNull(updateMatchingTypes);
        assertEquals(1, updateMatchingTypes.size());
        assertEquals(MatchingType.NAME, updateMatchingTypes.get(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getMatchingTypesByInsert和getMatchingTypesByUpdate方法返回相同的结果
     */
    @Test
    @DisplayName("匹配类型方法 - Insert和Update返回相同的结果")
    void test匹配类型方法_Insert和Update返回相同的结果() {
        // 执行被测试方法
        List<MatchingType> insertResult = dimensionObjectProvider.getMatchingTypesByInsert(objectDescribe);
        List<MatchingType> updateResult = dimensionObjectProvider.getMatchingTypesByUpdate(objectDescribe);

        // 验证结果
        assertNotNull(insertResult);
        assertNotNull(updateResult);
        assertEquals(insertResult.size(), updateResult.size());
        assertEquals(insertResult.get(0), updateResult.get(0));
        assertEquals(MatchingType.NAME, insertResult.get(0));
        assertEquals(MatchingType.NAME, updateResult.get(0));
    }
}
