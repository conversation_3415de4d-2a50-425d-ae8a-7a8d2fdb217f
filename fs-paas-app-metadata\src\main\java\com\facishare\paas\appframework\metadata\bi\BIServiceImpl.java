package com.facishare.paas.appframework.metadata.bi;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.CrossFilterSupportedObjects;
import com.facishare.paas.appframework.metadata.dto.CrossObjectFilter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * BI服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("biService")
public class BIServiceImpl implements BIService {

    @Autowired
    private BIRestServiceProxy biRestServiceProxy;

    @Autowired
    private BICRMRestServiceProxy biCrmRestServiceProxy;

    @Override
    public CrossObjectFilter.ObjRelationResult getObjRelationResult(User user, CrossObjectFilter.ObjRelationArg arg) {
        if (Objects.isNull(user) || Objects.isNull(arg)) {
            throw new ValidateException(I18nMessage.of(I18NKey.PARAM_ERROR, I18NExt.text(I18NKey.PARAM_ERROR)));
        }

        try {
            log.debug("Getting object relation result for user: {}, with business objects count: {}",
                    user.getUserId(),
                    CollectionUtils.nullToEmpty(arg.getBusinessObjects()).size());

            Map<String, String> headers = RestUtils.buildHeaders(user);
            CrossObjectFilter.ObjRelationResult result = biRestServiceProxy.getObjRelationResult(headers, arg);

            if (Objects.isNull(result)) {
                log.info("Object relation result is null, returning empty result");
                return createEmptyObjRelationResult();
            }

            log.debug("Successfully retrieved object relation result with objectsAndFields size: {}",
                    CollectionUtils.nullToEmpty(result.getObjectsAndFields()).size());
            return result;
        } catch (Exception e) {
            log.error("Exception occurred while getting object relation result: {}", e.getMessage(), e);
            return createEmptyObjRelationResult();
        }
    }

    @Override
    public CrossObjectFilter.QueryReportResult queryReportData(User user, CrossObjectFilter.QueryReportArg arg) {
        if (Objects.isNull(user) || Objects.isNull(arg)) {
           throw new ValidateException(I18nMessage.of(I18NKey.PARAM_ERROR, I18NExt.text(I18NKey.PARAM_ERROR)));
        }
        try {
            log.debug("Querying report data for user: {}", user.getUserId());

            Map<String, String> headers = RestUtils.buildHeaders(user);
            CrossObjectFilter.QueryReportResult result = biRestServiceProxy.queryReportData(headers, arg);

            if (Objects.isNull(result)) {
                log.info("Query report result is null, returning empty result");
                return createEmptyQueryReportResult();
            }

            log.debug("Successfully retrieved query report result with id size: {}",
                    CollectionUtils.nullToEmpty(result.getId()).size());
            return result;
        } catch (Exception e) {
            log.error("Exception occurred while querying report data: {}", e.getMessage(), e);
            return createEmptyQueryReportResult();
        }
    }

    /**
     * 创建空的对象关系结果
     *
     * @return 空的对象关系结果
     */
    private CrossObjectFilter.ObjRelationResult createEmptyObjRelationResult() {
        return CrossObjectFilter.ObjRelationResult.builder()
                .objectsAndFields(Collections.emptyList())
                .build();
    }

    /**
     * 创建空的查询报表结果
     *
     * @return 空的查询报表结果
     */
    private CrossObjectFilter.QueryReportResult createEmptyQueryReportResult() {
        return CrossObjectFilter.QueryReportResult.builder()
                .id(Collections.emptyList())
                .totalNum(0)
                .build();
    }

    @Override
    public CrossFilterSupportedObjects.Result getCrossFilterSupportedObjects(User user, List<String> crmObjNames) {
        if (Objects.isNull(user) || CollectionUtils.empty(crmObjNames)) {
            throw new ValidateException(I18nMessage.of(I18NKey.PARAM_ERROR, I18NExt.text(I18NKey.PARAM_ERROR)));
        }

        try {
            log.debug("Getting cross filter supported objects for user: {}, with object names: {}",
                    user.getUserId(), crmObjNames);

            Map<String, String> headers = RestUtils.buildHeaders(user);
            CrossFilterSupportedObjects.Arg arg = CrossFilterSupportedObjects.Arg.builder()
                    .crmObjNames(crmObjNames)
                    .build();

            CrossFilterSupportedObjects.RestResult result = biCrmRestServiceProxy.getCrossFilterSupportedObjects(headers, arg);

            if (Objects.isNull(result) || !result.isSuccess()) {
                log.info("Cross filter supported objects result is null, returning empty result");
                return createEmptyCrossFilterSupportedObjectsResult();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("Exception occurred while getting cross filter supported objects: {}", e.getMessage(), e);
            return createEmptyCrossFilterSupportedObjectsResult();
        }
    }

    /**
     * 创建空的跨对象筛选支持对象结果
     *
     * @return 空的跨对象筛选支持对象结果
     */
    private CrossFilterSupportedObjects.Result createEmptyCrossFilterSupportedObjectsResult() {
        return CrossFilterSupportedObjects.Result.builder()
                .crmObjNames(Collections.emptyList())
                .build();
    }

    @Override
    public boolean supportCrossObjectFilter(User user, String objectApiName) {
        if (Objects.isNull(user) || Objects.isNull(objectApiName)) {
            log.warn("Invalid parameters for supportCrossObjectFilter: user={}, objectApiName={}",
                    user, objectApiName);
            return false;
        }

        // 重构：使用批量查询方法来实现单个查询，减少代码重复
        log.debug("Checking cross object filter support for single object: {}", objectApiName);

        Map<String, Boolean> batchResult = batchSupportCrossObjectFilter(user, Collections.singletonList(objectApiName));
        return batchResult.getOrDefault(objectApiName, false);
    }

    @Override
    public Map<String, Boolean> batchSupportCrossObjectFilter(User user, List<String> objectApiNames) {
        Map<String, Boolean> resultMap = Maps.newHashMap();
        if (Objects.isNull(user) || CollectionUtils.empty(objectApiNames)) {
            log.warn("Invalid parameters for batchSupportCrossObjectFilter: user={}, objectApiNames={}",
                    user, objectApiNames);
            return resultMap;
        }
        try {
            // 检查灰度开关
            if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_CROSS_OBJECT_GRAY, user.getTenantId())) {
                // 灰度未开启，所有对象都不支持
                objectApiNames.forEach(apiName -> resultMap.put(apiName, false));
                return resultMap;
            }

            // 分离自定义对象和标准对象
            List<String> standardObjects = Lists.newArrayList();
            for (String apiName : objectApiNames) {
                if (ObjectDescribeExt.isCustomObject(apiName)) {
                    // 自定义对象直接支持
                    resultMap.put(apiName, true);
                } else {
                    // 标准对象需要查询
                    standardObjects.add(apiName);
                }
            }

            // 批量查询标准对象的支持情况
            if (!standardObjects.isEmpty()) {
                CrossFilterSupportedObjects.Result result = getCrossFilterSupportedObjects(user, standardObjects);
                List<String> supportedObjects = Objects.nonNull(result) ?
                        CollectionUtils.nullToEmpty(result.getCrmObjNames()) : Collections.emptyList();

                // 设置标准对象的支持状态
                for (String apiName : standardObjects) {
                    resultMap.put(apiName, supportedObjects.contains(apiName));
                }
            }
            return resultMap;
        } catch (Exception e) {
            log.error("Failed to batch check cross object filter support: {}", e.getMessage(), e);
            // 异常情况下，返回所有对象都不支持
            objectApiNames.forEach(apiName -> resultMap.put(apiName, false));
            return resultMap;
        }
    }
}