package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobResultDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import com.facishare.paas.appframework.metadata.publicobject.verify.VerifyResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;
import static org.mockito.Mockito.lenient;

/**
 * PublicObject专用Mock对象工厂 - 提供PublicObject相关业务对象的Mock创建
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 创建PublicObject相关的Mock对象
 * - 提供预配置的Mock对象模板
 * - 支持PublicObject业务场景的Mock数据构造
 * 
 * 使用场景：
 * - PublicObject业务逻辑测试的Mock对象创建
 * - 复杂PublicObject场景的Mock数据准备
 * - PublicObject集成测试的Mock对象生成
 * 
 * 覆盖率目标：为PublicObject包达到80%以上覆盖率提供Mock支持
 */
public class PublicObjectMockFactory {
    
    // 预定义的测试数据
    public static final String DEFAULT_TENANT_ID = "test-tenant-123";
    public static final String DEFAULT_USER_ID = "test-user-456";
    public static final String DEFAULT_UPSTREAM_TENANT_ID = "upstream-tenant-789";
    public static final String DEFAULT_OBJECT_API_NAME = "TestPublicObj";
    public static final String DEFAULT_JOB_ID = "test-job-id-001";
    public static final String DEFAULT_TOKEN = "test-invitation-token";
    
    /**
     * 创建Mock的User对象
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return Mock的User对象
     */
    public static User createMockUser(String tenantId, String userId) {
        // 使用真实的User对象，因为getTenantId()等方法是final的，不能Mock
        return User.systemUser(tenantId);
    }
    
    /**
     * 创建默认的Mock User对象
     * 
     * @return Mock的User对象
     */
    public static User createDefaultMockUser() {
        return createMockUser(DEFAULT_TENANT_ID, DEFAULT_USER_ID);
    }
    
    /**
     * 创建上游租户的Mock User对象
     * 
     * @return Mock的上游User对象
     */
    public static User createUpstreamMockUser() {
        return createMockUser(DEFAULT_UPSTREAM_TENANT_ID, DEFAULT_USER_ID);
    }
    
    /**
     * 创建Mock的IObjectDescribe对象
     * 
     * @param apiName 对象API名称
     * @return Mock的IObjectDescribe对象
     */
    public static IObjectDescribe createMockObjectDescribe(String apiName) {
        IObjectDescribe mockDescribe = Mockito.mock(IObjectDescribe.class);
        lenient().when(mockDescribe.getApiName()).thenReturn(apiName);
        lenient().when(mockDescribe.getDisplayName()).thenReturn("测试公共对象");
        lenient().when(mockDescribe.getTenantId()).thenReturn(DEFAULT_TENANT_ID);
        lenient().when(mockDescribe.isActive()).thenReturn(true);
        lenient().when(mockDescribe.isPublicObject()).thenReturn(false); // 默认不是公共对象
        lenient().when(mockDescribe.copy()).thenReturn(mockDescribe);

        // 创建基本字段列表
        List<IFieldDescribe> fields = createMockFieldDescribes();
        lenient().when(mockDescribe.getFieldDescribes()).thenReturn(fields);

        return mockDescribe;
    }
    
    /**
     * 创建公共对象的Mock IObjectDescribe
     * 
     * @param apiName 对象API名称
     * @return Mock的公共对象IObjectDescribe
     */
    public static IObjectDescribe createMockPublicObjectDescribe(String apiName) {
        IObjectDescribe mockDescribe = createMockObjectDescribe(apiName);
        when(mockDescribe.isPublicObject()).thenReturn(true);
        return mockDescribe;
    }
    
    /**
     * 创建Mock的字段描述列表
     * 
     * @return Mock的字段描述列表
     */
    public static List<IFieldDescribe> createMockFieldDescribes() {
        List<IFieldDescribe> fields = Lists.newArrayList();

        // ID字段
        IFieldDescribe idField = Mockito.mock(IFieldDescribe.class);
        lenient().when(idField.getApiName()).thenReturn("_id");
        lenient().when(idField.getLabel()).thenReturn("ID");
        lenient().when(idField.getType()).thenReturn("text");
        fields.add(idField);

        // 名称字段
        IFieldDescribe nameField = Mockito.mock(IFieldDescribe.class);
        lenient().when(nameField.getApiName()).thenReturn("name");
        lenient().when(nameField.getLabel()).thenReturn("名称");
        lenient().when(nameField.getType()).thenReturn("text");
        lenient().when(nameField.isRequired()).thenReturn(true);
        fields.add(nameField);

        // 测试字段
        IFieldDescribe testField = Mockito.mock(IFieldDescribe.class);
        lenient().when(testField.getApiName()).thenReturn("test_field");
        lenient().when(testField.getLabel()).thenReturn("测试字段");
        lenient().when(testField.getType()).thenReturn("text");
        fields.add(testField);

        return fields;
    }
    
    /**
     * 创建Mock的PublicObjectJobInfo对象
     * 
     * @param objectApiName 对象API名称
     * @param jobType 任务类型
     * @return Mock的PublicObjectJobInfo对象
     */
    public static PublicObjectJobInfo createMockJobInfo(String objectApiName, PublicObjectJobType jobType) {
        PublicObjectJobInfo mockJobInfo = Mockito.mock(PublicObjectJobInfo.class);
        when(mockJobInfo.getObjectApiName()).thenReturn(objectApiName);
        when(mockJobInfo.getJobType()).thenReturn(jobType);
        when(mockJobInfo.getJobStatus()).thenReturn(PublicObjectJobStatus.WAITING);
        when(mockJobInfo.getUpstreamTenantId()).thenReturn(DEFAULT_TENANT_ID);
        return mockJobInfo;
    }
    
    /**
     * 创建默认的Mock PublicObjectJobInfo对象
     * 
     * @return Mock的PublicObjectJobInfo对象
     */
    public static PublicObjectJobInfo createDefaultMockJobInfo() {
        return createMockJobInfo(DEFAULT_OBJECT_API_NAME, PublicObjectJobType.OPEN_JOB);
    }
    
    /**
     * 创建Mock的PublicObjectJobParamVerifyInfo对象
     * 
     * @param jobType 任务类型
     * @param upstreamTenantId 上游租户ID
     * @return Mock的PublicObjectJobParamVerifyInfo对象
     */
    public static PublicObjectJobParamVerifyInfo createMockJobParamVerifyInfo(PublicObjectJobType jobType, String upstreamTenantId) {
        return PublicObjectJobParamVerifyInfo.builder()
                .jobType(jobType)
                .upstreamTenantId(upstreamTenantId)
                .fields(createMockPublicFields())
                .enterprises(createMockEnterpriseHelper())
                .build();
    }
    
    /**
     * 创建Mock的PublicFieldDTO列表
     * 
     * @return Mock的PublicFieldDTO列表
     */
    public static List<PublicFieldDTO> createMockPublicFields() {
        List<PublicFieldDTO> fields = Lists.newArrayList();
        
        PublicFieldDTO field1 = new PublicFieldDTO();
        field1.setFieldApiName("name");
        field1.setPublicFieldType(PublicFieldType.PUBLIC_FIELD_PUBLIC_DATA.getType());
        fields.add(field1);
        
        PublicFieldDTO field2 = new PublicFieldDTO();
        field2.setFieldApiName("test_field");
        field2.setPublicFieldType(PublicFieldType.PUBLIC_FIELD_PRIVATE_DATA.getType());
        fields.add(field2);
        
        return fields;
    }
    
    /**
     * 创建Mock的EnterpriseHelper对象
     * 
     * @return Mock的EnterpriseHelper对象
     */
    public static PublicObjectJobParamVerifyInfo.EnterpriseHelper createMockEnterpriseHelper() {
        return PublicObjectJobParamVerifyInfo.EnterpriseHelper.from(Lists.newArrayList());
    }
    
    /**
     * 创建Mock的PublicObjectJobVerifyResult对象
     * 
     * @param success 是否成功
     * @return Mock的PublicObjectJobVerifyResult对象
     */
    public static PublicObjectJobVerifyResult createMockJobVerifyResult(boolean success) {
        PublicObjectJobVerifyResult mockResult = Mockito.mock(PublicObjectJobVerifyResult.class);
        when(mockResult.success()).thenReturn(success);
        when(mockResult.getMessageList()).thenReturn(success ? Lists.newArrayList() : Lists.newArrayList("验证失败"));
        when(mockResult.getVerifyMessages()).thenReturn(success ? Lists.newArrayList() : Lists.newArrayList(InternationalVerifyMessage.of("VERIFY_FAILED", "验证失败")));
        return mockResult;
    }
    
    /**
     * 创建Mock的VerifyResult对象
     * 
     * @param success 是否成功
     * @return Mock的VerifyResult对象
     */
    public static VerifyResult createMockVerifyResult(boolean success) {
        VerifyResult mockResult = Mockito.mock(VerifyResult.class);
        when(mockResult.success()).thenReturn(success);
        when(mockResult.getMessages()).thenReturn(Lists.newArrayList());
        when(mockResult.getMessageList()).thenReturn(Lists.newArrayList());
        return mockResult;
    }
    
    /**
     * 创建Mock的DesignerResourceResult对象
     * 
     * @return Mock的DesignerResourceResult对象
     */
    public static DesignerResourceResult createMockDesignerResourceResult() {
        DesignerResourceResult mockResult = Mockito.mock(DesignerResourceResult.class);
        when(mockResult.getFields()).thenReturn(Lists.newArrayList());
        when(mockResult.getFieldTypes()).thenReturn(Lists.newArrayList());
        return mockResult;
    }
    
    /**
     * 创建Mock的PublicObjectStatusResult对象
     * 
     * @param status 状态类型
     * @return Mock的PublicObjectStatusResult对象
     */
    public static PublicObjectStatusResult createMockStatusResult(PublicObjectStatusType status) {
        return PublicObjectStatusResult.builder()
                .publicObjectStatus(status)
                .build();
    }
    
    /**
     * 创建Mock的PublicObjectJobResult对象
     * 
     * @return Mock的PublicObjectJobResult对象
     */
    public static PublicObjectJobResult createMockJobResult() {
        PublicObjectJobResult mockResult = Mockito.mock(PublicObjectJobResult.class);
        when(mockResult.getJobType()).thenReturn(PublicObjectJobType.OPEN_JOB);
        when(mockResult.getJobStatus()).thenReturn(PublicObjectJobStatus.WAITING);
        when(mockResult.getJobParam()).thenReturn(null);
        when(mockResult.getJobResult()).thenReturn(null);
        return mockResult;
    }
    
    /**
     * 创建测试用的Map数据
     * 
     * @return 包含测试数据的Map
     */
    public static Map<String, Object> createTestDataMap() {
        Map<String, Object> data = Maps.newHashMap();
        data.put("_id", "test-id-123");
        data.put("name", "测试对象");
        data.put("test_field", "测试值");
        data.put("tenant_id", DEFAULT_TENANT_ID);
        data.put("created_time", System.currentTimeMillis());
        return data;
    }
    
    /**
     * Mock场景组合类
     */
    public static class MockScenario {
        private final User user;
        private final IObjectDescribe objectDescribe;
        private final PublicObjectJobInfo jobInfo;
        private final Map<String, Object> testData;
        
        public MockScenario(User user, IObjectDescribe objectDescribe, PublicObjectJobInfo jobInfo, Map<String, Object> testData) {
            this.user = user;
            this.objectDescribe = objectDescribe;
            this.jobInfo = jobInfo;
            this.testData = testData;
        }
        
        public User getUser() { return user; }
        public IObjectDescribe getObjectDescribe() { return objectDescribe; }
        public PublicObjectJobInfo getJobInfo() { return jobInfo; }
        public Map<String, Object> getTestData() { return testData; }
    }
    
    /**
     * 创建完整的Mock场景
     * 
     * @return MockScenario对象
     */
    public static MockScenario createCompleteScenario() {
        User mockUser = createDefaultMockUser();
        IObjectDescribe mockDescribe = createMockObjectDescribe(DEFAULT_OBJECT_API_NAME);
        PublicObjectJobInfo mockJobInfo = createDefaultMockJobInfo();
        Map<String, Object> testData = createTestDataMap();
        
        return new MockScenario(mockUser, mockDescribe, mockJobInfo, testData);
    }
}
