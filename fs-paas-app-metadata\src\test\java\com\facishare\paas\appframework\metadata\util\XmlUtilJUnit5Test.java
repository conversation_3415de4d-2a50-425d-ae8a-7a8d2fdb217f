package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * JUnit5测试类 for XmlUtil (兼容性类)
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("XmlUtil 单元测试 (兼容性类)")
class XmlUtilJUnit5Test {

    @Mock
    private IObjectDescribe mockObjectDescribe;
    
    @Mock
    private IFieldDescribe mockFieldDescribe;
    
    @Mock
    private IObjectData mockObjectData;

    private List<IFieldDescribe> fieldsToExport;
    private List<IObjectData> dataList;
    private List<IObjectDescribe> describes;
    private Map<String, List<IFieldDescribe>> fieldsToExportMap;
    private Map<String, List<IObjectData>> dataListMap;
    private Map<String, String> pathAndName;

    @BeforeEach
    void setUp() {
        fieldsToExport = Arrays.asList(mockFieldDescribe);
        dataList = Arrays.asList(mockObjectData);
        describes = Arrays.asList(mockObjectDescribe);
        
        fieldsToExportMap = new HashMap<>();
        fieldsToExportMap.put("testObject", fieldsToExport);
        
        dataListMap = new HashMap<>();
        dataListMap.put("testObject", dataList);
        
        pathAndName = new HashMap<>();
        pathAndName.put("path1", "name1");
        pathAndName.put("path2", "name2");
    }

    // ==================== create1LayerXml 方法测试 ====================

    @Test
    @DisplayName("create1LayerXml - 基本参数委托调用")
    void testCreate1LayerXml_BasicParameters() {
        // Arrange
        String expectedResult = "<xml>test result</xml>";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(mockObjectDescribe, fieldsToExport, dataList))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.create1LayerXml(mockObjectDescribe, fieldsToExport, dataList);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(mockObjectDescribe, fieldsToExport, dataList));
        }
    }

    @Test
    @DisplayName("create1LayerXml - 带rename参数委托调用")
    void testCreate1LayerXml_WithRenameParameter() {
        // Arrange
        String expectedResult = "<xml>test result with rename</xml>";
        boolean rename = true;
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(mockObjectDescribe, fieldsToExport, dataList, rename))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.create1LayerXml(mockObjectDescribe, fieldsToExport, dataList, rename);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(mockObjectDescribe, fieldsToExport, dataList, rename));
        }
    }

    @Test
    @DisplayName("create1LayerXml - null参数处理")
    void testCreate1LayerXml_NullParameters() {
        // Arrange
        String expectedResult = "<xml>empty</xml>";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(null, null, null))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.create1LayerXml(null, null, null);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(null, null, null));
        }
    }

    // ==================== createExportFileAndImage 方法测试 ====================

    @Test
    @DisplayName("createExportFileAndImage - 委托调用")
    void testCreateExportFileAndImage() {
        // Arrange
        String fileName = "test.xml";
        String expectedResult = "export_file_path";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFileAndImage(pathAndName, fileName, describes, fieldsToExportMap, dataListMap))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.createExportFileAndImage(pathAndName, fileName, describes, fieldsToExportMap, dataListMap);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFileAndImage(pathAndName, fileName, describes, fieldsToExportMap, dataListMap));
        }
    }

    @Test
    @DisplayName("createExportFileAndImage - 空参数处理")
    void testCreateExportFileAndImage_EmptyParameters() {
        // Arrange
        String fileName = "";
        String expectedResult = "";
        Map<String, String> emptyPathAndName = new HashMap<>();
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFileAndImage(emptyPathAndName, fileName, describes, fieldsToExportMap, dataListMap))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.createExportFileAndImage(emptyPathAndName, fileName, describes, fieldsToExportMap, dataListMap);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFileAndImage(emptyPathAndName, fileName, describes, fieldsToExportMap, dataListMap));
        }
    }

    // ==================== createExportFile 方法测试 ====================

    @Test
    @DisplayName("createExportFile - 委托调用")
    void testCreateExportFile() {
        // Arrange
        String fileName = "export.xml";
        String expectedResult = "export_file_result";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFile(pathAndName, fileName))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.createExportFile(pathAndName, fileName);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFile(pathAndName, fileName));
        }
    }

    @Test
    @DisplayName("createExportFile - null参数处理")
    void testCreateExportFile_NullParameters() {
        // Arrange
        String expectedResult = null;
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFile(null, null))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.createExportFile(null, null);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFile(null, null));
        }
    }

    // ==================== create3LayerXml 方法测试 ====================

    @Test
    @DisplayName("create3LayerXml - 委托调用")
    void testCreate3LayerXml() {
        // Arrange
        String expectedResult = "<xml>3 layer result</xml>";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create3LayerXml(mockObjectDescribe, fieldsToExport, dataList))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.create3LayerXml(mockObjectDescribe, fieldsToExport, dataList);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create3LayerXml(mockObjectDescribe, fieldsToExport, dataList));
        }
    }

    @Test
    @DisplayName("create3LayerXml - 空数据列表处理")
    void testCreate3LayerXml_EmptyDataList() {
        // Arrange
        String expectedResult = "<xml></xml>";
        List<IObjectData> emptyDataList = Arrays.asList();
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create3LayerXml(mockObjectDescribe, fieldsToExport, emptyDataList))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.create3LayerXml(mockObjectDescribe, fieldsToExport, emptyDataList);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create3LayerXml(mockObjectDescribe, fieldsToExport, emptyDataList));
        }
    }

    // ==================== hasFile 方法测试 ====================

    @Test
    @DisplayName("hasFile - 委托调用")
    void testHasFile() throws org.dom4j.DocumentException {
        // Arrange
        String xml = "<xml><file>test.txt</file></xml>";
        boolean expectedResult = true;
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .hasFile(xml))
                .thenReturn(expectedResult);

            // Act
            boolean result = XmlUtil.hasFile(xml);

            // Assert
            assertEquals(expectedResult, result);
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .hasFile(xml));
        }
    }

    @Test
    @DisplayName("hasFile - 异常传播")
    void testHasFile_ExceptionPropagation() throws org.dom4j.DocumentException {
        // Arrange
        String invalidXml = "<invalid xml";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class)) {
            
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .hasFile(invalidXml))
                .thenThrow(new org.dom4j.DocumentException("Invalid XML"));

            // Act & Assert
            assertThrows(org.dom4j.DocumentException.class, () -> {
                XmlUtil.hasFile(invalidXml);
            });
            
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .hasFile(invalidXml));
        }
    }

    // ==================== filterName 方法测试 ====================

    @Test
    @DisplayName("filterName - 委托调用")
    void testFilterName() {
        // Arrange
        String input = "test/file\\name:with*special?chars";
        String expectedResult = "test_file_name_with_special_chars";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.FileExtUtil> mockedFileExtUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.FileExtUtil.class)) {
            
            mockedFileExtUtil.when(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(input))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.filterName(input);

            // Assert
            assertEquals(expectedResult, result);
            mockedFileExtUtil.verify(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(input));
        }
    }

    @Test
    @DisplayName("filterName - null参数处理")
    void testFilterName_NullParameter() {
        // Arrange
        String expectedResult = null;
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.FileExtUtil> mockedFileExtUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.FileExtUtil.class)) {
            
            mockedFileExtUtil.when(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(null))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.filterName(null);

            // Assert
            assertEquals(expectedResult, result);
            mockedFileExtUtil.verify(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(null));
        }
    }

    @Test
    @DisplayName("filterName - 空字符串处理")
    void testFilterName_EmptyString() {
        // Arrange
        String input = "";
        String expectedResult = "";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.FileExtUtil> mockedFileExtUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.FileExtUtil.class)) {
            
            mockedFileExtUtil.when(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(input))
                .thenReturn(expectedResult);

            // Act
            String result = XmlUtil.filterName(input);

            // Assert
            assertEquals(expectedResult, result);
            mockedFileExtUtil.verify(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(input));
        }
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的兼容性类验证")
    void testIntegration_CompleteCompatibilityClassValidation() {
        // Arrange
        String xmlContent = "<xml>test</xml>";
        String fileName = "test.xml";
        String filterInput = "test/file\\name";
        
        try (MockedStatic<com.facishare.paas.appframework.common.util.XmlUtil> mockedXmlUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.XmlUtil.class);
             MockedStatic<com.facishare.paas.appframework.common.util.FileExtUtil> mockedFileExtUtil = 
             mockStatic(com.facishare.paas.appframework.common.util.FileExtUtil.class)) {
            
            // 设置所有方法的Mock行为
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(any(), any(), any()))
                .thenReturn(xmlContent);
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(any(), any(), any(), anyBoolean()))
                .thenReturn(xmlContent);
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFileAndImage(any(), any(), any(), any(), any()))
                .thenReturn("export_path");
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFile(any(), any()))
                .thenReturn("file_path");
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create3LayerXml(any(), any(), any()))
                .thenReturn(xmlContent);
            mockedXmlUtil.when(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .hasFile(any()))
                .thenReturn(true);
            mockedFileExtUtil.when(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(any()))
                .thenReturn("filtered_name");

            // Act & Assert - 验证所有方法都能正确委托调用
            assertDoesNotThrow(() -> {
                String result1 = XmlUtil.create1LayerXml(mockObjectDescribe, fieldsToExport, dataList);
                assertEquals(xmlContent, result1);
                
                String result2 = XmlUtil.create1LayerXml(mockObjectDescribe, fieldsToExport, dataList, true);
                assertEquals(xmlContent, result2);
                
                String result3 = XmlUtil.createExportFileAndImage(pathAndName, fileName, describes, fieldsToExportMap, dataListMap);
                assertEquals("export_path", result3);
                
                String result4 = XmlUtil.createExportFile(pathAndName, fileName);
                assertEquals("file_path", result4);
                
                String result5 = XmlUtil.create3LayerXml(mockObjectDescribe, fieldsToExport, dataList);
                assertEquals(xmlContent, result5);
                
                boolean result6 = XmlUtil.hasFile(xmlContent);
                assertTrue(result6);
                
                String result7 = XmlUtil.filterName(filterInput);
                assertEquals("filtered_name", result7);
            });
            
            // 验证所有委托调用都被执行
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(mockObjectDescribe, fieldsToExport, dataList));
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create1LayerXml(mockObjectDescribe, fieldsToExport, dataList, true));
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFileAndImage(pathAndName, fileName, describes, fieldsToExportMap, dataListMap));
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .createExportFile(pathAndName, fileName));
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .create3LayerXml(mockObjectDescribe, fieldsToExport, dataList));
            mockedXmlUtil.verify(() -> com.facishare.paas.appframework.common.util.XmlUtil
                .hasFile(xmlContent));
            mockedFileExtUtil.verify(() -> com.facishare.paas.appframework.common.util.FileExtUtil
                .filterName(filterInput));
        }
    }
}
