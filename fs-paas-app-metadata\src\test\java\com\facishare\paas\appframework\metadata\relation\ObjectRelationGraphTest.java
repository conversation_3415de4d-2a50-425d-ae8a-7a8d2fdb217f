package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.graph.Network;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

/**
 * GenerateByAI
 * ObjectRelationGraph单元测试类
 */
@ExtendWith(MockitoExtension.class)
class ObjectRelationGraphTest {

    @Mock
    private Network<ObjectRelationGraphBuilder.ObjectNode, ObjectRelationGraphBuilder.ObjectEdge> mockNetwork;

    private ObjectRelationGraph objectRelationGraph;
    private Map<String, IObjectDescribe> describeMap;
    private ObjectDescribe objectDescribe;

    @BeforeEach
    void setUp() {
        objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("TestObject");
        objectDescribe.setTenantId("74255");

        describeMap = Maps.newHashMap();
        describeMap.put("TestObject", objectDescribe);

        objectRelationGraph = ObjectRelationGraph.of(mockNetwork, describeMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectRelationGraph.of静态工厂方法
     */
    @Test
    @DisplayName("静态工厂方法 - 正常创建")
    void testOf_正常创建() {
        // 执行被测试方法
        ObjectRelationGraph result = ObjectRelationGraph.of(mockNetwork, describeMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockNetwork, result.getNetwork());
        assertEquals(describeMap, result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNetwork方法
     */
    @Test
    @DisplayName("获取网络 - 正常场景")
    void testGetNetwork_正常场景() {
        // 执行被测试方法
        Network<ObjectRelationGraphBuilder.ObjectNode, ObjectRelationGraphBuilder.ObjectEdge> result = objectRelationGraph.getNetwork();

        // 验证结果
        assertNotNull(result);
        assertEquals(mockNetwork, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDescribeMap方法
     */
    @Test
    @DisplayName("获取描述映射 - 正常场景")
    void testGetDescribeMap_正常场景() {
        // 执行被测试方法
        Map<String, IObjectDescribe> result = objectRelationGraph.getDescribeMap();

        // 验证结果
        assertNotNull(result);
        assertEquals(describeMap, result);
        assertTrue(result.containsKey("TestObject"));
        assertEquals(objectDescribe, result.get("TestObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNode方法找到节点的场景
     */
    @Test
    @DisplayName("获取节点 - 找到节点")
    void testGetNode_找到节点() {
        // 准备测试数据
        String objectApiName = "TestObject";
        ObjectRelationGraphBuilder.ObjectNode expectedNode = ObjectRelationGraphBuilder.ObjectNode.of(objectApiName);
        when(mockNetwork.nodes()).thenReturn(Sets.newHashSet(expectedNode));

        // 执行被测试方法
        Optional<ObjectRelationGraphBuilder.ObjectNode> result = objectRelationGraph.getNode(objectApiName);

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(expectedNode, result.get());
        assertEquals(objectApiName, result.get().getDescribeApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNode方法未找到节点的场景
     */
    @Test
    @DisplayName("获取节点 - 未找到节点")
    void testGetNode_未找到节点() {
        // 准备测试数据
        String objectApiName = "NonExistentObject";
        ObjectRelationGraphBuilder.ObjectNode otherNode = ObjectRelationGraphBuilder.ObjectNode.of("OtherObject");
        when(mockNetwork.nodes()).thenReturn(Sets.newHashSet(otherNode));

        // 执行被测试方法
        Optional<ObjectRelationGraphBuilder.ObjectNode> result = objectRelationGraph.getNode(objectApiName);

        // 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNode方法空图的场景
     */
    @Test
    @DisplayName("获取节点 - 空图场景")
    void testGetNode_空图场景() {
        // 准备测试数据
        String objectApiName = "TestObject";
        when(mockNetwork.nodes()).thenReturn(Sets.newHashSet());

        // 执行被测试方法
        Optional<ObjectRelationGraphBuilder.ObjectNode> result = objectRelationGraph.getNode(objectApiName);

        // 验证结果
        assertFalse(result.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nodes方法
     */
    @Test
    @DisplayName("获取所有节点 - 正常场景")
    void testNodes_正常场景() {
        // 准备测试数据
        ObjectRelationGraphBuilder.ObjectNode node1 = ObjectRelationGraphBuilder.ObjectNode.of("Object1");
        ObjectRelationGraphBuilder.ObjectNode node2 = ObjectRelationGraphBuilder.ObjectNode.of("Object2");
        Set<ObjectRelationGraphBuilder.ObjectNode> expectedNodes = Sets.newHashSet(node1, node2);
        when(mockNetwork.nodes()).thenReturn(expectedNodes);

        // 执行被测试方法
        Set<ObjectRelationGraphBuilder.ObjectNode> result = objectRelationGraph.nodes();

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedNodes, result);
        assertEquals(2, result.size());
        assertTrue(result.contains(node1));
        assertTrue(result.contains(node2));

        // 验证Mock交互 - 构造函数中调用一次，nodes()方法调用一次，总共2次
        verify(mockNetwork, times(2)).nodes();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试predecessors方法
     */
    @Test
    @DisplayName("获取前驱节点 - 正常场景")
    void testPredecessors_正常场景() {
        // 准备测试数据
        ObjectRelationGraphBuilder.ObjectNode targetNode = ObjectRelationGraphBuilder.ObjectNode.of("TargetObject");
        ObjectRelationGraphBuilder.ObjectNode predecessor1 = ObjectRelationGraphBuilder.ObjectNode.of("Predecessor1");
        ObjectRelationGraphBuilder.ObjectNode predecessor2 = ObjectRelationGraphBuilder.ObjectNode.of("Predecessor2");
        Set<ObjectRelationGraphBuilder.ObjectNode> expectedPredecessors = Sets.newHashSet(predecessor1, predecessor2);
        when(mockNetwork.predecessors(targetNode)).thenReturn(expectedPredecessors);

        // 执行被测试方法
        Set<ObjectRelationGraphBuilder.ObjectNode> result = objectRelationGraph.predecessors(targetNode);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPredecessors, result);
        assertEquals(2, result.size());
        assertTrue(result.contains(predecessor1));
        assertTrue(result.contains(predecessor2));

        // 验证Mock交互
        verify(mockNetwork).predecessors(targetNode);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试successors方法
     */
    @Test
    @DisplayName("获取后继节点 - 正常场景")
    void testSuccessors_正常场景() {
        // 准备测试数据
        ObjectRelationGraphBuilder.ObjectNode sourceNode = ObjectRelationGraphBuilder.ObjectNode.of("SourceObject");
        ObjectRelationGraphBuilder.ObjectNode successor1 = ObjectRelationGraphBuilder.ObjectNode.of("Successor1");
        ObjectRelationGraphBuilder.ObjectNode successor2 = ObjectRelationGraphBuilder.ObjectNode.of("Successor2");
        Set<ObjectRelationGraphBuilder.ObjectNode> expectedSuccessors = Sets.newHashSet(successor1, successor2);
        when(mockNetwork.successors(sourceNode)).thenReturn(expectedSuccessors);

        // 执行被测试方法
        Set<ObjectRelationGraphBuilder.ObjectNode> result = objectRelationGraph.successors(sourceNode);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedSuccessors, result);
        assertEquals(2, result.size());
        assertTrue(result.contains(successor1));
        assertTrue(result.contains(successor2));

        // 验证Mock交互
        verify(mockNetwork).successors(sourceNode);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空描述映射的场景
     */
    @Test
    @DisplayName("空描述映射 - 边界场景")
    void testEmptyDescribeMap_边界场景() {
        // 准备测试数据
        Map<String, IObjectDescribe> emptyDescribeMap = Maps.newHashMap();

        // 执行被测试方法
        ObjectRelationGraph result = ObjectRelationGraph.of(mockNetwork, emptyDescribeMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockNetwork, result.getNetwork());
        assertEquals(emptyDescribeMap, result.getDescribeMap());
        assertTrue(result.getDescribeMap().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null参数的边界场景
     */
    @Test
    @DisplayName("null参数 - 边界场景")
    void testNullParameters_边界场景() {
        // 执行被测试方法并验证抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            ObjectRelationGraph.of(null, null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多个对象描述的复杂场景
     */
    @Test
    @DisplayName("多对象描述 - 复杂场景")
    void testMultipleObjectDescribes_复杂场景() {
        // 准备测试数据
        ObjectDescribe describe1 = new ObjectDescribe();
        describe1.setApiName("Object1");
        describe1.setTenantId("74255");

        ObjectDescribe describe2 = new ObjectDescribe();
        describe2.setApiName("Object2");
        describe2.setTenantId("74255");

        Map<String, IObjectDescribe> multiDescribeMap = Maps.newHashMap();
        multiDescribeMap.put("Object1", describe1);
        multiDescribeMap.put("Object2", describe2);

        // 执行被测试方法
        ObjectRelationGraph result = ObjectRelationGraph.of(mockNetwork, multiDescribeMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getDescribeMap().size());
        assertTrue(result.getDescribeMap().containsKey("Object1"));
        assertTrue(result.getDescribeMap().containsKey("Object2"));
        assertEquals(describe1, result.getDescribeMap().get("Object1"));
        assertEquals(describe2, result.getDescribeMap().get("Object2"));
    }
}
