package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

/**
 * GenerateByAI
 * 测试内容描述：FieldRelationValidator类的综合单元测试
 * 使用JDK 8语法和JUnit 5 + Mockito技术栈
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FieldRelationValidatorTest {

    @Mock
    private IObjectDescribe mockObjectDescribe;

    @Mock
    private FieldRelationGraph mockFieldRelationGraph;

    @Mock
    private IFieldDescribe mockFieldDescribe;

    @Mock
    private Count mockCount;

    @Mock
    private FieldDescribeExt mockFieldDescribeExt;

    @Mock
    private ObjectDescribeExt mockObjectDescribeExt;

    private List<FieldRelationValidator.ValidateField> validateFields;
    private Map<String, IObjectDescribe> describeMap;

    @BeforeEach
    void setUp() {
        // 创建验证字段列表
        validateFields = Lists.newArrayList(
            FieldRelationValidator.ValidateField.of(mockFieldDescribe, false, false)
        );

        // 创建描述映射
        describeMap = Maps.newHashMap();
        describeMap.put("TestObject", mockObjectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldRelationValidator构建器的正常场景
     */
    @Test
    @DisplayName("构建验证器 - 正常场景")
    void testBuilder_正常场景() {
        // 配置必要的mock行为
        when(mockObjectDescribe.getApiName()).thenReturn("TestObject");
        when(mockFieldRelationGraph.getDescribeMap()).thenReturn(describeMap);

        // 执行被测试方法
        FieldRelationValidator validator = FieldRelationValidator.builder()
                .describe(mockObjectDescribe)
                .fields(validateFields)
                .graph(mockFieldRelationGraph)
                .build();

        // 验证结果
        assertNotNull(validator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建器参数为null时的异常处理
     */
    @Test
    @DisplayName("构建验证器 - 参数为null异常")
    void testBuilder_参数为null异常() {
        // 验证describe为null时抛出异常
        assertThrows(NullPointerException.class, () -> {
            FieldRelationValidator.builder()
                    .describe(null)
                    .fields(validateFields)
                    .graph(mockFieldRelationGraph)
                    .build();
        });

        // 验证fields为null时抛出异常
        assertThrows(NullPointerException.class, () -> {
            FieldRelationValidator.builder()
                    .describe(mockObjectDescribe)
                    .fields(null)
                    .graph(mockFieldRelationGraph)
                    .build();
        });

        // 验证graph为null时抛出异常
        assertThrows(NullPointerException.class, () -> {
            FieldRelationValidator.builder()
                    .describe(mockObjectDescribe)
                    .fields(validateFields)
                    .graph(null)
                    .build();
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doValidate方法的正常场景
     */
    @Test
    @DisplayName("执行验证 - 正常场景")
    void testDoValidate_正常场景() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置必要的mock行为
            when(mockObjectDescribe.getApiName()).thenReturn("TestObject");
            when(mockFieldRelationGraph.getDescribeMap()).thenReturn(describeMap);

            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(any(IFieldDescribe.class)))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isCalculateField()).thenReturn(false);
            when(mockFieldDescribeExt.isCountField()).thenReturn(false);

            // 创建验证器
            FieldRelationValidator validator = FieldRelationValidator.builder()
                    .describe(mockObjectDescribe)
                    .fields(validateFields)
                    .graph(mockFieldRelationGraph)
                    .build();

            // 执行被测试方法
            assertDoesNotThrow(() -> validator.doValidate());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试计算字段验证场景
     */
    @Test
    @DisplayName("执行验证 - 计算字段场景")
    void testDoValidate_计算字段场景() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<com.facishare.paas.appframework.metadata.expression.ExpressionFactory> mockedExpressionFactory =
                     mockStatic(com.facishare.paas.appframework.metadata.expression.ExpressionFactory.class)) {

            // 配置必要的mock行为
            when(mockObjectDescribe.getApiName()).thenReturn("TestObject");
            when(mockFieldRelationGraph.getDescribeMap()).thenReturn(describeMap);

            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(any(IFieldDescribe.class)))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isCalculateField()).thenReturn(true);
            when(mockFieldDescribeExt.isCountField()).thenReturn(false);

            // Mock表达式工厂
            mockedExpressionFactory.when(() ->
                com.facishare.paas.appframework.metadata.expression.ExpressionFactory
                    .createExpression(any(IObjectDescribe.class), any(IFieldDescribe.class)))
                    .thenReturn(mock(com.facishare.paas.appframework.metadata.expression.Expression.class));

            // 创建验证器
            FieldRelationValidator validator = FieldRelationValidator.builder()
                    .describe(mockObjectDescribe)
                    .fields(validateFields)
                    .graph(mockFieldRelationGraph)
                    .build();

            // 执行被测试方法
            assertDoesNotThrow(() -> validator.doValidate());

            // 验证表达式工厂被调用
            mockedExpressionFactory.verify(() ->
                com.facishare.paas.appframework.metadata.expression.ExpressionFactory
                    .createExpression(eq(mockObjectDescribe), eq(mockFieldDescribe)));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ValidateField静态工厂方法
     */
    @Test
    @DisplayName("ValidateField创建 - 静态工厂方法")
    void testValidateField_静态工厂方法() {
        // 执行被测试方法
        FieldRelationValidator.ValidateField validateField = 
                FieldRelationValidator.ValidateField.of(mockFieldDescribe, true, false);

        // 验证结果
        assertNotNull(validateField);
        assertEquals(mockFieldDescribe, validateField.getFieldDescribe());
        assertTrue(validateField.isReturnTypeChanged());
        assertFalse(validateField.isTypeChanged());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ValidateField的委托方法
     */
    @Test
    @DisplayName("ValidateField委托方法 - 正常场景")
    void testValidateField_委托方法() {
        // 配置Mock行为
        when(mockFieldDescribe.getApiName()).thenReturn("delegateField");
        when(mockFieldDescribe.getType()).thenReturn(IFieldType.NUMBER);

        // 创建ValidateField
        FieldRelationValidator.ValidateField validateField =
                FieldRelationValidator.ValidateField.of(mockFieldDescribe, false, true);

        // 验证委托方法
        assertEquals("delegateField", validateField.getApiName());
        assertEquals(IFieldType.NUMBER, validateField.getType());
        assertFalse(validateField.isReturnTypeChanged());
        assertTrue(validateField.isTypeChanged());
    }
}
