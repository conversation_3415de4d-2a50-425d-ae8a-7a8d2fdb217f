package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.GetRolesByUserId;
import com.facishare.paas.appframework.privilege.util.AuthContextExt;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.checker.CheckerResult;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.service.IRecordTypeService;
import com.facishare.paas.metadata.api.service.IRelationMatchService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeOption;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.enterpriserelation2.arg.ListAppOuterRolesByAppIdArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.data.RoleInfoData;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.fxiaoke.paas.auth.factory.ViewClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isEnableCheckEnterpriseResourcesQuote;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;
import static com.facishare.paas.appframework.privilege.util.PrivilegeConstants.ADMIN_ROLE_CODE;


/**
 * Created by linqiuying on 17/10/12.
 */
@Slf4j
@Service("recordTypeLogicService")
public class RecordTypeLogicServiceImpl implements RecordTypeLogicService {
    public static final List<ObjectAction> RECORD_TYPE_FUNCTION_CODES = Lists.newArrayList(ObjectAction.CREATE,
            ObjectAction.BATCH_IMPORT, ObjectAction.UPDATE, ObjectAction.TRANSFER_ADD);
    @Autowired
    private IRecordTypeService recordTypeService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthProxy;

    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    @Qualifier("layoutLogicService")
    private LayoutLogicService layoutService;
    @Autowired
    private LogService logService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private IRelationMatchService relationMatchServiceImpl;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Autowired
    private ViewClient viewClient;
    @Autowired
    private ManageGroupService manageGroupService;
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;
    @Autowired
    private ApplicationLayeredGrayService applicationLayeredGrayService;
    @Autowired
    private AppOuterRoleService appOuterRoleService;

    /**
     * 查询角色信息
     */
    public RecordTypeResult findRoleInfoList(User user) {
        log.debug("Entering RecordTypeService findRoleInfoList(tenantId = {}, objectDescribeApiName = {})");
        List<RoleInfoPojo> roleInfoPojoList = getRoleInfoPojoList(user);

        RecordTypeResult result = new RecordTypeResult();
        if (CollectionUtils.empty(roleInfoPojoList)) {
            result.setRole_list(null);
        } else {
            List<JSONObject> roleList = Lists.newArrayList();
            for (RoleInfoPojo roleInfoPojo : roleInfoPojoList) {
                roleList.add(roleInfoPojo.toJSONObject());
            }
            result.setRole_list(roleList);
        }
        return result;
    }

    private List<RoleInfoPojo> getRoleInfoPojoList(User user) {
        RoleInfoModel.Arg arg = new RoleInfoModel.Arg();
        arg.setAuthContext(user);
        RoleInfoModel.Result roleInfoResult = recordTypeAuthProxy.roleInfo(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        return roleInfoResult.getResult().getRoles();
    }

    @Override
    public List<RoleInfoPojo> findRoleInfoWithMangeGroup(User user, String sourceInfo, boolean excludeOuterRole) {
        List<RoleInfoPojo> roleInfoPojoList = getRoleInfoPojoList(user);
        if (CollectionUtils.empty(roleInfoPojoList)) {
            return Lists.newArrayList();
        }
        ManageGroup manageGroup = queryRoleManageGroup(user, sourceInfo);
        List<RoleInfoPojo> result = Lists.newArrayList();
        for (RoleInfoPojo roleInfoPojo : roleInfoPojoList) {
            if (excludeOuterRole && RoleInfoPojo.isOuterRole(roleInfoPojo)) {
                continue;
            }
            if (ManageGroup.support(manageGroup, roleInfoPojo.getRoleCode())) {
                result.add(roleInfoPojo);
            }
        }
        return result;
    }

    @Override
    public List<RoleInfoPojo> findRoleInfoByAppIdWithMangeGroup(User user, String sourceInfo, String describeApiName, String appId) {
        if (!applicationLayeredGrayService.supportAppLayered(user, appId, describeApiName)) {
            return findRoleInfoWithMangeGroup(user, sourceInfo, true);
        }
        return findListAppOuterRolesByAppId(user, appId);

    }

    public List<RoleInfoPojo> findListAppOuterRolesByAppId(User user, String appId) {
        Integer tenantIdInt = user.getTenantIdInt();
        HeaderObj header = HeaderObj.newInstance(tenantIdInt);
        ListAppOuterRolesByAppIdArg arg = new ListAppOuterRolesByAppIdArg();
        arg.setLinkAppId(appId);
        arg.setTenantId(tenantIdInt);
        RestResult<List<RoleInfoData>> listRestResult = appOuterRoleService.listAppOuterRolesByAppId(header, arg);
        if (!listRestResult.isSuccess()) {
            log.warn("listAppOuterRolesByAppId fail! ei:{}, appId:{}, msg:{}", user.getTenantId(), appId, listRestResult.getErrMsg());
            return Lists.newArrayList();
        }
        return CollectionUtils.nullToEmpty(listRestResult.getData()).stream()
                .map(it -> {
                    RoleInfoPojo roleInfoPojo = new RoleInfoPojo();
                    roleInfoPojo.setRoleCode(it.getRoleCode());
                    roleInfoPojo.setRoleName(it.getRoleName());
                    return roleInfoPojo;
                })
                .collect(Collectors.toList());
    }

    public RecordTypeResult findRecordInfo(User user, String describeApiName, String recordApiName, boolean defaultLang) {
        log.debug("Entering RecordTypeService findRecordInfo(user = {}, objectDescribeApiName = {}, " +
                "recordApiName={})", user, describeApiName, recordApiName);
        RecordTypeResult result = new RecordTypeResult();
        IRecordTypeOption recordTypeOption;
        try {
            String tenantId = user.getTenantId();
            ActionContextExt actionContextExt = ActionContextExt.of(user);
            if (defaultLang) {
                actionContextExt.setDefaultValueFlag();
            }
            recordTypeOption = recordTypeService.findByApiName(tenantId, describeApiName,
                    recordApiName, actionContextExt.getContext());
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        result.setRecordTypeOption(recordTypeOption == null ? null : ((RecordTypeOption) recordTypeOption)
                .getContainerDocument());
        return result;
    }

    /**
     * 查询业务类型详细信息
     */
    public RecordTypeResult findRecordInfo(User user, String describeApiName, String recordApiName) {
        return findRecordInfo(user, describeApiName, recordApiName, false);
    }

    /**
     * 获取角色列表和layout列表
     */
    public RecordTypeResult findRoleAndLayout(String tenantId, String describeApiName, User user) {
        log.debug("Entering RecordTypeService findRoleAndLayout(tenantId = {}, objectDescribeApiName = {})",
                tenantId, describeApiName);
        RecordTypeResult result = new RecordTypeResult();
        List<RoleInfoPojo> roleList = findRoleInfoByFunctionCode(RECORD_TYPE_FUNCTION_CODES, describeApiName, user);
        result.setRoleInfoPojoList(roleList);
        List<ILayout> detailLayouts = getLayoutByType(tenantId, describeApiName, LayoutTypes.DETAIL);
        List<ILayout> editLayouts = getLayoutByType(tenantId, describeApiName, LayoutTypes.EDIT);
        List<Map<String, String>> layoutApiAndLabelList = Stream.of(detailLayouts, editLayouts)
                .flatMap(Collection::stream)
                .map(layout -> {
                    Map<String, String> map = Maps.newHashMap();
                    map.put("api_name", layout.getName());
                    map.put("label", layout.getDisplayName());
                    map.put("layout_type", layout.getLayoutType());
                    return map;
                }).collect(Collectors.toList());
        result.setLayout_list(layoutApiAndLabelList);
        return result;
    }

    private List<RoleInfoPojo> findRoleInfoByFunctionCode(List<ObjectAction> actionCodes, String describeApiName, User user) {
        Set<String> roleSet = findRoleCodesByFunction(actionCodes, describeApiName, user);
        return queryRoleInfoWithCodes(roleSet, user);
    }

    public Set<String> findRoleCodesWithInitRecordType(String describeApiName, User user) {
        Set<String> roleCodes = findRoleCodesByFunction(RECORD_TYPE_FUNCTION_CODES, describeApiName, user);
        if (CollectionUtils.empty(roleCodes)) {
            return Sets.newHashSet(ADMIN_ROLE_CODE);
        }
        // 追加 CRM管理员 角色
        roleCodes.add(ADMIN_ROLE_CODE);
        return roleCodes;
    }

    @Override
    public void sortRecordTypeOption(User user, String describeApiName, List<String> orderedList) {
        if (CollectionUtils.empty(orderedList)) {
            return;
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        Optional<RecordTypeFieldDescribe> recordTypeField = ObjectDescribeExt.of(describe).getRecordTypeField();
        if (!recordTypeField.isPresent()) {
            return;
        }
        RecordTypeFieldDescribe recordTypeFieldDescribe = recordTypeField.get();
        List<IRecordTypeOption> recordTypeOptions = CollectionUtils.sortByGivenOrder(
                recordTypeFieldDescribe.getRecordTypeOptions(), orderedList, IRecordTypeOption::getApiName);
        recordTypeFieldDescribe.setRecordTypeOptions(recordTypeOptions);
        describeLogicService.updateFieldDescribe(user, describe, Lists.newArrayList(recordTypeFieldDescribe));
    }

    private Map<String, Set<String>> findRoleCodesByFunction(List<ObjectAction> actionCodes, List<IObjectDescribe> describeList, User user) {
        Map<String, Set<String>> resultMap = Maps.newHashMap();
        describeList.forEach(describe -> {
            Set<String> function = findRoleCodesByFunction(actionCodes, describe.getApiName(), user);
            resultMap.put(describe.getApiName(), function);
        });
        return resultMap;
    }

    private Set<String> findRoleCodesByFunction(List<ObjectAction> actionCodes, String describeApiName, User user) {
        String apiName = getFunctionDescribeApiName(describeApiName);
        List<String> functionCodes = actionCodes.stream()
                .map(x -> x.getPrivilegeCode(ObjectAPINameMapping.isSFAObjectNew(apiName)))
                .collect(Collectors.toList());
        Map<String, List<String>> roleMap = functionPrivilegeService.getHavePrivilegeRolesByActionCodes(user, apiName, functionCodes);
        return roleMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
    }

    private String getFunctionDescribeApiName(String describeApiName) {
        // 订单产品走订单的功能权限
        if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(describeApiName)) {
            return Utils.SALES_ORDER_API_NAME;
        }
        // 退货单产品走退货单的功能权限
        if (Utils.RETURN_GOODS_INVOICE_Product_API_NAME.equals(describeApiName)) {
            return Utils.RETURN_GOODS_INVOICE_API_NAME;
        }
        return describeApiName;
    }

    private List<RoleInfoPojo> queryRoleInfoWithCodes(Collection<String> roles, User user) {
        QueryRoleInfoWithCodes.Arg arg = new QueryRoleInfoWithCodes.Arg();
        arg.setAuthContext(user);
        arg.setRoleCodes(Lists.newArrayList(roles));
        QueryRoleInfoWithCodes.Result roleInfoResult = recordTypeAuthProxy.queryRoleInfoWithCodes(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        return filterByRoleType(roleInfoResult, user);
    }

    private List<RoleInfoPojo> filterByRoleType(QueryRoleInfoWithCodes.Result roleInfoResult, User user) {
        return CollectionUtils.nullToEmpty(roleInfoResult.getResult().getRoles()).stream()
                .filter(roleInfoPojo -> user.isOutUser() ? RoleInfoPojo.isOuterRole(roleInfoPojo) : RoleInfoPojo.isCrmRole(roleInfoPojo))
                .collect(Collectors.toList());
    }

    private List<ILayout> getLayoutByType(String tenantId, String describeApiName, String layoutType) {
        return layoutService.findLayoutByObjectApiNameAndLayoutType(tenantId, describeApiName, layoutType);
    }

    private List<ILayout> getLayoutByType(String tenantId, String describeApiName, String whatApiName, String layoutType, String appId) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(User.systemUser(tenantId), appId);
        List<ILayout> layouts;
        if (FlowTaskLayoutExt.isFlowTaskLayout(layoutType)) {
            layouts = layoutService.findFlowTaskListLayoutsByType(layoutContext, describeApiName, whatApiName, layoutType);
        } else {
            layouts = layoutService.findLayoutByObjectApiNameAndLayoutType(layoutContext, describeApiName, layoutType);
        }
        List<ILayout> first = layouts.stream().filter(x -> BooleanUtils.isTrue(x.isDefault())).collect(Collectors.toList());
        List<ILayout> last = layouts.stream().filter(x -> BooleanUtils.isNotTrue(x.isDefault())).collect(Collectors.toList());
        List<ILayout> result = Lists.newArrayList();
        result.addAll(first);
        result.addAll(last);
        return result;
    }

    /**
     * 新建业务类型，并分配角色和layout
     */
    public RecordTypeResult createRecordType(String tenantId, String describeApiName, RecordTypeRoleViewPojo pojo,
                                             User user) {
        log.debug("Entering RecordTypeService createRecordType(tenantId = {}, objectDescribeApiName = {}, " +
                "recordTypeJson={})", tenantId, describeApiName, JSON.toJSONString(pojo));
        RecordTypeResult result = new RecordTypeResult();
        if (null == pojo) {
            result.setSuccess(false);
            return result;
        }

        String label = pojo.getLabel();
        String apiName = pojo.getApi_name();
        String description = pojo.getDescription();
        boolean isActive = pojo.getIs_active();
        IObjectDescribe objectDescribe = new ObjectDescribe();
        Boolean success = false;
        try {
            objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
            checkRecordTypeCountLimit(user, objectDescribe);

            success = createRecordType(tenantId, describeApiName, label, apiName, description, isActive, pojo.getConfig());
        } catch (MetadataServiceException e) {
            log.warn("Error in createRecordType,tenantId:{},describeApiName:{},pojo:{}", tenantId, describeApiName, pojo, e);
            throw new MetaDataBusinessException(e);
        }

        if (!success) {
            result.setSuccess(false);
            return result;
        }

        //发送同步业务类型的mq消息
        //sendRecordTypeSyncMessage(RecordTypeProducer.FLAG_ADD, tenantId, describeApiName, label, apiName);

        logService.log(user, EventType.ADD, ActionType.CREATE_RECORD_TYPE, describeApiName, String.format(I18N.text(I18NKey.OBJECT) + "%s", objectDescribe.getDisplayName()));
        List<RoleViewForWebPojo> roleViewList = pojo.getRoles();
        if (CollectionUtils.empty(roleViewList)) {
            result.setSuccess(success);
            return result;
        }

        AddRoleRecordTypeModel.Result addRoleRecordTypeResult = createRoleAndRecordType(roleViewList,
                describeApiName, apiName, tenantId, user);
        AddRoleViewModel.Result addRoleViewResult = createRoleAndRecordTypeAndLayout(roleViewList, describeApiName,
                apiName, tenantId, user);
        result.setSuccess(success && addRoleRecordTypeResult.isSuccess() && addRoleViewResult.isSuccess());
        return result;
    }

//    private void sendRecordTypeSyncMessage(String flag, String tenantId, String describeApiName, String label, String apiName) {
//        if (!OLD_OBJ_API_NAMES.contains(describeApiName)) {
//            //老对象才发q
//            return;
//        }
//
//        try {
//            RecordTypeProducer.RecordTypeMessage message = new RecordTypeProducer.RecordTypeMessage();
//            message.setFlag(flag);
//            message.setObjApiName(describeApiName);
//            message.setRecordApiName(apiName);
//            message.setRecordLabel(label);
//            message.setTenantId(tenantId);
//            asyncProducer.sendMessage(message);
//        } catch (Exception e) {
//            log.error("Error in sending Sync Record TYpe message, flat:{}, tenantId:{}, describeApiName:{}, recordTypeApiName:{}, label:{}",
//                    flag, tenantId, describeApiName, apiName, label, e);
//        }
//    }

    private Boolean createRecordType(String tenantId, String describeApiName, String label, String apiName, String
            description, boolean isActive, Map config) throws MetadataServiceException {
        IRecordTypeOption option = new RecordTypeOption(label, apiName, description, isActive);
        if (Objects.nonNull(config)) {
            option.set("config", config);
        }
        return recordTypeService.createRecordType(tenantId, describeApiName, option);
    }

    private AddRoleRecordTypeModel.Result createRoleAndRecordType(List<RoleViewForWebPojo> roleViewList, String
            describeApiName, String apiName, String tenantId, User user) {
        Set<RecordTypePojo> recordTypePojoList = Sets.newHashSet();
        RecordTypePojo recordTypePojo;
        for (RoleViewForWebPojo roleView : roleViewList) {
            if (null == roleView || !roleView.getIs_used()) {
                continue;
            }
            String roleCode = roleView.getRoleCode();
            recordTypePojo = getRecordTypePojo(describeApiName, apiName, tenantId, roleCode, roleView.getIs_default());
            recordTypePojoList.add(recordTypePojo);
        }

        AddRoleRecordTypeModel.Arg arg = new AddRoleRecordTypeModel.Arg();
        arg.setRecordTypePojos(Lists.newArrayList(recordTypePojoList));
        arg.setAuthContext(user);
        arg.setEntityId(describeApiName);
        arg.setRecordTypeId(apiName);
        AddRoleRecordTypeModel.Result result = recordTypeAuthProxy.addRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));

        return result;
    }

    private RecordTypePojo getRecordTypePojo(String describeApiName, String apiName, String tenantId, String
            roleCode, boolean isDefault) {
        RecordTypePojo recordTypePojo = new RecordTypePojo();
        recordTypePojo.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        recordTypePojo.setEntityId(describeApiName);
        recordTypePojo.setRecordTypeId(apiName);
        recordTypePojo.setRoleCode(roleCode);
        recordTypePojo.setTenantId(tenantId);
        recordTypePojo.setDefaultType(isDefault);
        return recordTypePojo;
    }

    private AddRoleViewModel.Result createRoleAndRecordTypeAndLayout(List<RoleViewForWebPojo> roleViewList, String
            describeApiName, String apiName, String tenantId, User user) {
        List<RoleViewPojo> roleViewPojoList = Lists.newArrayList();
        List<String> layoutApiNames = roleViewList.stream().map(x -> x.getLayout_api_name()).distinct().collect(Collectors.toList());
        Map<String, Layout> layoutMap = layoutService.findLayoutByApiNames(tenantId, layoutApiNames, describeApiName);
        for (RoleViewForWebPojo roleView : roleViewList) {
            String roleCode = roleView.getRoleCode();
            RoleViewPojo roleViewPojo = new RoleViewPojo();
            roleViewPojo.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
            roleViewPojo.setEntityId(describeApiName);
            roleViewPojo.setRecordTypeId(apiName);
            roleViewPojo.setRoleCode(roleCode);
            roleViewPojo.setTenantId(tenantId);
            roleViewPojo.setViewId(roleView.getLayout_api_name());
            if (layoutMap.containsKey(roleView.getLayout_api_name())) {
                roleViewPojo.setViewType(layoutMap.get(roleView.getLayout_api_name()).getLayoutType());
            } else {
                roleViewPojo.setViewType(LayoutTypes.DETAIL);
            }
            roleViewPojoList.add(roleViewPojo);
        }
        upsertRoleViewList(user, roleViewPojoList);

        return new AddRoleViewModel.Result();
    }

    /**
     * 新建对象时候初始化recordType
     */
    @Override
    public void recordTypeInit(User user, String layoutApiName, String tenantId, String describeApiName) {
        // 强制返回 CRM管理员 角色
        Set<String> roleCodes = findRoleCodesWithInitRecordType(describeApiName, user);
        // 新建对象时，只初始化有 「新建」、「导入」功能权限角色的默认业务类型
        List<RoleViewForWebPojo> viewForWebPojos = roleCodes.stream().map(roleCode -> RoleViewForWebPojo.builder()
                .is_default(true)
                .is_used(true)
                .layout_api_name(layoutApiName)
                .roleCode(roleCode)
                .build()).collect(Collectors.toList());

        try {
            if (!Strings.isNullOrEmpty(layoutApiName)) {
                createRoleAndRecordTypeAndLayout(viewForWebPojos, describeApiName, "default__c", tenantId, user);
            }
            createRoleAndRecordType(viewForWebPojos, describeApiName, "default__c", tenantId, user);
        } catch (Exception e) {
            log.error("Error in recordTypeInit, tenantId:{}, DescribeApiName:{}, layoutApiName:{}", tenantId, describeApiName, layoutApiName, e);
        }
    }

    public RecordTypeResult findRoleAndRecordType(String tenantId, String describeApiName, User user) {
        return findRoleAndRecordType(tenantId, describeApiName, user, null);
    }

    /**
     * 查询角色和类型关系
     */
    public RecordTypeResult findRoleAndRecordType(String tenantId, String describeApiName, User user, String sourceInfo) {
        log.debug("Entering RecordTypeService findRoleAndRecordType(tenantId = {}, objectDescribeApiName = {})",
                tenantId, describeApiName);
        RecordTypeResult result = new RecordTypeResult();
        List<RoleInfoPojo> roleInfoPojoList = findRoleInfoWithMangeGroup(user, null, true);
        if (CollectionUtils.empty(roleInfoPojoList)) {
            return result;
        }
        Map<String, RoleInfoPojo> roleMap = roleInfoPojoList.stream().collect(Collectors.toMap(RoleInfoPojo::getRoleCode, x -> x));

        //查找recordType
        List<IRecordTypeOption> recordTypeList;
        try {
            recordTypeList = recordTypeService.findByObjectDescribeApiName(tenantId, describeApiName, getActionContext(user.getTenantId(), user.getUserId()));
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        if (!CollectionUtils.empty(recordTypeList)) {
            List list = recordTypeList.stream()
                    .filter(it -> it.isActive())
                    .map(item -> ((DocumentBasedBean) item).getContainerDocument())
                    .collect(Collectors.toList());
            result.setRecord_list(list);
        }


        List<String> roleCods = roleInfoPojoList.stream().map(RoleInfoPojo::getRoleCode).collect(Collectors.toList());
        BatchFindRecordType.Arg arg = BatchFindRecordType.Arg.builder().roleCodes(roleCods).entityId(describeApiName).build();
        arg.setAuthContext(user);
        FindRecordTypeModel.Result batchFindRecordType = recordTypeAuthProxy.batchFindRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        Map<String, List<RecordTypePojo>> map = batchFindRecordType.getResult().stream().collect(Collectors.groupingBy(RecordTypePojo::getRoleCode));

        List<RecordTypeResult.RoleInfo> roleList = Lists.newArrayList();
        map.forEach((roleCode, recordTypes) -> {
            List<String> recordList = recordTypes.stream().map(RecordTypePojo::getRecordTypeId).collect(Collectors.toList());
            String defaultRecordType = recordTypes.stream()
                    .filter(x -> BooleanUtils.isTrue(x.getDefaultType()))
                    .map(RecordTypePojo::getRecordTypeId)
                    .findFirst().orElse("");
            RecordTypeResult.RoleInfo roleInfo = RecordTypeResult.RoleInfo.builder()
                    .roleName(roleMap.get(roleCode).getRoleName())
                    .roleCode(roleCode)
                    .records(recordList)
                    .defaultRecord(defaultRecordType)
                    .build();
            roleList.add(roleInfo);
        });
        List<RoleInfoPojo> roleInfoList = findRoleInfoByFunctionCode(RECORD_TYPE_FUNCTION_CODES, describeApiName, user);
        List<String> source = roleInfoPojoList.stream().map(RoleInfoPojo::getRoleCode).collect(Collectors.toList());

        ManageGroup roleManageGroup = queryRoleManageGroup(user, sourceInfo);
        result.setRoleInfoList(fillRoleList(roleInfoList, roleList, source, roleManageGroup));
        return result;

    }

    private ManageGroup queryRoleManageGroup(User user, String sourceInfo) {
        if (!ObjectListConfig.OBJECT_MANAGEMENT.equals(sourceInfo)) {
            return null;
        }
        return manageGroupService.queryManageGroup(user, null, ManageGroupType.BUSINESS_ROLE);
    }

    private List<RecordTypeResult.RoleInfo> fillRoleList(List<RoleInfoPojo> roleInfoList, List<RecordTypeResult.RoleInfo> roleList,
                                                         List<String> source, ManageGroup roleManageGroup) {
        Set<String> roleCodes = roleList.stream()
                .map(RecordTypeResult.RoleInfo::getRoleCode)
                .collect(Collectors.toSet());
        roleInfoList.stream().filter(it -> !roleCodes.contains(it.getRoleCode()))
                .forEach(it -> {
                    RecordTypeResult.RoleInfo roleInfo = RecordTypeResult.RoleInfo.builder()
                            .roleName(it.getRoleName())
                            .roleCode(it.getRoleCode())
                            .records(Collections.emptyList())
                            .defaultRecord("")
                            .build();
                    roleList.add(roleInfo);
                });
        if (Objects.nonNull(roleManageGroup)) {
            roleList.removeIf(it -> !ManageGroup.support(roleManageGroup, it.getRoleCode()));
        }
        return CollectionUtils.sortByGivenOrder(Lists.newArrayList(roleList), source, RecordTypeResult.RoleInfo::getRoleCode);
    }

    public RecordTypeResult assignRecord(String tenantId, String describeApiName, String json, User user) {
        return assignRecord(tenantId, describeApiName, json, user, null);
    }

    /**
     * 分配业务类型－设置不同的角色可以使用的业务类型
     */
    public RecordTypeResult assignRecord(String tenantId, String describeApiName, String json, User user, String sourceInfo) {
        log.debug("Entering RecordTypeService assignRecord(tenantId = {}, objectDescribeApiName = {}, " +
                "roleListJson={})", tenantId, describeApiName, json);
        RecordTypeResult result = new RecordTypeResult();
        List<RecordTypeResult.RoleInfo> roleList = JSONArray.parseArray(json, RecordTypeResult.RoleInfo.class);
        if (CollectionUtils.empty(roleList)) {
            log.warn("role list is empty, json={}", json);
            return result;
        }

        // 校验具有当前对象的新建或导入权限的角色，必须至少分配一个业务类型
        validateRecordTypeByFunctionPrivilege(roleList, describeApiName, user, sourceInfo);

        List<RecordTypePojo> recordTypePojoList = Lists.newArrayList();
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
        Optional<RecordTypeFieldDescribe> recordTypeField = ObjectDescribeExt.of(describe).getRecordTypeField();
        if (!recordTypeField.isPresent()) {
            throw new ValidateException(I18nMessage.of(I18NKey.RECORD_TYPE_NOT_EXIST, I18N.text(I18NKey.RECORD_TYPE_NOT_EXIST)));
        }

        List<String> toDeleteRoleList = Lists.newArrayList();
        roleList.forEach(roleInfo -> {
            List<String> records = roleInfo.getRecords();
            if (CollectionUtils.empty(records)) {
                toDeleteRoleList.add(roleInfo.getRoleCode());
                return;
            }
            validateRecordTypeEnable(records, recordTypeField.get());
            String roleCode = roleInfo.getRoleCode();
            String defaultRecord = roleInfo.getDefaultRecord();
            for (String apiName : records) {
                RecordTypePojo recordTypePojo = getRecordTypePojo(describeApiName, apiName, tenantId, roleCode, Objects.equals(defaultRecord, apiName));
                recordTypePojoList.add(recordTypePojo);
            }
        });
        result.setSuccess(updateAndDelete(describeApiName, user, recordTypePojoList, toDeleteRoleList));
        if (Utils.SPU_API_NAME.equals(describeApiName) && result.isSuccess()) {
            syncRecordTypeRole2Product(user, recordTypePojoList, toDeleteRoleList);
        }
        return result;
    }

    private void syncRecordTypeRole2Product(User user, List<RecordTypePojo> toUpdateRoleList, List<String> toDeleteRoleList) {
        toUpdateRoleList.forEach(r -> r.setEntityId(Utils.PRODUCT_API_NAME));
        updateAndDelete(Utils.PRODUCT_API_NAME, user, toUpdateRoleList, toDeleteRoleList);
    }

    private boolean updateAndDelete(String describeApiName, User user, List<RecordTypePojo> toUpdateRoleList, List<String> toDeleteRoleList) {
        boolean isSuccess = true;
        if (CollectionUtils.notEmpty(toUpdateRoleList)) {
            UpdateRoleRecordTypeModel.Arg arg = new UpdateRoleRecordTypeModel.Arg();
            arg.setRecordTypePojos(toUpdateRoleList);
            arg.setEntityId(describeApiName);
            arg.setAuthContext(user);
            UpdateRoleRecordTypeModel.Result updateRoleRecordTypeResult =
                    recordTypeAuthProxy.updateRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
            if (!updateRoleRecordTypeResult.isSuccess()) {
                log.warn("batchDeleteRoleEntityRelation fail, ei:{}, describeApiName:{}, arg:{}, result:{}",
                        user.getTenantId(), describeApiName, JSON.toJSONString(arg), updateRoleRecordTypeResult);
            }
            isSuccess = updateRoleRecordTypeResult.isSuccess();
        }

        if (CollectionUtils.notEmpty(toDeleteRoleList)) {
            BatchDeleteRoleEntityRelation.Arg deleteArg = new BatchDeleteRoleEntityRelation.Arg();
            deleteArg.setEntityId(describeApiName);
            deleteArg.setRoleCodes(toDeleteRoleList);
            deleteArg.setAuthContext(user);
            BatchDeleteRoleEntityRelation.Result deleteResult =
                    recordTypeAuthProxy.batchDeleteRoleEntityRelation(deleteArg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
            if (!deleteResult.isSuccess()) {
                log.warn("batchDeleteRoleEntityRelation fail, ei:{}, describeApiName:{}, arg:{}, result:{}",
                        user.getTenantId(), describeApiName, JSON.toJSONString(deleteArg), deleteResult);
            }
            isSuccess = isSuccess && deleteResult.isSuccess();
        }
        return isSuccess;
    }

    private void validateRecordTypeByFunctionPrivilege(List<RecordTypeResult.RoleInfo> roleList, String describeApiName, User user, String sourceInfo) {
        Map<String, RecordTypeResult.RoleInfo> roleMap = roleList.stream().collect(Collectors.toMap(RecordTypeResult.RoleInfo::getRoleCode, x -> x));
        List<RoleInfoPojo> roleInfoList = findRoleInfoByFunctionCode(RECORD_TYPE_FUNCTION_CODES, describeApiName, user);
        // 只有分管的角色才参与校验
        ManageGroup manageGroup = queryRoleManageGroup(user, sourceInfo);
        List<String> roleNames = roleInfoList.stream()
                .filter(roleInfo -> ManageGroup.support(manageGroup, roleInfo.getRoleCode()))
                .filter(roleInfo -> filterRole(roleMap, roleInfo.getRoleCode()))
                .map(roleInfo -> String.format("「%s」", roleInfo.getRoleName()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(roleNames)) {
            return;
        }
        StringJoiner joiner = new StringJoiner("、", "",
                Utils.TRANSFER_ADD_OBJECT.contains(describeApiName) ?
                        I18N.text(I18NKey.VALIDATE_RECORD_TYPE_BY_FUNCTION_PRIVILEGE_TRANSFER_ADD) :
                        I18N.text(I18NKey.VALIDATE_RECORD_TYPE_BY_FUNCTION_PRIVILEGE));
        roleNames.forEach(joiner::add);
        throw new ValidateException(joiner.toString());
    }

    private String getFuncName(String describeApiName) {
        if (Utils.TRANSFER_ADD_OBJECT.contains(describeApiName)) {
            return String.join(",", ObjectAction.CREATE.getActionLabel(), ObjectAction.UPDATE.getActionLabel(),
                    ObjectAction.TRANSFER_ADD.getActionLabel());
        }
        return String.join(",", ObjectAction.CREATE.getActionLabel(), ObjectAction.UPDATE.getActionLabel());
    }

    private boolean filterRole(Map<String, RecordTypeResult.RoleInfo> roleMap, String roleCode) {
        RecordTypeResult.RoleInfo roleInfo = roleMap.get(roleCode);
        if (Objects.isNull(roleInfo)) {
            return true;
        }
        return CollectionUtils.empty(roleInfo.getRecords());
    }

    private void validateRecordTypeEnable(List<String> list, RecordTypeFieldDescribe recordTypeField) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        boolean allDisabled = true;
        for (String apiName : list) {
            IRecordTypeOption recordTypeOption = recordTypeField.getRecordTypeOption(apiName);
            if (Objects.isNull(recordTypeOption)) {
                continue;
            }
            allDisabled = allDisabled && !recordTypeOption.isActive();
        }

        if (allDisabled) {
            throw new ValidateException(I18nMessage.of(I18NKey.RECORD_TYPE_NOT_EXIST, I18N.text(I18NKey.RECORD_TYPE_NOT_EXIST)));
        }
    }

    /**
     * 查询某一个对象下的业务类型列表
     */
    @Override
    public RecordTypeResult findRecordTypeList(String tenantId, String describeApiName, boolean isOnlyActive) {
        log.debug("Entering RecordTypeService findRoleAndRecordType(tenantId = {}, objectDescribeApiName = {})",
                tenantId, describeApiName);
        List<IRecordTypeOption> recordTypeList = findRecordTypeOptionList(tenantId, describeApiName, isOnlyActive);
        RecordTypeResult result = new RecordTypeResult();
        List resultList = recordTypeList.stream().map(x -> ((DocumentBasedBean) x).getContainerDocument()).collect(Collectors.toList());
        result.setRecord_list(resultList);
        return result;
    }

    @Override
    public List<IRecordTypeOption> findRecordTypeOptionList(String tenantId, String describeApiName, boolean isOnlyActive) {
        try {
            List<IRecordTypeOption> recordTypeList = recordTypeService.findByObjectDescribeApiName(tenantId, describeApiName, getActionContext(tenantId, null));
            recordTypeList = recordTypeList.stream().filter(x -> !isOnlyActive || x.isActive()).collect(Collectors.toList());
            return recordTypeList;
        } catch (MetadataServiceException e) {
            log.warn("findRecordTypeOptionList error,tenantId:{},describeApiName:{},isOnlyActive:{}", tenantId, describeApiName, isOnlyActive, e);
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * 启用业务类型（根据业务类型apiname）
     */
    public RecordTypeResult enableRecordType(User user, String describeApiName, String recordApiName) {
        log.debug("Entering RecordTypeService enableRecordType(tenantId = {}, objectDescribeApiName = {})", user.getTenantId(),
                describeApiName);

        if (Strings.isNullOrEmpty(describeApiName)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.OBJECT_DESCRIBE_IS_EMPTY, I18N.text(I18NKey.OBJECT_DESCRIBE_IS_EMPTY)));
        }
        if (Strings.isNullOrEmpty(recordApiName)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.RECORD_TYPE_IS_EMPTY, I18N.text(I18NKey.RECORD_TYPE_IS_EMPTY)));
        }
        RecordTypeResult result = new RecordTypeResult();
        CheckerResult checkerResult;
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        if (isEnableCheckEnterpriseResourcesQuote(user.getTenantId())) {
            checkRecordTypeCountLimit(user, objectDescribe, true);
        }
        try {
            IRecordTypeOption option = recordTypeService.findByApiName(user.getTenantId(), describeApiName, recordApiName);

            if (option == null) {
                log.warn("record type option is null in enableRecordType,tenant_id={}, describeApiName={}, " +
                        "optionApiName={}", user.getTenantId(), describeApiName, recordApiName);
                result.setSuccess(false);
                result.setFailMessage(I18N.text(I18NKey.RECORD_TYPE_NOT_EXIST));
                return result;
            }
            checkerResult = recordTypeService.enableRecordType(user.getTenantId(), describeApiName, option);
            result.setSuccess(checkerResult.isPass());
            result.setFailMessage(checkerResult.getFailMessage());
//            if (checkerResult.isPass()) {
//                //发送同步业务类型的mq消息
//                sendRecordTypeSyncMessage(RecordTypeProducer.FLAG_ENABLE, user.getTenantId(), describeApiName, option.getLabel(), recordApiName);
//            }
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        logService.log(user, EventType.ADD, ActionType.ENABLE_RECORD_TYPE, describeApiName, String.format(I18N.text(I18NKey.OBJECT) + "%s", objectDescribe.getDisplayName()));
        return result;
    }


    /**
     * 禁用业务类型（根据业务类型apiname）
     */
    public RecordTypeResult disableRecordType(User user, String describeApiName, String
            recordApiName) {
        String userId = user.getUserId();
        String tenantId = user.getTenantId();
        log.debug("Entering disableRecordType disableRecordType(tenantId = {}, objectDescribeApiName = {})",
                tenantId, describeApiName);
        if (Strings.isNullOrEmpty(describeApiName)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.OBJECT_DESCRIBE_IS_EMPTY, I18N.text(I18NKey.OBJECT_DESCRIBE_IS_EMPTY)));
        }
        if (Strings.isNullOrEmpty(recordApiName)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.RECORD_TYPE_IS_EMPTY, I18N.text(I18NKey.RECORD_TYPE_IS_EMPTY)));
        }
        RecordTypeResult result = new RecordTypeResult();
        CheckerResult checkerResult;
        IObjectDescribe objectDescribe;
        try {
            IRecordTypeOption option = recordTypeService.findByApiName(tenantId, describeApiName, recordApiName);
            if (option == null) {
                log.warn("record type option is null in disableRecordType,tenant_id={}, describeApiName={}, " +
                        "optionApiName={}", tenantId, describeApiName, recordApiName);
                result.setSuccess(false);
                result.setFailMessage(I18N.text(I18NKey.RECORD_TYPE_NOT_EXIST));
                return result;
            }
            checkerResult = recordTypeService.disableRecordType(tenantId, describeApiName, option, getActionContext(tenantId, userId));
            result.setSuccess(checkerResult.isPass());
            result.setFailMessage(checkerResult.getFailMessage());
            objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
//            if (checkerResult.isPass()) {
//                //发送同步业务类型的mq消息
//                sendRecordTypeSyncMessage(RecordTypeProducer.FLAG_DISABLE, tenantId, describeApiName, option.getLabel(), recordApiName);
//            }
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        logService.log(user, EventType.ADD, ActionType.DISABLE_RECORD_TYPE, describeApiName, String.format(I18N.text(I18NKey.OBJECT) + "%s", objectDescribe.getDisplayName()));

        return result;
    }

    private IActionContext getActionContext(String tenantId, String userId) {
        IActionContext actionContext = ActionContextExt.of(new User(tenantId, userId)).getContext();
        return actionContext;
    }

    /**
     * 删除业务类型（根据业务类型recordApiName）
     */
    public RecordTypeResult deleteRecordType(User user, String describeApiName, String recordApiName) {
        String tenantId = user.getTenantId();
        log.info("Entering deleteRecordType tenantId:{},objectDescribeApiName:{}", tenantId, describeApiName);
        if (Strings.isNullOrEmpty(describeApiName)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.OBJECT_DESCRIBE_IS_EMPTY, I18N.text(I18NKey.OBJECT_DESCRIBE_IS_EMPTY)));
        }
        if (Strings.isNullOrEmpty(recordApiName)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.RECORD_TYPE_IS_EMPTY, I18N.text(I18NKey.RECORD_TYPE_IS_EMPTY)));
        }
        RecordTypeResult result = new RecordTypeResult();
        CheckerResult checkerResult;
        IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, describeApiName);
        try {
            IRecordTypeOption option = recordTypeService.findByApiName(tenantId, describeApiName, recordApiName);
            if (option == null) {
                log.warn("record type option is null in deleteRecordType,tenant_id={}, describeApiName={}, " +
                        "optionApiName={}", tenantId, describeApiName, recordApiName);
                result.setSuccess(false);
                result.setFailMessage(I18N.text(I18NKey.RECORD_TYPE_NOT_EXIST));
                return result;
            }

            checkerResult = recordTypeService.deleteRecordType(tenantId, describeApiName, option, getActionContext(tenantId, null));
            result.setSuccess(checkerResult.isPass());
            result.setFailMessage(checkerResult.getFailMessage());

            if (checkerResult.isPass()) {
                //触发相关计算字段的全量计算
                fieldRelationCalculateService.checkRecordLabelChange(objectDescribe, option, false);

                IObjectDescribe describe = describeLogicService.findObject(tenantId, describeApiName);
                List<IFieldDescribe> updatedFields = ObjectDescribeExt.of(describe).cleanChildOptionRelationForRecordType(option);
                describeLogicService.updateFieldDescribe(user, describe, updatedFields);
            }
//            if (checkerResult.isPass()) {
//                //发送同步业务类型的mq消息
//                sendRecordTypeSyncMessage(RecordTypeProducer.FLAG_DELETE, tenantId, describeApiName, option.getLabel(), recordApiName);
//            }
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        logService.log(user, EventType.ADD, ActionType.DELETE_RECORD_TYPE, describeApiName, String.format(I18N.text(I18NKey.OBJECT) + "%s", objectDescribe.getDisplayName()));

        return result;
    }

    /**
     * 更新业务类型基本信息
     */
    public RecordTypeResult updateRecordType(User user, String describeApiName, String recordType) {
        String tenantId = user.getTenantId();
        log.debug("Entering recordTypeService updateRecordType updateRecordType(tenantId = {}, objectDescribeApiName " +
                "= {})", tenantId, describeApiName);
        if (Strings.isNullOrEmpty(describeApiName)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.OBJECT_DESCRIBE_IS_EMPTY, I18N.text(I18NKey.OBJECT_DESCRIBE_IS_EMPTY)));
        }
        if (Strings.isNullOrEmpty(recordType)) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.RECORD_TYPE_IS_EMPTY, I18N.text(I18NKey.RECORD_TYPE_IS_EMPTY)));
        }
        IRecordTypeOption option = new RecordTypeOption();
        Boolean success = false;
        RecordTypeResult result = new RecordTypeResult();
        IObjectDescribe objectDescribe;
        try {
            objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(tenantId, describeApiName);
            option.fromJsonString(recordType);
            success = recordTypeService.updateRecordType(tenantId, describeApiName, option, ActionContextExt.of(user).getContext());

            result.setSuccess(success);

            //触发相关计算字段的全量计算
            fieldRelationCalculateService.checkRecordLabelChange(objectDescribe, option, true);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        logService.log(user, EventType.ADD, ActionType.UPDATE_RECORD_TYPE, describeApiName, String.format(I18N.text(I18NKey.OBJECT) + "%s", objectDescribe.getDisplayName()));
        return result;
    }

    /**
     * 根据对象apiname和record查询主角色的layout
     */
    public RecordTypeResult findLayoutByRecordType(IObjectDescribe objectDescribe, String recordApiName, User user, String layoutType) {
        if (Strings.isNullOrEmpty(layoutType)) {
            layoutType = LayoutTypes.DETAIL;
        }
        String roleCode = getDefaultRole(user);
        List<RoleViewPojo> roleViewPojoList = findRoleViewList(user, objectDescribe.getApiName(), layoutType,
                false, recordApiName, roleCode);

        //角色是主角色，所以不用传（需要和海波沟通）
        RecordTypeResult result = new RecordTypeResult();
        if (CollectionUtils.empty(roleViewPojoList)) {
            result.setLayout(null);
            return result;
        }

        String layoutApiName = roleViewPojoList.get(0).getViewId();
        ILayout layout = layoutService.findLayoutByApiName(user, layoutApiName, objectDescribe.getApiName());
        LayoutExt.of(layout).removeOwnerOrDepartmentFieldIfIsDetailObj(objectDescribe);
        result.setLayout(((DocumentBasedBean) layout).getContainerDocument());
        result.setObjectDescribe(((DocumentBasedBean) objectDescribe).getContainerDocument());
        return result;
    }

    /**
     * 获取主角色
     */
    private String getDefaultRole(User user) {
        GetMultiEmployeeRoleRelationEntitiesByEmployeeIDsModel.Arg arg = new
                GetMultiEmployeeRoleRelationEntitiesByEmployeeIDsModel.Arg();
        arg.setAuthContext(user);
        arg.setUsers(Lists.newArrayList(user.getUserId()));
        GetMultiEmployeeRoleRelationEntitiesByEmployeeIDsModel.Result result = recordTypeAuthProxy
                .getMultiEmployeeRoleRelationEntitiesByEmployeeIDs(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        List<UserRolePojo> userRolePojo = result.getResult();

        boolean isHasDefaultRole = false;
        String defaultRole = "";
        for (UserRolePojo userRole : userRolePojo) {
            if (userRole.getDefaultRole()) {
                isHasDefaultRole = true;
                defaultRole = userRole.getRoleCode();
                break;
            }
        }
        if (!isHasDefaultRole) {
            throw new MetaDataBusinessException(I18nMessage.of(I18NKey.MISSING_PRIMARY_ROLE, I18N.text(I18NKey.MISSING_PRIMARY_ROLE)), CRMErrorCode.DEFAULT_ROLE_NOT_EXIST_EXCEPTION.getCode());
        }
        return defaultRole;
    }

    public RecordTypeResult findAssignedLayout(String layoutType, String describeApiName, User user) {
        return findAssignedLayout(layoutType, describeApiName, user, null);
    }

    public RecordTypeResult findAssignedLayout(String layoutType, String describeApiName, User user, String sourceInfo) {
        return findAssignedLayout(layoutType, describeApiName, null, user, sourceInfo);
    }

    /**
     * 查询布局分配情况
     */
    public RecordTypeResult findAssignedLayout(String layoutType, String describeApiName, String whatDescribeApiName, User user, String sourceInfo) {
        return findAssignedLayout(user, describeApiName, whatDescribeApiName, layoutType, sourceInfo, null);
    }

    public RecordTypeResult findAssignedLayout(User user, String describeApiName, String whatDescribeApiName,
                                               String layoutType, String sourceInfo, String appId) {
        if (Strings.isNullOrEmpty(layoutType)) {
            layoutType = LayoutTypes.DETAIL;
        }
        String tenantId = user.getTenantId();
        String viewEntityId = FlowTaskLayoutExt.getRoleViewEntityId(describeApiName, whatDescribeApiName, layoutType);
        List<IRecordTypeOption> recordTypeList = findRecordTypeOptionList(tenantId, viewEntityId, false);
        List<ILayout> layoutList = getLayoutByType(tenantId, describeApiName, whatDescribeApiName, layoutType, appId);

        List<Map<String, Object>> layoutApiAndLabelList = Lists.newArrayList();
        ILayout defaultLayout = null;
        Map<String, ILayout> layoutMap = Maps.newHashMap();
        for (ILayout layout : layoutList) {
            if (BooleanUtils.isTrue(layout.isDefault())) {
                defaultLayout = layout;
            }
            Map<String, Object> map = Maps.newHashMap();
            map.put("api_name", layout.getName());
            map.put("label", layout.get("display_name", String.class));
            map.put(ILayout.IS_DEFAULT, layout.isDefault());
            layoutApiAndLabelList.add(map);
            layoutMap.put(layout.getName(), layout);
        }
        if (defaultLayout == null) {
            throw new ValidateException(I18nMessage.of(I18NKey.DEFAULT_LAYOUT_NOT_EXIST, I18N.text(I18NKey.DEFAULT_LAYOUT_NOT_EXIST)));
        }
        List<Map> recordDocumentList = recordTypeList.stream()
                .map(recordTypeOption -> ((DocumentBasedBean) recordTypeOption).getContainerDocument())
                .collect(Collectors.toList());
        RecordTypeResult result = new RecordTypeResult();
        result.setLayout_list(layoutApiAndLabelList);
        result.setRecord_list(recordDocumentList);

        List<RoleInfoPojo> roleInfoPojoList = findRoleInfoByAppIdWithMangeGroup(user, sourceInfo, describeApiName, appId);
        if (CollectionUtils.empty(roleInfoPojoList)) {
            result.setRole_list(null);
            return result;
        }

        String viewType = FlowTaskLayoutExt.getRoleViewType(describeApiName, layoutType);
        List<RoleViewPojo> roleViewPojoList = findRoleViewList(user, viewEntityId, viewType, false, appId);

        //组织role_list结构
        List<Document> roleList = Lists.newArrayList();
        for (RoleInfoPojo roleInfo : roleInfoPojoList) {
            Document role = new Document();
            role.put("roleCode", roleInfo.getRoleCode());
            role.put("label", roleInfo.getRoleName());
            List<Map> recordLayoutList = Lists.newArrayList();
            for (IRecordTypeOption option : recordTypeList) {
                Map<String, String> item = Maps.newHashMap();
                item.put("record_api_name", option.getApiName());
                String layoutApiName = checkInRoleView(roleInfo.getRoleCode(), option.getApiName(), viewType, roleViewPojoList);
                if (Strings.isNullOrEmpty(layoutApiName) || !layoutMap.containsKey(layoutApiName)) {
                    log.warn("layout not exist, tenantId:{}, describeApiName:{}, whatApiName:{}, roleCode:{}, recordType:{},layoutType:{}, layoutApiName:{}",
                            user.getTenantId(), describeApiName, whatDescribeApiName, roleInfo.getRoleCode(), option.getApiName(), viewType, layoutApiName);
                    item.put("layout_api_name", defaultLayout.getName());
                } else {
                    item.put("layout_api_name", layoutApiName);
                }
                recordLayoutList.add(item);
            }
            role.put("record_layout", recordLayoutList);
            roleList.add(role);
        }

        ManageGroup layoutManageGroup = layoutService.queryLayoutManageGroup(user, describeApiName, sourceInfo);
        result.setLayoutManageGroup(layoutManageGroup);
        result.setRole_list(roleList);
        return result;
    }

    private String checkInRoleView(String roleCode, String recordTypeApiName, String layoutType, List<RoleViewPojo> roleViewPojoList) {
        if (CollectionUtils.empty(roleViewPojoList)) {
            return null;
        }
        for (RoleViewPojo roleViewPojo : roleViewPojoList) {
            if (roleViewPojo.getRoleCode().equals(roleCode)
                    && roleViewPojo.getRecordTypeId().equals(recordTypeApiName)
                    && Optional.ofNullable(roleViewPojo.getViewType()).orElse(LayoutTypes.DETAIL).equals(layoutType)) {
                return roleViewPojo.getViewId();
            }
        }
        return null;
    }

    /**
     * 保存分配布局
     */
    public RecordTypeResult saveLayoutAssign(String layoutType, String describeApiName, String json, User user) {
        return saveLayoutAssign(layoutType, describeApiName, json, user, null);
    }

    public RecordTypeResult saveLayoutAssign(String layoutType, String describeApiName, String json, User user, String sourceInfo) {
        return saveLayoutAssign(layoutType, describeApiName, null, json, user, sourceInfo, null);
    }

    public RecordTypeResult saveLayoutAssign(String layoutType, String describeApiName, String whatDescribeApiName,
                                             String json, User user, String sourceInfo, String appId) {
        log.debug("Entering RecordTypeService saveLayoutAssign(tenantId = {}, objectDescribeApiName = {}, whatDescribeApiName:{}, json={})",
                user.getTenantId(), describeApiName, whatDescribeApiName, json);
        if (Strings.isNullOrEmpty(layoutType)) {
            layoutType = LayoutTypes.DETAIL;
        }
        RecordTypeResult result = new RecordTypeResult();
        JSONArray jsonArray = JSONObject.parseArray(json);
        if (null == jsonArray || jsonArray.isEmpty()) {
            result.setSuccess(false);
            return result;
        }

        String roleViewEntityId = FlowTaskLayoutExt.getRoleViewEntityId(describeApiName, whatDescribeApiName, layoutType);
        String viewType = FlowTaskLayoutExt.getRoleViewType(describeApiName, layoutType);
        String tenantId = user.getTenantId();
        List<RoleViewPojo> list = Lists.newArrayList();
        JSONObject object;
        for (int index = 0; index < jsonArray.size(); index++) {
            object = jsonArray.getJSONObject(index);
            String roleCode = object.getString("roleCode");

            JSONArray recordLayout = object.getJSONArray("record_layout");
            String recordTypeApiName;
            String layoutApiName;
            if (null == recordLayout || recordLayout.isEmpty()) {
                continue;
            }

            for (Object obj : recordLayout) {
                JSONObject jsonObject = (JSONObject) obj;
                recordTypeApiName = jsonObject.getString("record_api_name");
                layoutApiName = jsonObject.getString("layout_api_name");
                RoleViewPojo roleViewPojo = new RoleViewPojo();
                roleViewPojo.setEntityId(roleViewEntityId);
                roleViewPojo.setRecordTypeId(recordTypeApiName);
                roleViewPojo.setRoleCode(roleCode);
                roleViewPojo.setTenantId(tenantId);
                roleViewPojo.setViewId(layoutApiName);
                roleViewPojo.setViewType(viewType);
                list.add(roleViewPojo);
            }
        }

        upsertRoleViewList(user, list, appId);
        result.setSuccess(true);
        return result;
    }

    @Override
    public List<RoleViewPojo> findRoleViewList(User user, String describeApiName, String layoutType, boolean allAppScope) {
        return findRoleViewList(user, describeApiName, layoutType, allAppScope, null, null);
    }

    @Override
    public List<RoleViewPojo> findRoleViewList(User user, String describeApiName, String layoutType, boolean allAppScope, String appId) {
        return findRoleViewList(user, describeApiName, layoutType, allAppScope, null, null, appId);
    }

    @Override
    public List<RoleViewPojo> findRoleViewList(User user, String describeApiName, String layoutType,
                                               boolean allAppScope, String recordType, String roleCode) {
        return findRoleViewList(user, describeApiName, layoutType, allAppScope, recordType, roleCode, null);
    }

    @Override
    public List<RoleViewPojo> findRoleViewList(User user, String describeApiName, String layoutType, boolean allAppScope,
                                               String recordType, String roleCode, String appId) {
        try {
            AuthContext authContext = buildAuthContext(user, allAppScope, appId, describeApiName);
            return viewClient.findView(authContext, describeApiName, recordType, roleCode, layoutType);
        } catch (AuthException e) {
            log.error("findRoleViewList error,user:{},describeApiName:{},layoutType:{},allAppScope:{},recordType:{},roleCode:{}",
                    user, describeApiName, layoutType, allAppScope, recordType, roleCode, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<RoleViewPojo> batchFindRoleViewList(User user, String describeApiName, Set<String> recordTypes,
                                                    Set<String> roleCodes, Set<String> layoutTypes) {
        return batchFindRoleViewList(user, describeApiName, recordTypes, roleCodes, layoutTypes, false);
    }

    @Override
    public List<RoleViewPojo> batchFindRoleViewList(User user, String describeApiName, Set<String> recordTypes,
                                                    Set<String> roleCodes, Set<String> layoutTypes, String appId) {
        return batchFindRoleViewList(user, describeApiName, recordTypes, roleCodes, layoutTypes, false, appId);
    }

    @Override
    public List<RoleViewPojo> batchFindRoleViewList(User user, String describeApiName, Set<String> recordTypes,
                                                    Set<String> roleCodes, Set<String> layoutTypes, boolean allAppScope) {
        return batchFindRoleViewList(user, describeApiName, recordTypes, roleCodes, layoutTypes, allAppScope, null);
    }

    @Override
    public List<RoleViewPojo> batchFindRoleViewList(User user, String describeApiName, Set<String> recordTypes, Set<String> roleCodes,
                                                    Set<String> layoutTypes, boolean allAppScope, String appId) {
        try {

            String objectAPIName = changeOrderLogicService.findOriginalApiNameByChangeOrder(user, describeApiName);
            if (!Strings.isNullOrEmpty(objectAPIName)) {
                describeApiName = objectAPIName;
            }
            AuthContext authContext = buildAuthContext(user, allAppScope, appId, describeApiName);
            return viewClient.batchQueryView(authContext, describeApiName,
                    recordTypes, roleCodes, layoutTypes);
        } catch (AuthException e) {
            log.error("batchFindRoleViewList error,user:{},describeApiName:{},recordTypes:{},roleCodes:{},layoutTypes:{},allAppScope:{}",
                    user, describeApiName, recordTypes, roleCodes, layoutTypes, allAppScope, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void upsertRoleViewList(User user, List<RoleViewPojo> roleViewPojoList) {
        upsertRoleViewList(user, roleViewPojoList, false);
    }

    @Override
    public void upsertRoleViewList(User user, List<RoleViewPojo> roleViewPojoList, String appId) {
        upsertRoleViewList(user, roleViewPojoList, false, appId);
    }

    @Override
    public void upsertRoleViewList(User user, List<RoleViewPojo> roleViewPojoList, boolean allAppScope) {
        upsertRoleViewList(user, roleViewPojoList, allAppScope, null);
    }

    @Override
    public void upsertRoleViewList(User user, List<RoleViewPojo> roleViewPojoList, boolean allAppScope, String appId) {
        if (CollectionUtils.empty(roleViewPojoList)) {
            return;
        }
        //补充viewType，防止调用方没有传值
        roleViewPojoList.stream().filter(roleViewPojo -> Strings.isNullOrEmpty(roleViewPojo.getViewType()))
                .forEach(roleViewPojo -> {
                    if (ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE.equals(roleViewPojo.getRecordTypeId())) {
                        roleViewPojo.setViewType(LayoutTypes.LIST_LAYOUT);
                    } else {
                        roleViewPojo.setViewType(LayoutTypes.DETAIL);
                    }
                });
        String describeApiName = roleViewPojoList.stream()
                .map(RoleViewPojo::getEntityId)
                .findFirst()
                .orElse(null);
        AuthContext authContext = buildAuthContext(user, allAppScope, appId, describeApiName);
        try {
            viewClient.addViewAccess(authContext, roleViewPojoList);
        } catch (AuthException e) {
            log.error("addViewAccess error,authContext:{},roleViewPojoList:{}", authContext, roleViewPojoList, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void deleteRoleView(User user, String describeApiName, String layoutApiName, boolean allAppScope) {
        AuthContext authContext = AuthContextExt.of(user, allAppScope).getAuthContext();
        try {
            viewClient.delViewAccess(authContext, describeApiName, layoutApiName);
        } catch (AuthException e) {
            log.error("delViewAccess error,authContext:{},describeApiName:{},layoutApiName:{}", authContext,
                    describeApiName, layoutApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IRecordTypeOption> findValidRecordTypeList(String describeApiName, User user) {
        log.debug("Entering RecordTypeService findValidRecordTypeList(tenantId = {}, objectDescribeApiName = {}, " +
                "userID={})", user.getTenantId(), describeApiName, user.getUserId());
        List<String> userRole = findRoleCodeByAppId(user);

        List<IRecordTypeOption> recordList = Lists.newArrayList();
        List<IRecordTypeOption> optionList;
        List<IRecordTypeOption> allOptionList;
        try {
            allOptionList = recordTypeService.findByObjectDescribeApiName(user.getTenantId(), describeApiName, getActionContext(user.getTenantId(), user.getUserId()));
            optionList = allOptionList.stream().filter(x -> x.isActive()).collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("findValidRecordTypeList error,user:{},describeApiName:{}", user, describeApiName, e);
            throw new MetaDataBusinessException(e);
        }
        if (CollectionUtils.empty(userRole)) {
            IRecordTypeOption recordTypeOption = getRecordTypeOption(optionList, MultiRecordType.RECORD_TYPE_DEFAULT);
            recordList.add(recordTypeOption);
        } else {
            BatchFindRecordType.Arg arg = BatchFindRecordType.Arg.builder().entityId(describeApiName).roleCodes(userRole).build();
            arg.setAuthContext(user);

            FindRecordTypeModel.Result result = recordTypeAuthProxy.batchFindRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
            List<RecordTypePojo> recordTypePojoList = result.getResult();

            if (CollectionUtils.notEmpty(recordTypePojoList)) {
                List<IRecordTypeOption> allList = recordTypePojoList.stream()
                        .map(x -> getRecordTypeOption(optionList, x.getRecordTypeId()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                List<String> allOptionApiNameList = allOptionList.stream().map(IRecordTypeOption::getApiName).collect(Collectors.toList());
                allList = CollectionUtils.sortByGivenOrder(allList, allOptionApiNameList, IRecordTypeOption::getApiName);
                allList.forEach(x -> {
                    if (!listContainsRecordType(recordList, x.getApiName())) {
                        recordList.add(x);
                    }
                });
            }

        }

        return recordList;
    }

    private List<String> findRoleCodeByAppId(User user) {
        List<String> userRoleCodes = userRoleInfoService.getUserRole(user);
        if (!user.isOutUser()
                || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIND_ROLE_CODE_BY_APP_ID_GRAY, user.getTenantId())
                || CollectionUtils.empty(userRoleCodes)) {
            return userRoleCodes;
        }
        String appId = RequestUtil.getAppId();
        Set<String> defineRoleCodesInApp = findListAppOuterRolesByAppId(user, appId).stream()
                .map(RoleInfoPojo::getRoleCode)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(defineRoleCodesInApp)) {
            return userRoleCodes;
        }
        return userRoleCodes.stream()
                .filter(defineRoleCodesInApp::contains)
                .collect(Collectors.toList());
    }

    @Override
    public void checkRecordTypeCountLimit(User user, IObjectDescribe objectDescribe) {
        checkRecordTypeCountLimit(user, objectDescribe, false);
    }


    @Override
    public void checkRecordTypeCountLimit(User user, IObjectDescribe objectDescribe, Boolean isEnable) {
        IFieldDescribe recordTypeField = objectDescribe.getFieldDescribe(MultiRecordType.RECORD_TYPE);
        if (null == recordTypeField) {
            return;
        }

        RecordType recordType = (RecordType) recordTypeField;
        if (CollectionUtils.empty(recordType.getRecordTypeOptions())) {
            return;
        }
        int size = recordType.getRecordTypeOptions().size();
        if (isEnable) {
            size--;
        }
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        tenantLicenseInfo.checkRecordTypeCount(size, objectDescribe.getApiName());
    }

    private boolean listContainsRecordType(List<IRecordTypeOption> recordList, String recordApiName) {
        if (CollectionUtils.empty(recordList)) {
            return false;
        }

        for (IRecordTypeOption option : recordList) {
            if (StringUtils.equals(option.getApiName(), recordApiName)) {
                return true;
            }
        }

        return false;
    }

    private IRecordTypeOption getRecordTypeOption(List<IRecordTypeOption> optionList, String recordType) {
        if (CollectionUtils.empty(optionList)) {
            return null;
        }

        for (IRecordTypeOption option : optionList) {
            if (option != null && !Strings.isNullOrEmpty(option.getApiName()) && option.getApiName().equals(recordType)) {
                return option;
            }
        }
        return null;
    }

    @Override
    public Map<String, List<IRecordTypeOption>> findValidRecordTypeListMap(List<String> describeApiNameList, User user) {
        if (CollectionUtils.empty(describeApiNameList)) {
            return Maps.newHashMap();
        }
        Map<String, List<IRecordTypeOption>> optionResult = Maps.newHashMap();
        List<String> userRole = findRoleCodeByAppId(user);

        BatchFindRecordType.Arg arg = BatchFindRecordType.Arg.builder().entityIds(describeApiNameList).roleCodes(userRole).build();
        arg.setAuthContext(user);
        FindRecordTypeModel.Result result = recordTypeAuthProxy.batchFindRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        Map<String, List<RecordTypePojo>> recordTypePojoMap = CollectionUtils.nullToEmpty(result.getResult()).stream()
                .collect(Collectors.groupingBy(x -> x.getEntityId()));

        Map<String, List<IRecordTypeOption>> optionMap = findRecordTypes(user.getTenantId(), describeApiNameList);
        describeApiNameList.forEach(apiName -> {
            List<IRecordTypeOption> optionList = CollectionUtils.nullToEmpty(optionMap.get(apiName)).stream()
                    .filter(x -> x.isActive()).collect(Collectors.toList());
            if (CollectionUtils.empty(userRole)) {
                IRecordTypeOption recordTypeOption = getRecordTypeOption(optionList, MultiRecordType.RECORD_TYPE_DEFAULT);
                optionResult.put(apiName, Lists.newArrayList(recordTypeOption));
            } else {
                List<IRecordTypeOption> resultList = Lists.newArrayList();
                List<RecordTypePojo> recordTypePojoList = recordTypePojoMap.get(apiName);
                if (CollectionUtils.notEmpty(recordTypePojoList)) {
                    List<IRecordTypeOption> allList = recordTypePojoList.stream()
                            .map(x -> getRecordTypeOption(optionList, x.getRecordTypeId()))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    allList.forEach(x -> {
                        if (!listContainsRecordType(resultList, x.getApiName())) {
                            resultList.add(x);
                        }
                    });
                }
                optionResult.put(apiName, resultList);
            }
        });
        return optionResult;
    }

    @Override
    public Map<String, List<IRecordTypeOption>> findRecordTypes(String tenantId, List<String> objectApiNames) {
        List<IObjectDescribe> describes = Lists.newArrayList(describeLogicService.findObjectsWithoutCopyIfGray(tenantId, objectApiNames).values());
        if (CollectionUtils.empty(describes)) {
            return Maps.newHashMap();
        }
        Map<String, List<IRecordTypeOption>> result = Maps.newHashMap();
        describes.forEach(x -> {
            ObjectDescribeExt.of(x).getFieldDescribeSilently(IObjectDescribe.RECORD_TYPE).ifPresent(y -> {
                //先拷贝一下，防止原始数据被修改
                RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe) FieldDescribeExt.of(y).copyOnWrite();
                result.put(x.getApiName(), recordTypeFieldDescribe.getRecordTypeOptions());
            });
        });

        return result;
    }

    @Override
    public boolean checkRecordType(String describeApiName, String recordType) {
        CheckRecordTypeModel.Arg arg = new CheckRecordTypeModel.Arg();
        arg.setEntityId(describeApiName);
        arg.setRecordTypeId(recordType);
        CheckRecordTypeModel.Result result = recordTypeAuthProxy.checkRecordType(arg, PAAS_PRIVILEGE_HEADDER.defaultHeader());
        return Objects.equals(result.getResult(), Boolean.TRUE);
    }


    @Override
    public Map<String, List<IRecordTypeOption>> bulkFindValidRecordTypeList(List<IObjectDescribe> describeList, User user) {
        log.debug("Entering RecordTypeService bulkFindValidRecordTypeList(tenantId = {},  " +
                "userID={})", user.getTenantId(), user.getUserId());
        List<String> userRole = findRoleCodeByAppId(user);
        Map<String, List<IRecordTypeOption>> map = Maps.newHashMap();

        if (CollectionUtils.empty(userRole)) {
            describeList.forEach(a -> {
                IRecordTypeOption recordTypeOption = ObjectDescribeExt.of(a).getDefaultRecordType();
                map.put(a.getApiName(), Lists.newArrayList(recordTypeOption));
            });
            return map;
        }

        Map<String, List<IRecordTypeOption>> validTypeMap = Maps.newHashMap();
        describeList.forEach(a -> {
            Optional<RecordTypeFieldDescribe> recordTypeField = ObjectDescribeExt.of(a).getRecordTypeField();
            if (!recordTypeField.isPresent()) {
                return;
            }
            List<IRecordTypeOption> list = recordTypeField.get().getRecordTypeOptions();
            validTypeMap.put(a.getApiName(), list.stream().filter(IRecordTypeOption::isActive).collect(Collectors.toList()));
        });

        List<RecordTypePojo> recordTypePojoList = batchFindValidRecordType(describeList, user, userRole);
        recordTypePojoList.forEach(a -> {
            IRecordTypeOption recordTypeOption = getRecordTypeOption(validTypeMap.get(a.getEntityId()), a.getRecordTypeId());
            if (Objects.isNull(recordTypeOption)) {
                return;
            }

            if (map.containsKey(a.getEntityId())) {
                addRecordType(map.get(a.getEntityId()), recordTypeOption);
            } else {
                map.put(a.getEntityId(), Lists.newArrayList(recordTypeOption));
            }
        });

        return map;
    }

    @Override
    public Map<String, List<IRecordTypeOption>> findValidRecordTypesByFunctionCode(User user,
                                                                                   List<IObjectDescribe> describeList,
                                                                                   ObjectAction action) {
        List<String> userRole = findRoleCodeByAppId(user);
        Map<String, List<IRecordTypeOption>> resultMap = Maps.newHashMap();

        if (CollectionUtils.empty(userRole)) {
            return Collections.emptyMap();
        }
        Map<String, List<IRecordTypeOption>> recordTypeMap = Maps.newHashMap();
        describeList.forEach(a -> {
            Optional<RecordTypeFieldDescribe> recordTypeField = ObjectDescribeExt.of(a).getRecordTypeField();
            if (!recordTypeField.isPresent()) {
                return;
            }
            List<IRecordTypeOption> list = recordTypeField.get().getRecordTypeOptions();
            recordTypeMap.put(a.getApiName(), list.stream().filter(IRecordTypeOption::isActive).collect(Collectors.toList()));
        });
        List<RecordTypePojo> recordTypePojoList = batchFindValidRecordType(describeList, user, userRole);
        Map<String, Set<String>> describeApiNameRoleMap = findRoleCodesByFunction(Lists.newArrayList(action), describeList, user);

        recordTypePojoList.forEach(it -> {
            IRecordTypeOption recordTypeOption = getRecordTypeOption(recordTypeMap.get(it.getEntityId()), it.getRecordTypeId());
            if (Objects.isNull(recordTypeOption)) {
                return;
            }
            if (!describeApiNameRoleMap.getOrDefault(it.getEntityId(), Collections.emptySet()).contains(it.getRoleCode())) {
                return;
            }
            if (resultMap.containsKey(it.getEntityId())) {
                addRecordType(resultMap.get(it.getEntityId()), recordTypeOption);
            } else {
                resultMap.put(it.getEntityId(), Lists.newArrayList(recordTypeOption));
            }
        });
        return resultMap;
    }

    public Map<String, List<IRecordTypeOption>> bulkFindValidRecordTypeListWithTransfer(List<IObjectDescribe> describeList, User user) {
        Map<String, List<IRecordTypeOption>> resultMap = bulkFindValidRecordTypeList(describeList, user);
        handleTransfer(describeList, resultMap, user.getTenantId());
        return resultMap;
    }

    /**
     * 入参中有客户对象，但没有下发客户的业务类型的时，补充一个（客户对象下的所有业务类型）
     *
     * @param describeList
     * @param resultMap
     * @param tenantId
     */
    private void handleTransfer(List<IObjectDescribe> describeList, Map<String, List<IRecordTypeOption>> resultMap, String tenantId) {
        if (describeList.stream().noneMatch(it -> Utils.ACCOUNT_API_NAME.equals(it.getApiName()))) {
            return;
        }
        if (resultMap.containsKey(Utils.ACCOUNT_API_NAME)) {
            return;
        }
        try {
            List<IRecordTypeOption> recordTypeOptions = recordTypeService.findByObjectDescribeApiName(tenantId, Utils.ACCOUNT_API_NAME, getActionContext(tenantId, null));
            resultMap.put(Utils.ACCOUNT_API_NAME, recordTypeOptions);
        } catch (MetadataServiceException e) {
            log.warn("handleTransfer fail, ei:{}", tenantId, e);
        }
    }

    @Override
    public List<String> findValidAndMatchRecordTypes(User user, String masterApiName, String masterRecordType, String detailApiName) {
        List<IRecordTypeOption> validRecordTypeOptionList = findValidRecordTypeList(detailApiName, user);
        List<String> validRecordTypes = validRecordTypeOptionList.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        return filterUnMatchRecordTypes(user.getTenantId(), validRecordTypes, masterApiName, masterRecordType, detailApiName);
    }

    private List<String> filterUnMatchRecordTypes(String tenantId, List<String> recordTypes, String masterApiName, String masterRecordType, String detailApiName) {
        if (CollectionUtils.empty(recordTypes)) {
            return Lists.newArrayList();
        }
        List<String> matchTypes = Lists.newArrayList(recordTypes);
        try {
            List<IObjectRelationMatch> relationMatchList = relationMatchServiceImpl.queryRelationMatchOfBusinessType(tenantId,
                    masterApiName, masterRecordType, IFieldType.RECORD_TYPE, detailApiName);
            if (CollectionUtils.empty(relationMatchList)) {
                return matchTypes;
            }
            relationMatchList.forEach(x -> {
                String targetValue = x.getTargetValue();
                List<RecordInfo> recordInfos = JSONArray.parseArray(targetValue, RecordInfo.class);
                if (CollectionUtils.empty(recordInfos)) {
                    return;
                }
                recordInfos.stream()
                        .filter(y -> Boolean.FALSE.equals(y.isMatch()))
                        .forEach(y -> matchTypes.remove(y.getApiName()));
            });
        } catch (Exception e) {
            log.error("queryRelationMatchOfBusinessType tenantId:{},sourceApiName:{},recordType:{},targetApiName:{},error,", tenantId,
                    masterApiName, masterRecordType, detailApiName, e);
        }
        return matchTypes;
    }

    @Override
    public Map<String, List<IRecordTypeOption>> filterUnMatchRecordTypes(String tenantId, Map<String, List<IRecordTypeOption>> recordTypeOptionMap, String masterApiName, String masterRecordType) {
        if (CollectionUtils.empty(recordTypeOptionMap)) {
            return recordTypeOptionMap;
        }
        Map<String, List<IRecordTypeOption>> matchRecordTypeOptionMap = Maps.newHashMap(recordTypeOptionMap);
        try {
            List<IObjectRelationMatch> relationMatchList = relationMatchServiceImpl.queryRelationMatchOfBusinessType(tenantId,
                    masterApiName, masterRecordType, IFieldType.RECORD_TYPE, "");
            if (CollectionUtils.empty(relationMatchList)) {
                return matchRecordTypeOptionMap;
            }
            relationMatchList.forEach(x -> {
                if (!matchRecordTypeOptionMap.containsKey(x.getTargetApiName())) {
                    return;
                }
                List<RecordInfo> recordInfos = JSONArray.parseArray(x.getTargetValue(), RecordInfo.class);
                if (CollectionUtils.empty(recordInfos)) {
                    return;
                }
                List<String> unMatchTypes = recordInfos.stream()
                        .filter(y -> Boolean.FALSE.equals(y.isMatch()))
                        .map(y -> y.getApiName())
                        .collect(Collectors.toList());
                matchRecordTypeOptionMap.get(x.getTargetApiName()).removeIf(y -> unMatchTypes.contains(y.getApiName()));
            });
        } catch (Exception e) {
            log.error("queryRelationMatchOfBusinessType tenantId:{},sourceApiName:{},recordType:{},error,", tenantId,
                    masterApiName, masterRecordType, e);
        }
        return matchRecordTypeOptionMap;
    }

    @Override
    public List<IObjectRelationMatch> findMatchRecordTypeRelation(String tenantId, String sourceApiName, String sourceValue, String targetApiName) {
        List<IObjectRelationMatch> relationMatchList;
        try {
            relationMatchList = relationMatchServiceImpl.queryRelationMatchOfBusinessType(tenantId, sourceApiName, sourceValue, IFieldType.RECORD_TYPE, targetApiName);
        } catch (MetadataServiceException e) {
            log.warn("queryRelationMatchOfBusinessType tenantId:{},sourceApiName:{},recordType:{},targetApiName:{},error:{}", tenantId,
                    sourceApiName, sourceValue, targetApiName, e);
            throw new MetaDataBusinessException("find recordType relation failure.");
        }
        return relationMatchList;
    }

    @Override
    public List<IObjectRelationMatch> createOrUpdateRecordTypeRelation(ServiceContext context, List<IObjectRelationMatch> relationMatchList) {
        return createOrUpdateRecordTypeRelation(context.getUser(), relationMatchList);
    }

    @Override
    public List<IObjectRelationMatch> createOrUpdateRecordTypeRelation(User user, List<IObjectRelationMatch> relationMatchList) {
        ActionContext actionContext = (ActionContext) ActionContextExt.of(user).getContext();
        try {
            return relationMatchServiceImpl.createOrUpdateRelationMatchOfBusinessType(actionContext, relationMatchList);
        } catch (MetadataServiceException e) {
            log.warn("createOrUpdateRelationMatchOfBusinessType tenantId:{},relationMatchList,error:{}", user.getTenantId(),
                    relationMatchList, e);
            throw new MetaDataBusinessException("update recordType relation failure.");
        }
    }

    @Override
    public boolean validateLayoutAndRecordType(User user, Map<String, IObjectDescribe> objectDescribes, List<DescribeLayoutValidateModel> describeLayoutValidateModels) {
        // 查找主对象的布局类型、API名称和记录类型
        Optional<DescribeLayoutValidateModel> masterModelOpt = describeLayoutValidateModels.stream()
                .filter(x -> BooleanUtils.isTrue(x.getIsMaster()))
                .findFirst();

        if (!masterModelOpt.isPresent()) {
            log.warn("Master object configuration not found");
            return false;
        }
        DescribeLayoutValidateModel masterModel = masterModelOpt.get();
        // 验证所有对象的布局配置
        return describeLayoutValidateModels.stream().allMatch(model ->
                validateModelLayout(user, objectDescribes, model, masterModel.getLayoutType()));
    }

    @Override
    public Map<String, String> getMainRoleRecordLayoutMappingByRecordTypes(User user, String defaultLayoutApiName, String describeApiName,
                                                                           Function<List<String>, Map<String, Layout>> findLayoutMap) {
        List<IRecordTypeOption> recordTypeOptionList = findRecordTypeOptionList(user.getTenantId(), describeApiName, false);
        Set<String> types = recordTypeOptionList.stream().map(IRecordTypeOption::getApiName).collect(Collectors.toSet());
        //查当前人员的所有角色信息
        List<GetRolesByUserId.UserRole> roleInfoByUserList = userRoleInfoService.getRoleInfoByUser(user);

        Set<String> roleCodeList = roleInfoByUserList.stream().map(GetRolesByUserId.UserRole::getRoleCode).collect(Collectors.toSet());

        String defaultRoleCode = roleInfoByUserList.stream()
                .filter(GetRolesByUserId.UserRole::getDefaultRole)
                .map(GetRolesByUserId.UserRole::getRoleCode)
                .findFirst().orElse("");

        Map<String, String> mapping = Maps.newHashMap();
        List<RoleViewPojo> roleViewPojos = Lists.newArrayList();
        //主角色不为空，则根据主角色查询当前应用下主角色对应的视图
        if (StringUtils.isNotEmpty(defaultRoleCode)) {
            roleViewPojos = batchFindRoleViewList(user, describeApiName, types,
                    Sets.newHashSet(defaultRoleCode), Sets.newHashSet(LayoutTypes.LIST), RequestUtil.getAppId());
        } else {
            log.warn("no main role code， tenantId:{}, userId:{}, objApiName:{}", user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser(), describeApiName);
        }
        //（不是主角色 || 主角色当前应用下对应的视图为空） && 是下游   则查询当前用户所有角色对应的布局，并拿到当前业务类型对应的最新创建的布局
        if (CollectionUtils.empty(roleViewPojos) && user.isOutUser()) {
            //查询当前app中所适用的角色
            List<String> listAppOuterRolesByAppId = findListAppOuterRolesByAppId(user, RequestUtil.getAppId())
                    .stream().map(RoleInfoPojo::getRoleCode).collect(Collectors.toList());
            //如果主角色不在当前应用中则，取其他角色对应的布局，否则取默认布局
            if (!listAppOuterRolesByAppId.contains(defaultRoleCode)) {
                //查询当前用户所有角色下的布局，业务类型关系
                roleViewPojos = batchFindRoleViewList(user, describeApiName, types,
                        roleCodeList, Sets.newHashSet(LayoutTypes.LIST), RequestUtil.getAppId());
                //获取所有的布局
                List<String> layoutApiNames = roleViewPojos.stream().map(RoleViewPojo::getViewId).collect(Collectors.toList());

                Map<String, Layout> layoutMap = findLayoutMap.apply(layoutApiNames);

                Map<String, Set<Layout>> recordToLayout = Maps.newHashMap();
                roleViewPojos.forEach(x -> recordToLayout.computeIfAbsent(x.getRecordTypeId(), t -> Sets.newHashSet()).add(layoutMap.get(x.getViewId())));

                recordToLayout.forEach((key, layoutList) ->
                        layoutList.stream()
                                .filter(Objects::nonNull)
                                .max(Comparator.comparing(Layout::getCreateTime))
                                .map(Layout::getName)
                                .ifPresent(x -> mapping.put(key, x)));
            }
        } else {
            mapping.putAll(roleViewPojos.stream()
                    .collect(Collectors.toMap(RoleViewPojo::getRecordTypeId, RoleViewPojo::getViewId, (a, b) -> a)));
        }
        types.forEach(recordType -> mapping.putIfAbsent(recordType, defaultLayoutApiName));

        return mapping;
    }

    /**
     * 验证模型的布局配置是否匹配
     */
    private boolean validateModelLayout(User user, Map<String, IObjectDescribe> objectDescribes,
                                        DescribeLayoutValidateModel model, String layoutType) {
        IObjectDescribe describe = objectDescribes.get(model.getObjectApiName());
        if (Objects.isNull(describe)) {
            log.warn("Object describe not found for: {}", model.getObjectApiName());
            return false;
        }
        return model.getLayoutRuleValidateInfos().stream().allMatch(layoutRule -> {
            // 查找指定类型的布局
            ILayout layout = layoutService.findObjectLayoutWithType(
                    LayoutLogicService.LayoutContext.of(user),
                    BooleanUtils.isTrue(model.getIsMaster()) ? model.getMasterRecordType() : layoutRule.getRecordType(),
                    describe,
                    layoutType,
                    null,
                    false);
            if (Objects.isNull(layout)) {
                log.warn("Layout not found for object: {}, recordType: {}, layoutType: {}",
                        model.getObjectApiName(), layoutRule.getRecordType(), layoutType);
                return false;
            }
            boolean valid = StringUtils.equals(layout.getName(), layoutRule.getLayoutApiName());
            if (!valid) {
                log.warn("Layout name mismatch - expected: {}, actual: {}, object: {}, recordType: {}",
                        layoutRule.getLayoutApiName(), layout.getName(),
                        model.getObjectApiName(), layoutRule.getRecordType());
            }
            return valid;
        });
    }

    private void addRecordType(List<IRecordTypeOption> list, IRecordTypeOption option) {
        Optional<IRecordTypeOption> first = list.stream().filter(a -> Objects.equals(a.getApiName(), option.getApiName())).findFirst();
        if (!first.isPresent()) {
            list.add(option);
        }
    }


    private List<RecordTypePojo> batchFindValidRecordType(List<IObjectDescribe> describeList, User user, List<String> userRole) {
        List<String> apiNameList = describeList.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
        BatchFindRecordType.Arg arg = BatchFindRecordType.Arg.builder().entityIds(apiNameList).roleCodes(userRole).build();
        arg.setAuthContext(user);

        FindRecordTypeModel.Result result = recordTypeAuthProxy.batchFindRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        return result.getResult();
    }

    private AuthContext buildAuthContext(User user, boolean allAppScope, String appId, String describeApiName) {
        if (!applicationLayeredGrayService.supportAppLayered(user, appId, describeApiName)) {
            return AuthContextExt.of(user, allAppScope).getAuthContext();
        }
        return AuthContextExt.of(user, allAppScope, appId).getAuthContext();
    }

    @Data
    @AllArgsConstructor
    public static class RecordInfo {
        private String apiName;
        private boolean match;
    }
}
