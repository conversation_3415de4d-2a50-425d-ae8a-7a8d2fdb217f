package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectRelationGraphBuilder单元测试类
 */
@ExtendWith(MockitoExtension.class)
class ObjectRelationGraphBuilderTest {

    private ObjectRelationGraphBuilder objectRelationGraphBuilder;
    private List<ObjectRelationGraphBuilder.GraphLayer> graphLayers;
    private ObjectDescribe objectDescribe;
    private ObjectDescribe targetObjectDescribe;

    @BeforeEach
    void setUp() {
        // 创建测试对象描述
        objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("TestObject");
        objectDescribe.setTenantId("74255");

        targetObjectDescribe = new ObjectDescribe();
        targetObjectDescribe.setApiName("TargetObject");
        targetObjectDescribe.setTenantId("74255");

        // 创建图层
        List<IObjectDescribe> describes = Lists.newArrayList(objectDescribe);
        ObjectRelationGraphBuilder.GraphLayer graphLayer = ObjectRelationGraphBuilder.GraphLayer.of(describes);
        graphLayers = Lists.newArrayList(graphLayer);

        // 创建构建器
        objectRelationGraphBuilder = ObjectRelationGraphBuilder.builder()
                .graphLayers(graphLayers)
                .includeRefMany(false)
                .build();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式的正常构建
     */
    @Test
    @DisplayName("Builder模式 - 正常构建")
    void testBuilder_正常构建() {
        // 执行被测试方法
        ObjectRelationGraphBuilder builder = ObjectRelationGraphBuilder.builder()
                .graphLayers(graphLayers)
                .includeRefMany(true)
                .build();

        // 验证结果
        assertNotNull(builder);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法不带目标对象
     */
    @Test
    @DisplayName("获取图 - 不带目标对象")
    void testGetGraph_不带目标对象() {
        // 执行被测试方法
        ObjectRelationGraph result = objectRelationGraphBuilder.getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertNotNull(result.getDescribeMap());
        assertTrue(result.getDescribeMap().containsKey("TestObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getGraph方法带目标对象
     */
    @Test
    @DisplayName("获取图 - 带目标对象")
    void testGetGraph_带目标对象() {
        // 执行被测试方法
        ObjectRelationGraph result = objectRelationGraphBuilder.getGraph(targetObjectDescribe);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertNotNull(result.getDescribeMap());
        assertTrue(result.getDescribeMap().containsKey("TestObject"));
        assertTrue(result.getDescribeMap().containsKey("TargetObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带主从字段的对象构建
     */
    @Test
    @DisplayName("构建图 - 带主从字段")
    void testBuildGraph_带主从字段() {
        // 创建带主从字段的对象描述
        ObjectDescribe masterObject = new ObjectDescribe();
        masterObject.setApiName("MasterObject");
        masterObject.setTenantId("74255");

        ObjectDescribe detailObject = new ObjectDescribe();
        detailObject.setApiName("DetailObject");
        detailObject.setTenantId("74255");

        // 创建主从字段
        Map<String, Object> masterDetailFieldConfig = new HashMap<>();
        masterDetailFieldConfig.put("api_name", "master_detail_field__c");
        masterDetailFieldConfig.put("type", IFieldType.MASTER_DETAIL);
        masterDetailFieldConfig.put("target_api_name", "MasterObject");
        IFieldDescribe masterDetailField = FieldDescribeFactory.newInstance(masterDetailFieldConfig);

        // 设置字段到详情对象
        List<IFieldDescribe> detailFields = Lists.newArrayList(masterDetailField);
        detailObject.setFieldDescribes(detailFields);

        // 创建图层
        List<IObjectDescribe> describes = Lists.newArrayList(masterObject, detailObject);
        ObjectRelationGraphBuilder.GraphLayer graphLayer = ObjectRelationGraphBuilder.GraphLayer.of(describes);
        List<ObjectRelationGraphBuilder.GraphLayer> layers = Lists.newArrayList(graphLayer);

        // 创建构建器
        ObjectRelationGraphBuilder builder = ObjectRelationGraphBuilder.builder()
                .graphLayers(layers)
                .includeRefMany(false)
                .build();

        // 执行被测试方法
        ObjectRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertTrue(result.getDescribeMap().containsKey("MasterObject"));
        assertTrue(result.getDescribeMap().containsKey("DetailObject"));
        
        // 验证图中有节点
        assertFalse(result.getNetwork().nodes().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带引用字段的对象构建
     */
    @Test
    @DisplayName("构建图 - 带引用字段")
    void testBuildGraph_带引用字段() {
        // 创建带引用字段的对象描述
        ObjectDescribe sourceObject = new ObjectDescribe();
        sourceObject.setApiName("SourceObject");
        sourceObject.setTenantId("74255");

        ObjectDescribe refObject = new ObjectDescribe();
        refObject.setApiName("RefObject");
        refObject.setTenantId("74255");

        // 创建引用字段
        Map<String, Object> referenceFieldConfig = new HashMap<>();
        referenceFieldConfig.put("api_name", "reference_field__c");
        referenceFieldConfig.put("type", IFieldType.OBJECT_REFERENCE);
        referenceFieldConfig.put("target_api_name", "RefObject");
        IFieldDescribe referenceField = FieldDescribeFactory.newInstance(referenceFieldConfig);

        // 设置字段到源对象
        List<IFieldDescribe> sourceFields = Lists.newArrayList(referenceField);
        sourceObject.setFieldDescribes(sourceFields);

        // 创建图层
        List<IObjectDescribe> describes = Lists.newArrayList(sourceObject, refObject);
        ObjectRelationGraphBuilder.GraphLayer graphLayer = ObjectRelationGraphBuilder.GraphLayer.of(describes);
        List<ObjectRelationGraphBuilder.GraphLayer> layers = Lists.newArrayList(graphLayer);

        // 创建构建器
        ObjectRelationGraphBuilder builder = ObjectRelationGraphBuilder.builder()
                .graphLayers(layers)
                .includeRefMany(true)
                .build();

        // 执行被测试方法
        ObjectRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertTrue(result.getDescribeMap().containsKey("SourceObject"));
        assertTrue(result.getDescribeMap().containsKey("RefObject"));
        
        // 验证图中有节点
        assertFalse(result.getNetwork().nodes().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空图层列表的场景
     */
    @Test
    @DisplayName("构建图 - 空图层列表")
    void testBuildGraph_空图层列表() {
        // 创建空图层列表的构建器
        ObjectRelationGraphBuilder builder = ObjectRelationGraphBuilder.builder()
                .graphLayers(Lists.newArrayList())
                .includeRefMany(false)
                .build();

        // 执行被测试方法
        ObjectRelationGraph result = builder.getGraph();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertNotNull(result.getDescribeMap());
        assertTrue(result.getDescribeMap().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试GraphLayer.of静态方法
     */
    @Test
    @DisplayName("GraphLayer创建 - 静态方法")
    void testGraphLayer_of静态方法() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(objectDescribe);

        // 执行被测试方法
        ObjectRelationGraphBuilder.GraphLayer result = ObjectRelationGraphBuilder.GraphLayer.of(describes);

        // 验证结果
        assertNotNull(result);
        assertEquals(describes, result.getDescribeList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectNode.of静态方法
     */
    @Test
    @DisplayName("ObjectNode创建 - 静态方法")
    void testObjectNode_of静态方法() {
        // 准备测试数据
        String describeApiName = "TestObject";

        // 执行被测试方法
        ObjectRelationGraphBuilder.ObjectNode result = ObjectRelationGraphBuilder.ObjectNode.of(describeApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(describeApiName, result.getDescribeApiName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectNode的compareTo方法
     */
    @Test
    @DisplayName("ObjectNode比较 - compareTo方法")
    void testObjectNode_compareTo方法() {
        // 准备测试数据
        ObjectRelationGraphBuilder.ObjectNode node1 = ObjectRelationGraphBuilder.ObjectNode.of("AObject");
        ObjectRelationGraphBuilder.ObjectNode node2 = ObjectRelationGraphBuilder.ObjectNode.of("BObject");
        ObjectRelationGraphBuilder.ObjectNode node3 = ObjectRelationGraphBuilder.ObjectNode.of("AObject");

        // 执行被测试方法并验证结果
        assertTrue(node1.compareTo(node2) < 0);  // A < B
        assertTrue(node2.compareTo(node1) > 0);  // B > A
        assertEquals(0, node1.compareTo(node3)); // A == A
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectEdge.of静态方法
     */
    @Test
    @DisplayName("ObjectEdge创建 - 静态方法")
    void testObjectEdge_of静态方法() {
        // 准备测试数据
        String describeApiName = "SourceObject";
        String fieldApiName = "field__c";
        String targetApiName = "TargetObject";
        String edgeType = IFieldType.OBJECT_REFERENCE;

        // 执行被测试方法
        ObjectRelationGraphBuilder.ObjectEdge result = ObjectRelationGraphBuilder.ObjectEdge.of(
                describeApiName, fieldApiName, targetApiName, edgeType);

        // 验证结果
        assertNotNull(result);
        assertEquals(describeApiName, result.getDescribeApiName());
        assertEquals(fieldApiName, result.getFieldApiName());
        assertEquals(targetApiName, result.getTargetApiName());
        assertEquals(edgeType, result.getEdgeType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectEdge.equals静态方法
     */
    @Test
    @DisplayName("ObjectEdge比较 - equals静态方法")
    void testObjectEdge_equals静态方法() {
        // 准备测试数据
        ObjectRelationGraphBuilder.ObjectEdge edge1 = ObjectRelationGraphBuilder.ObjectEdge.of(
                "Object1", "field1", "Target1", IFieldType.OBJECT_REFERENCE);
        ObjectRelationGraphBuilder.ObjectEdge edge2 = ObjectRelationGraphBuilder.ObjectEdge.of(
                "Object1", "field2", "Target1", IFieldType.MASTER_DETAIL);
        ObjectRelationGraphBuilder.ObjectEdge edge3 = ObjectRelationGraphBuilder.ObjectEdge.of(
                "Object2", "field1", "Target2", IFieldType.OBJECT_REFERENCE);

        // 执行被测试方法并验证结果
        assertTrue(ObjectRelationGraphBuilder.ObjectEdge.equals(edge1, edge2)); // 相同的describe和target
        assertFalse(ObjectRelationGraphBuilder.ObjectEdge.equals(edge1, edge3)); // 不同的describe和target
        assertTrue(ObjectRelationGraphBuilder.ObjectEdge.equals(edge1, edge1)); // 相同对象
        assertFalse(ObjectRelationGraphBuilder.ObjectEdge.equals(edge1, null)); // null比较
        assertFalse(ObjectRelationGraphBuilder.ObjectEdge.equals(null, edge1)); // null比较
        assertTrue(ObjectRelationGraphBuilder.ObjectEdge.equals(null, null)); // 都为null
    }
}
