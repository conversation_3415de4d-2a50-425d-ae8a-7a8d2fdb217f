package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.metadata.I18nMessageExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Maps;

import java.util.List;

public class SidebarLayoutProcessorImpl extends AbstractMobileLayoutProcessor {


    public SidebarLayoutProcessorImpl(PageType pageType, LayoutExt webLayout, ObjectDescribeExt describeExt,
                                      IObjectData objectData, List<IComponent> componentConfig) {
        super(pageType, webLayout, describeExt, objectData, componentConfig);
    }

    @Override
    protected LayoutExt extractLayout() {
        LayoutExt targetLayout;
        if (webLayout.isEnableSidebarLayout()) {
            targetLayout = LayoutExt.of(webLayout.getSidebarLayout());
            syncLayoutFromWebLayout(targetLayout);
        } else if (AppFrameworkConfig.isCustomSidebarObject(describeExt.getApiName())) {
            targetLayout = LayoutExt.of(Maps.newHashMap());
            targetLayout.setButtons(webLayout.getButtonOrder());
            targetLayout.setHiddenButtons(webLayout.getHiddenButtons());
            targetLayout.setHiddenComponents(webLayout.getHiddenComponents());
            targetLayout.setComponents(filterMobileComponents());
            targetLayout.setLayoutStructure(buildMobileLayoutStructure(targetLayout));
        } else {
            targetLayout = AppFrameworkConfig.getSidebarLayoutTemplate(describeExt.getApiName())
                    .map(LayoutExt::of)
                    .orElseThrow(() -> new ValidateException(I18nMessage.of(I18NKey.NO_SIDEBAR_LAYOUT_TEMPLATE,
                            I18NExt.text(I18NKey.NO_SIDEBAR_LAYOUT_TEMPLATE, describeExt.getDisplayName()),
                            I18nMessageExt.buildDescribeDisplayName(describeExt))));
            syncLayoutFromWebLayout(targetLayout);
        }
        return targetLayout;
    }


    @Override
    protected void createLayoutResetFromComponentToFirst(LayoutExt targetLayout) {

    }

    @Override
    protected void mergeFormComponentToTargetLayout(LayoutExt targetLayout) {

    }

    @Override
    protected boolean isEnableTargetLayout() {
        return webLayout.isEnableSidebarLayout();
    }
}
