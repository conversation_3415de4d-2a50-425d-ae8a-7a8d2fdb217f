package com.facishare.paas.appframework.metadata.config;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.facishare.paas.appframework.common.util.Tuple;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ObjectConfigServiceImpl的单元测试
 * 测试对象配置服务的功能
 */
@ExtendWith(MockitoExtension.class)
class ObjectConfigServiceImplTest {

    @Mock
    private ObjectConfigManager mockConfigManager;

    @Mock
    private ObjectConfigProvider mockConfigProvider;

    @Mock
    private BusinessFilterProvider mockBusinessFilterProvider;

    @Mock
    private DescribeLogicService mockDescribeLogicService;

    @InjectMocks
    private ObjectConfigServiceImpl objectConfigService;

    private User testUser;
    private Set<String> testApiNameList;

    @BeforeEach
    void setUp() {
        // 构造测试用户
        testUser = User.systemUser("74255");
        
        // 构造测试API名称列表
        testApiNameList = Sets.newHashSet("Account", "Contact", "Opportunity");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置的正常场景
     */
    @Test
    @DisplayName("获取对象配置 - 正常场景")
    void testGetObjectConfig_正常场景() {
        // 配置Mock行为
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenReturn(Maps.newHashMap());

        // 执行被测试方法
        Map<String, ObjectConfig> result = objectConfigService.getObjectConfig(testUser, testApiNameList);

        // 验证结果
        assertNotNull(result);

        // 验证Mock交互
        verify(mockDescribeLogicService).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置时用户为null
     */
    @Test
    @DisplayName("获取对象配置 - 用户为null")
    void testGetObjectConfig_用户为null() {
        // 执行被测试方法并验证异常
        assertThrows(Exception.class, () -> {
            objectConfigService.getObjectConfig(null, testApiNameList);
        });
        
        // 验证没有调用其他服务
        verify(mockDescribeLogicService, never()).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置时API名称列表为空
     */
    @Test
    @DisplayName("获取对象配置 - API名称列表为空")
    void testGetObjectConfig_API名称列表为空() {
        // 准备测试数据
        Set<String> emptyApiNameList = Sets.newHashSet();
        
        // 配置Mock行为
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenReturn(Maps.newHashMap());
        
        // 执行被测试方法
        Map<String, ObjectConfig> result = objectConfigService.getObjectConfig(testUser, emptyApiNameList);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证Mock交互
        verify(mockDescribeLogicService).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取对象配置时服务抛出异常
     */
    @Test
    @DisplayName("获取对象配置 - 服务抛出异常")
    void testGetObjectConfig_服务抛出异常() {
        // 配置Mock行为 - 抛出异常
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenThrow(new RuntimeException("服务异常"));
        
        // 执行被测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            objectConfigService.getObjectConfig(testUser, testApiNameList);
        });
        
        // 验证异常信息
        assertEquals("服务异常", exception.getMessage());
        
        // 验证Mock交互
        verify(mockDescribeLogicService).findObjectsWithoutCopyIfGray(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectConfigService);
        assertNotNull(mockConfigManager);
        assertNotNull(mockConfigProvider);
        assertNotNull(mockBusinessFilterProvider);
        assertNotNull(mockDescribeLogicService);
    }

    /**
     * 测试批量业务配置优化 - 验证使用批量接口
     */
    @Test
    @DisplayName("测试批量业务配置优化")
    void testBatchBusinessConfigOptimization() {
        // 准备测试数据
        Set<String> apiNameList = Sets.newHashSet("AccountObj", "ContactObj");
        Map<String, IObjectDescribe> mockObjectDescribes = Maps.newHashMap();
        Map<String, Map<String, Object>> mockBatchBusinessConfigs = Maps.newHashMap();

        // 模拟对象描述
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        mockObjectDescribes.put("AccountObj", mockDescribe);
        mockObjectDescribes.put("ContactObj", mockDescribe);

        // 模拟批量业务配置
        mockBatchBusinessConfigs.put("AccountObj", Maps.newHashMap());
        mockBatchBusinessConfigs.put("ContactObj", Maps.newHashMap());

        // 配置Mock行为
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenReturn(mockObjectDescribes);
        when(mockConfigManager.batchSearchBusinessConfig(anyString(), any()))
                .thenReturn(mockBatchBusinessConfigs);
        when(mockConfigManager.searchObjectConfig(anyString(), anyString()))
                .thenReturn(Maps.newHashMap());
        when(mockConfigManager.searchRuleConfig(anyString(), anyString()))
                .thenReturn(Maps.newHashMap());
        when(mockConfigManager.searchFieldConfig(anyString(), anyString(), any()))
                .thenReturn(Maps.newHashMap());
        when(mockConfigManager.searchLayoutConfig(anyString(), anyString()))
                .thenReturn(Maps.newHashMap());
        when(mockConfigManager.searchLayoutRuleConfig(anyString(), anyString()))
                .thenReturn(Maps.newHashMap());
        when(mockConfigManager.searchFilterConfig(anyString(), anyString()))
                .thenReturn(Maps.newHashMap());
        when(mockConfigProvider.handle(anyString(), anyString(), any(), any(), anyString()))
                .thenReturn(Tuple.of(Maps.newHashMap(), Maps.newHashMap()));
        when(mockBusinessFilterProvider.handle(anyString(), anyString(), any(), any()))
                .thenReturn(Tuple.of(Maps.newHashMap(), Maps.newHashMap()));

        // 模拟字段描述
        when(mockDescribe.getFieldDescribes()).thenReturn(Collections.emptyList());

        // 执行被测试方法
        Map<String, ObjectConfig> result = objectConfigService.getObjectConfig(
                testUser, apiNameList, true, true, "test");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("AccountObj"));
        assertTrue(result.containsKey("ContactObj"));

        // 验证关键：使用了批量接口而不是循环调用单个接口
        verify(mockConfigManager, times(1)).batchSearchBusinessConfig(anyString(), any());
        verify(mockConfigManager, never()).searchBusinessConfig(anyString(), anyString());
    }

    /**
     * 测试getBusinessConfig方法的批量优化
     */
    @Test
    @DisplayName("测试getBusinessConfig批量优化")
    void testGetBusinessConfigBatchOptimization() {
        // 准备测试数据
        Set<String> apiNameList = Sets.newHashSet("AccountObj", "ContactObj");
        Map<String, IObjectDescribe> mockObjectDescribes = Maps.newHashMap();
        Map<String, Map<String, Object>> mockBatchBusinessConfigs = Maps.newHashMap();

        // 模拟对象描述
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        mockObjectDescribes.put("AccountObj", mockDescribe);
        mockObjectDescribes.put("ContactObj", mockDescribe);

        // 模拟批量业务配置
        Map<String, Object> accountConfig = Maps.newHashMap();
        accountConfig.put("testKey", "testValue");
        Map<String, Object> contactConfig = Maps.newHashMap();
        contactConfig.put("testKey", "testValue");

        mockBatchBusinessConfigs.put("AccountObj", accountConfig);
        mockBatchBusinessConfigs.put("ContactObj", contactConfig);

        // 配置Mock行为
        when(mockDescribeLogicService.findObjectsWithoutCopyIfGray(anyString(), any()))
                .thenReturn(mockObjectDescribes);
        when(mockConfigManager.batchSearchBusinessConfig(anyString(), any()))
                .thenReturn(mockBatchBusinessConfigs);

        // 执行被测试方法
        Map<String, Object> result = objectConfigService.getBusinessConfig(testUser, apiNameList);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("AccountObj"));
        assertTrue(result.containsKey("ContactObj"));
        assertEquals(accountConfig, result.get("AccountObj"));
        assertEquals(contactConfig, result.get("ContactObj"));

        // 验证关键：使用了批量接口
        verify(mockConfigManager, times(1)).batchSearchBusinessConfig(anyString(), any());
        verify(mockConfigManager, never()).searchBusinessConfig(anyString(), anyString());
    }
}
