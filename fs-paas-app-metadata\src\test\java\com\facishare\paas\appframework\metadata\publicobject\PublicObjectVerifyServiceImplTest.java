package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobParamVerifyInfo;
import com.facishare.paas.appframework.metadata.publicobject.verify.PublicObjectJobVerify;
import com.facishare.paas.appframework.metadata.publicobject.verify.PublicObjectJobVerifyManger;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PublicObjectVerifyServiceImpl的JUnit5测试类
 * 
 * GenerateByAI
 * 
 * 测试目标：
 * - 覆盖PublicObjectVerifyServiceImpl的所有3个public方法
 * - 测试验证逻辑的正确性和异常处理
 * - 验证灰度配置的验证逻辑
 * - 确保Mock配置正确，达到80%以上覆盖率
 * 
 * 测试策略：
 * - 使用AAA模式组织测试代码
 * - 覆盖正常流程、异常流程、边界条件
 * - 重点测试验证逻辑和异常抛出场景
 * - 验证与依赖服务的交互
 * 
 * 覆盖率目标：80%以上
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("PublicObjectVerifyServiceImpl JUnit5 测试")
class PublicObjectVerifyServiceImplTest extends PublicObjectTestBase {

    @InjectMocks
    private PublicObjectVerifyServiceImpl publicObjectVerifyService;

    @Mock
    private PublicObjectJobParamVerifyInfo mockJobParamVerifyInfo;

    private PublicObjectJobInfo testJobInfo;

    /**
     * 测试前的特定设置
     */
    @Override
    protected void specificSetUp() {
        // 创建测试用的业务对象
        testJobInfo = createTestJobInfo();

        // 配置Mock对象的基本行为
        when(mockJobParamVerifyInfo.getJobType()).thenReturn(PublicObjectJobType.OPEN_JOB);
        when(mockJobParamVerifyInfo.verifyBeforeCreateJob()).thenReturn(true);

        // 配置常用的Mock行为
        setupCommonMockBehaviors();
    }

    /**
     * 配置常用的Mock行为
     */
    private void setupCommonMockBehaviors() {
        // 配置DescribeLogicService的常用行为
        when(mockDescribeLogicService.findObjectWithoutCopy(anyString(), anyString()))
                .thenReturn(mockObjectDescribe);
        
        // 配置对象描述的基本属性
        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");
        when(mockObjectDescribe.getApiName()).thenReturn(TEST_OBJECT_API_NAME);
        when(mockObjectDescribe.getTenantId()).thenReturn(TEST_TENANT_ID);
        
        // 配置PublicObjectJobVerifyManger
        when(mockPublicObjectJobVerifyManger.getPublicObjectJobVerify(any(PublicObjectJobType.class)))
                .thenReturn(Optional.of(mockPublicObjectJobVerify));
        
        // 配置PublicObjectJobVerify
        PublicObjectJobVerifyResult mockVerifyResult = PublicObjectMockFactory.createMockJobVerifyResult(true);
        when(mockPublicObjectJobVerify.verifyByJob(any(User.class), any(IObjectDescribe.class), any(PublicObjectJobParamVerifyInfo.class)))
                .thenReturn(mockVerifyResult);
    }

    // ==================== findDesignerResource方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试查找设计器资源 - 正常场景，成功返回设计器资源
     */
    @Test
    @DisplayName("正常场景 - 查找设计器资源成功")
    void testFindDesignerResource_Success() {
        // Arrange
        // Mock行为已在setupCommonMockBehaviors中配置

        // Act
        DesignerResourceResult result = publicObjectVerifyService.findDesignerResource(mockUser, TEST_OBJECT_API_NAME);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getFields());
        assertNotNull(result.getFieldTypes());
        
        verify(mockDescribeLogicService).findObjectWithoutCopy(TEST_TENANT_ID, TEST_OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找设计器资源 - 对象不存在，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 对象不存在")
    void testFindDesignerResource_ObjectNotFound() {
        // Arrange
        when(mockDescribeLogicService.findObjectWithoutCopy(anyString(), anyString()))
                .thenThrow(new ValidateException("对象不存在"));

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            publicObjectVerifyService.findDesignerResource(mockUser, TEST_OBJECT_API_NAME);
        });
        
        assertEquals("对象不存在", exception.getMessage());
        verify(mockDescribeLogicService).findObjectWithoutCopy(TEST_TENANT_ID, TEST_OBJECT_API_NAME);
    }

    // ==================== verifyBeforeCreateJob方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试创建任务前验证 - 正常场景，验证通过
     */
    @Test
    @DisplayName("正常场景 - 创建任务前验证成功")
    void testVerifyBeforeCreateJob_Success() {
        // Arrange
        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isGrayPublicObject(anyString(), anyString()))
                    .thenReturn(true);
            
            when(testJobInfo.getJobType()).thenReturn(PublicObjectJobType.OPEN_JOB);
            when(testJobInfo.getJobParam()).thenReturn(createTestJobParam());
            
            // Mock ObjectDescribeExt
            try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
                ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
                mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                        .thenReturn(mockDescribeExt);
                when(mockDescribeExt.isChangeOrderObject()).thenReturn(false);
                when(mockDescribeExt.enabledChangeOrder()).thenReturn(false);
                when(mockDescribeExt.copyOnWrite()).thenReturn(mockObjectDescribe);
                
                // Mock PublicObjectJobParamVerifyInfo
                try (MockedStatic<PublicObjectJobParamVerifyInfo> mockedVerifyInfo = mockStatic(PublicObjectJobParamVerifyInfo.class)) {
                    mockedVerifyInfo.when(() -> PublicObjectJobParamVerifyInfo.fromPublicObjectJobParamDTO(any(), anyString()))
                            .thenReturn(mockJobParamVerifyInfo);
                    when(mockJobParamVerifyInfo.verifyBeforeCreateJob()).thenReturn(true);

                    // Act
                    publicObjectVerifyService.verifyBeforeCreateJob(mockUser, testJobInfo);

                    // Assert
                    verify(mockDescribeLogicService).findObjectWithoutCopy(TEST_TENANT_ID, TEST_OBJECT_API_NAME);
                    verify(mockPublicObjectJobVerifyManger).getPublicObjectJobVerify(PublicObjectJobType.OPEN_JOB);
                    verify(mockPublicObjectJobVerify).verifyByJob(any(User.class), any(IObjectDescribe.class), any(PublicObjectJobParamVerifyInfo.class));
                }
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建任务前验证 - 不在灰度名单，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 不在灰度名单")
    void testVerifyBeforeCreateJob_NotInGrayList() {
        // Arrange
        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isGrayPublicObject(anyString(), anyString()))
                    .thenReturn(false);

            // Act & Assert
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                publicObjectVerifyService.verifyBeforeCreateJob(mockUser, testJobInfo);
            });
            
            assertTrue(exception.getMessage().contains("NOT_SUPPORT_OPEN_PUBLIC_OBJECT"));
            verify(mockDescribeLogicService).findObjectWithoutCopy(TEST_TENANT_ID, TEST_OBJECT_API_NAME);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建任务前验证 - 变更单对象，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 变更单对象不能开启公共对象")
    void testVerifyBeforeCreateJob_ChangeOrderObject() {
        // Arrange
        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isGrayPublicObject(anyString(), anyString()))
                    .thenReturn(true);
            
            try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
                ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
                mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                        .thenReturn(mockDescribeExt);
                when(mockDescribeExt.isChangeOrderObject()).thenReturn(true);

                // Act & Assert
                ValidateException exception = assertThrows(ValidateException.class, () -> {
                    publicObjectVerifyService.verifyBeforeCreateJob(mockUser, testJobInfo);
                });
                
                assertTrue(exception.getMessage().contains("PUBLIC_OBJECT_CANNOT_OPEN_CHANGE_ORDER"));
                verify(mockDescribeLogicService).findObjectWithoutCopy(TEST_TENANT_ID, TEST_OBJECT_API_NAME);
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建任务前验证 - 任务类型不是Job，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 任务类型不是Job")
    void testVerifyBeforeCreateJob_NotJobType() {
        // Arrange
        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isGrayPublicObject(anyString(), anyString()))
                    .thenReturn(true);

            when(testJobInfo.getJobType()).thenReturn(PublicObjectJobType.VERIFY_JOB); // 假设VERIFY_JOB不是Job类型

            try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
                ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
                mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                        .thenReturn(mockDescribeExt);
                when(mockDescribeExt.isChangeOrderObject()).thenReturn(false);
                when(mockDescribeExt.enabledChangeOrder()).thenReturn(false);

                // Mock isJob()方法返回false
                when(testJobInfo.getJobType().isJob()).thenReturn(false);

                // Act & Assert
                ValidateException exception = assertThrows(ValidateException.class, () -> {
                    publicObjectVerifyService.verifyBeforeCreateJob(mockUser, testJobInfo);
                });

                assertTrue(exception.getMessage().contains("PARAM_ERROR"));
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建任务前验证 - 任务参数为空，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 任务参数为空")
    void testVerifyBeforeCreateJob_NullJobParam() {
        // Arrange
        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isGrayPublicObject(anyString(), anyString()))
                    .thenReturn(true);

            when(testJobInfo.getJobType()).thenReturn(PublicObjectJobType.OPEN_JOB);
            when(testJobInfo.getJobParam()).thenReturn(createTestJobParam());

            try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
                ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
                mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                        .thenReturn(mockDescribeExt);
                when(mockDescribeExt.isChangeOrderObject()).thenReturn(false);
                when(mockDescribeExt.enabledChangeOrder()).thenReturn(false);

                // Mock PublicObjectJobParamVerifyInfo返回null
                try (MockedStatic<PublicObjectJobParamVerifyInfo> mockedVerifyInfo = mockStatic(PublicObjectJobParamVerifyInfo.class)) {
                    mockedVerifyInfo.when(() -> PublicObjectJobParamVerifyInfo.fromPublicObjectJobParamDTO(any(), anyString()))
                            .thenReturn(null);

                    // Act & Assert
                    ValidateException exception = assertThrows(ValidateException.class, () -> {
                        publicObjectVerifyService.verifyBeforeCreateJob(mockUser, testJobInfo);
                    });

                    assertTrue(exception.getMessage().contains("PARAM_ERROR"));
                }
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建任务前验证 - 验证失败，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 验证失败")
    void testVerifyBeforeCreateJob_VerifyFailed() {
        // Arrange
        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isGrayPublicObject(anyString(), anyString()))
                    .thenReturn(true);

            when(testJobInfo.getJobType()).thenReturn(PublicObjectJobType.OPEN_JOB);
            when(testJobInfo.getJobParam()).thenReturn(createTestJobParam());

            try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
                ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
                mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                        .thenReturn(mockDescribeExt);
                when(mockDescribeExt.isChangeOrderObject()).thenReturn(false);
                when(mockDescribeExt.enabledChangeOrder()).thenReturn(false);
                when(mockDescribeExt.copyOnWrite()).thenReturn(mockObjectDescribe);

                try (MockedStatic<PublicObjectJobParamVerifyInfo> mockedVerifyInfo = mockStatic(PublicObjectJobParamVerifyInfo.class)) {
                    mockedVerifyInfo.when(() -> PublicObjectJobParamVerifyInfo.fromPublicObjectJobParamDTO(any(), anyString()))
                            .thenReturn(mockJobParamVerifyInfo);
                    when(mockJobParamVerifyInfo.verifyBeforeCreateJob()).thenReturn(true);

                    // Mock验证失败
                    PublicObjectJobVerifyResult mockFailedResult = PublicObjectMockFactory.createMockJobVerifyResult(false);
                    when(mockFailedResult.getMessage()).thenReturn("验证失败");
                    when(mockPublicObjectJobVerify.verifyByJob(any(User.class), any(IObjectDescribe.class), any(PublicObjectJobParamVerifyInfo.class)))
                            .thenReturn(mockFailedResult);

                    // Act & Assert
                    ValidateException exception = assertThrows(ValidateException.class, () -> {
                        publicObjectVerifyService.verifyBeforeCreateJob(mockUser, testJobInfo);
                    });

                    assertEquals("验证失败", exception.getMessage());
                }
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建任务前验证 - 不需要验证，直接通过
     */
    @Test
    @DisplayName("正常场景 - 不需要验证，直接通过")
    void testVerifyBeforeCreateJob_NoVerifyNeeded() {
        // Arrange
        try (MockedStatic<PublicObjectGrayConfig> mockedGrayConfig = mockStatic(PublicObjectGrayConfig.class)) {
            mockedGrayConfig.when(() -> PublicObjectGrayConfig.isGrayPublicObject(anyString(), anyString()))
                    .thenReturn(true);

            when(testJobInfo.getJobType()).thenReturn(PublicObjectJobType.OPEN_JOB);
            when(testJobInfo.getJobParam()).thenReturn(createTestJobParam());

            try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
                ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
                mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                        .thenReturn(mockDescribeExt);
                when(mockDescribeExt.isChangeOrderObject()).thenReturn(false);
                when(mockDescribeExt.enabledChangeOrder()).thenReturn(false);

                try (MockedStatic<PublicObjectJobParamVerifyInfo> mockedVerifyInfo = mockStatic(PublicObjectJobParamVerifyInfo.class)) {
                    mockedVerifyInfo.when(() -> PublicObjectJobParamVerifyInfo.fromPublicObjectJobParamDTO(any(), anyString()))
                            .thenReturn(mockJobParamVerifyInfo);
                    when(mockJobParamVerifyInfo.verifyBeforeCreateJob()).thenReturn(false); // 不需要验证

                    // Act
                    publicObjectVerifyService.verifyBeforeCreateJob(mockUser, testJobInfo);

                    // Assert
                    verify(mockDescribeLogicService).findObjectWithoutCopy(TEST_TENANT_ID, TEST_OBJECT_API_NAME);
                    verify(mockPublicObjectJobVerifyManger, never()).getPublicObjectJobVerify(any());
                    verify(mockPublicObjectJobVerify, never()).verifyByJob(any(), any(), any());
                }
            }
        }
    }

    // ==================== verifyJob方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试验证任务 - 正常场景，验证成功
     */
    @Test
    @DisplayName("正常场景 - 验证任务成功")
    void testVerifyJob_Success() {
        // Arrange
        PublicObjectJobVerifyResult mockVerifyResult = PublicObjectMockFactory.createMockJobVerifyResult(true);
        when(mockPublicObjectJobVerify.verifyByJob(any(User.class), any(IObjectDescribe.class), any(PublicObjectJobParamVerifyInfo.class)))
                .thenReturn(mockVerifyResult);

        // Act
        PublicObjectJobVerifyResult result = publicObjectVerifyService.verifyJob(mockUser, mockJobParamVerifyInfo, mockObjectDescribe);

        // Assert
        assertNotNull(result);
        assertTrue(result.success());

        verify(mockPublicObjectJobVerifyManger).getPublicObjectJobVerify(any(PublicObjectJobType.class));
        verify(mockPublicObjectJobVerify).verifyByJob(mockUser, mockObjectDescribe, mockJobParamVerifyInfo);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证任务 - 验证器不存在，抛出异常
     */
    @Test
    @DisplayName("异常场景 - 验证器不存在")
    void testVerifyJob_VerifierNotFound() {
        // Arrange
        when(mockPublicObjectJobVerifyManger.getPublicObjectJobVerify(any(PublicObjectJobType.class)))
                .thenReturn(Optional.empty());

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            publicObjectVerifyService.verifyJob(mockUser, mockJobParamVerifyInfo, mockObjectDescribe);
        });

        assertNotNull(exception);
        assertNotNull(exception.getMessage());
        verify(mockPublicObjectJobVerifyManger).getPublicObjectJobVerify(any(PublicObjectJobType.class));
        verify(mockPublicObjectJobVerify, never()).verifyByJob(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证任务 - 验证失败
     */
    @Test
    @DisplayName("正常场景 - 验证任务失败")
    void testVerifyJob_Failed() {
        // Arrange
        PublicObjectJobVerifyResult mockVerifyResult = PublicObjectMockFactory.createMockJobVerifyResult(false);
        when(mockPublicObjectJobVerify.verifyByJob(any(User.class), any(IObjectDescribe.class), any(PublicObjectJobParamVerifyInfo.class)))
                .thenReturn(mockVerifyResult);

        // Act
        PublicObjectJobVerifyResult result = publicObjectVerifyService.verifyJob(mockUser, mockJobParamVerifyInfo, mockObjectDescribe);

        // Assert
        assertNotNull(result);
        assertFalse(result.success());

        verify(mockPublicObjectJobVerifyManger).getPublicObjectJobVerify(any(PublicObjectJobType.class));
        verify(mockPublicObjectJobVerify).verifyByJob(mockUser, mockObjectDescribe, mockJobParamVerifyInfo);
    }

    /**
     * 创建测试用的PublicObjectJobParamDTO
     */
    private PublicObjectJobParamDTO createTestJobParam() {
        PublicObjectJobParamDTO param = new PublicObjectJobParamDTO();
        param.setObjectApiName(TEST_OBJECT_API_NAME);
        param.setJobType(PublicObjectJobType.OPEN_JOB.getType());
        param.setFields(createTestPublicFields());
        return param;
    }
}
