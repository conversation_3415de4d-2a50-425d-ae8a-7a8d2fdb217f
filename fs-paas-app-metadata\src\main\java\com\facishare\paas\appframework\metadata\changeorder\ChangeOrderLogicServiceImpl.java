package com.facishare.paas.appframework.metadata.changeorder;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18N<PERSON>ey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.LanguageClientUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.ObjectDescribeFinder;
import com.facishare.paas.appframework.metadata.layout.LayoutComponents;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.component.SummaryKeyComponentInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.facishare.paas.metadata.api.describe.Quote;
import com.facishare.paas.metadata.dao.pg.entity.metadata.DataExtra;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.DataExtraMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectFieldExtra;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.support.JsonFieldHandler;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITopInfoComponent;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Language;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2023/3/20
 */
@Slf4j
@Service("changeOrderLogicService")
public class ChangeOrderLogicServiceImpl implements ChangeOrderLogicService {
    private static final String CHANGE_ORDER_SNAPSHOT = "__change_order_snapshot__";
    public static final String CHANGE_FIELD = "change_field";
    public static final String ORIGINAL_FIELD = "original_field";

    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ApprovalFlowService approvalFlowService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private RedissonService redissonService;
    @Resource
    private DataExtraMapper dataExtraMapper;

    @Autowired
    private AppDefaultRocketMQProducer changeOrderOpenProducer;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private MetaDataService metaDataService;

    @Override
    public List<DescribeInfo> findSupportChangeOrderDescribes(String tenantId) {
        ObjectDescribeFinder build = ObjectDescribeFinder.builder()
                .user(User.systemUser(tenantId))
                .describeDefineType(null)
                .isOnlyActivate(false)
                .isExcludeDetailObj(true)
                .isExcludeDetailWithMasterCreated(false)
                .isAsc(true)
                .sourceInfo(null)
                .includeChangeOrderObject(true)
                .build();

        List<IObjectDescribe> describes = describeLogicService.findObjectsByTenantId(build);

        List<IObjectDescribe> supportChangeOrderDescribes = Lists.newArrayList();
        Map<String, String> originalDescribeMap = Maps.newHashMap();
        for (IObjectDescribe describe : describes) {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            if (describeExt.isSlaveObject() || describeExt.isPublicObject()) {
                continue;
            }
            String originalDescribeApiName = describeExt.getOriginalDescribeApiName();
            if (ChangeOrderConfig.changeOrderDescribeGray(tenantId, describe.getApiName()) && Strings.isNullOrEmpty(originalDescribeApiName)) {
                supportChangeOrderDescribes.add(describe);
            }
            if (!Strings.isNullOrEmpty(originalDescribeApiName)) {
                originalDescribeMap.putIfAbsent(originalDescribeApiName, describe.getApiName());
            }
        }
        return supportChangeOrderDescribes.stream()
                .map(describe -> DescribeInfo.of(describe.getApiName(), describe.getDisplayName(), originalDescribeMap.get(describe.getApiName())))
                .collect(Collectors.toList());
    }

    @Override
    public OriginalAndChangeDescribes findDescribesByOriginalApiName(User user, String originalDescribeApiName) {
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribeApiName)) {
            return null;
        }
        MasterAndDetailDescribes originalDescribes = getMasterAndDetailDescribes(user, originalDescribeApiName);
        // 在字段上记录那些字段可变更，那些字段可记录原值
        recordCanChangeFields(originalDescribes);
        String changeOrderDescribeApiName = findChangeOrderDescribeApiNameByOriginalApiName(user, originalDescribeApiName);
        MasterAndDetailDescribes changeOrderDescribes = Optional.ofNullable(changeOrderDescribeApiName)
                .filter(it -> !Strings.isNullOrEmpty(it))
                .map(it -> getMasterAndDetailDescribes(user, it))
                .orElseGet(() -> MasterAndDetailDescribes.of(null, Collections.emptyList()));
        return OriginalAndChangeDescribes.builder()
                .originalDescribes(originalDescribes)
                .changeOrderDescribes(changeOrderDescribes)
                .build();
    }

    private void recordCanChangeFields(MasterAndDetailDescribes originalDescribes) {
        IObjectDescribe describe = originalDescribes.getObjectDescribe();
        recordCanChangeFieldsByDescribe(describe);
        CollectionUtils.nullToEmpty(originalDescribes.getDetailDescribes()).values()
                .forEach(this::recordCanChangeFieldsByDescribe);
    }

    private void recordCanChangeFieldsByDescribe(IObjectDescribe describe) {
        ChangeOrderConfig.ChangeOrderConfigItem configItem = ChangeOrderConfig.getChangeOrderConfigItem(describe.getApiName());
        if (Objects.isNull(configItem)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            changeFields(describeExt, configItem, fieldDescribeExt);
            originalFields(describeExt, configItem, fieldDescribeExt);
        }
    }

    private void originalFields(ObjectDescribeExt describeExt, ChangeOrderConfig.ChangeOrderConfigItem configItem, FieldDescribeExt fieldDescribeExt) {
        boolean originalFields = configItem.isOriginalFields(describeExt.getApiName(), fieldDescribeExt.getFieldDescribe());
        if (!fieldDescribeExt.isGroupField()) {
            fieldDescribeExt.setIfAbsent(ORIGINAL_FIELD, originalFields);
            return;
        }
        List<IFieldDescribe> fieldList = describeExt.getGroupFieldList(fieldDescribeExt.getApiName());
        fieldDescribeExt.set(ORIGINAL_FIELD, originalFields);
        fieldList.forEach(fieldDescribe -> fieldDescribe.set(ORIGINAL_FIELD, originalFields));
    }

    private void changeFields(ObjectDescribeExt describeExt, ChangeOrderConfig.ChangeOrderConfigItem configItem, FieldDescribeExt fieldDescribeExt) {
        boolean changeFields = configItem.isChangeFields(describeExt.getApiName(), fieldDescribeExt.getFieldDescribe());
        if (!fieldDescribeExt.isGroupField()) {
            fieldDescribeExt.setIfAbsent(CHANGE_FIELD, changeFields);
            return;
        }
        // 组件字段设置为可变更，组件下的其他字段设置为不可变更
        fieldDescribeExt.set(CHANGE_FIELD, changeFields);
        List<IFieldDescribe> fieldList = describeExt.getGroupFieldList(fieldDescribeExt.getApiName());
        fieldList.forEach(fieldDescribe -> fieldDescribe.set(CHANGE_FIELD, false));
    }

    @Override
    public Optional<IObjectDescribe> findChangeOrderDescribeByOriginalApiName(User user, String originalDescribe) {
        String apiName = findChangeOrderDescribeApiNameByOriginalApiName(user, originalDescribe);
        if (Strings.isNullOrEmpty(apiName)) {
            return Optional.empty();
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), apiName);
        return Optional.of(describe);
    }

    private String findChangeOrderDescribeApiNameByOriginalApiName(User user, String apiName) {
        String changeOrderKey = getChangeOrderLockKey(null, apiName);
        return configService.findTenantConfig(user, changeOrderKey);
    }

    private void saveChangeOrderObjectFlag(User user, String originalApiName, String changeApiName) {
        String changeOrderKey = getChangeOrderLockKey(null, originalApiName);
        configService.createTenantConfig(user, changeOrderKey, changeApiName, ConfigValueType.STRING);
    }

    private void deletedChangeOrderObjectFlag(User user, String originalApiName) {
        String changeOrderKey = getChangeOrderLockKey(null, originalApiName);
        configService.deleteTenantConfig(user, changeOrderKey);
    }

    private String getChangeOrderLockKey(String tenantId, String apiName) {
        return Stream.of(tenantId, apiName)
                .filter(it -> !Strings.isNullOrEmpty(it))
                .collect(Collectors.joining("_", "obj_change_order_", ""));
    }

    @Override
    @Transactional
    public void openDetailChangeOrder(User user, IObjectDescribe originalDescribe) {

        if (!licenseService.isSupportChangeOrder(user.getTenantId()) || !ObjectDescribeExt.of(originalDescribe).isSlaveObject()) {
            return;
        }

        // 加锁防止并发开启导致的问题
        RLock lock = tryLock(user, originalDescribe.getApiName());
        try {
            MasterAndDetailDescribes masterAndDetailDescribes = MasterAndDetailDescribes.of(originalDescribe, Lists.newArrayList());
            // 校验是否支持开启变更单对象
            OpenChangeOrderResult openChangeOrderResult = canOpenChangeOrder(user, masterAndDetailDescribes);
            if (!openChangeOrderResult.success()) {
                throw new ValidateException(openChangeOrderResult.getMessage());
            }

            // 新建变更单对象的描述
            MasterAndDetailDescribes changeOrderMasterAndDetailDescribes = null;

            MasterDetailFieldDescribe masterDetailFieldDescribe = ObjectDescribeExt.of(originalDescribe).getMasterDetailFieldDescribe()
                    .orElseThrow(() -> new ValidateException(I18nMessage.of(I18NKey.PARAM_EMPTY, I18N.text(I18NKey.PARAM_EMPTY))));
            //获取主对象的变更单对象apiName
            String masterChangeOrderApiName = findChangeOrderDescribeApiNameByOriginalApiName(user, masterDetailFieldDescribe.getTargetApiName());
            //将从对象描述转为变更单描述
            changeOrderMasterAndDetailDescribes = masterAndDetailDescribes.detailConvert2ChangeOrderDescribes(masterChangeOrderApiName);
            //创建变更单描述
            createChangeOrderDescribes(user, changeOrderMasterAndDetailDescribes);

            // 更新原单对象的描述，添加一些开启变更单后需要的业务字段
            originalDescribe.addFieldDescribeList(ChangeOrderConfig.getOriginalDescribeFields(originalDescribe.getApiName()));

            // 在 bizconf 中记录一个标记，防止变更单对象重复开启
            IObjectDescribe changeOrderDescribe = changeOrderMasterAndDetailDescribes.getObjectDescribe();
            String changeOrderDescribeApiName = changeOrderDescribe.getApiName();
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        saveChangeOrderObjectFlag(user, originalDescribe.getApiName(), changeOrderDescribeApiName);
                        sendChangeOrderOpenMsg(user, originalDescribe.getApiName(), changeOrderDescribeApiName);
                    }
                });
            }
        } finally {
            redissonService.unlock(lock);
        }
    }

    @Override
    @Transactional
    public OpenChangeOrderResult openChangeOrder(User user, String originalDescribeApiName) {
        if (!licenseService.isSupportChangeOrder(user.getTenantId())) {
            return OpenChangeOrderResult.buildFail("license not support change order");
        }
        if (ObjectDescribeExt.isCustomObject(originalDescribeApiName)) {
            describeLogicService.checkDescribeCountLimit(user, false);
        }
        // 加锁防止并发开启导致的问题
        RLock lock = tryLock(user, originalDescribeApiName);
        try {
            MasterAndDetailDescribes masterAndDetailDescribes = getMasterAndAllDetailDescribes(user, originalDescribeApiName);
            // 校验是否支持开启变更单对象
            OpenChangeOrderResult openChangeOrderResult = canOpenChangeOrder(user, masterAndDetailDescribes);
            if (!openChangeOrderResult.success()) {
                log.warn("canOpenChangeOrder fail! ei:{}, apiName:{}, result:{}", user.getTenantId(), originalDescribeApiName, JacksonUtils.toJson(openChangeOrderResult));
                return openChangeOrderResult;
            }
            //被禁用的从对象不开变更单
            masterAndDetailDescribes = MasterAndDetailDescribes.of(masterAndDetailDescribes.getObjectDescribe(),
                    masterAndDetailDescribes.getDetailDescribes().values().stream().filter(IObjectDescribe::isActive).collect(Collectors.toList()));
            // 新建变更单对象的描述
            MasterAndDetailDescribes changeOrderMasterAndDetailDescribes = masterAndDetailDescribes.convert2ChangeOrderDescribes();
            createChangeOrderDescribes(user, changeOrderMasterAndDetailDescribes);
            // 更新原单对象的描述，添加一些开启变更单后需要的业务字段
            updateOriginalDescribes(user, masterAndDetailDescribes);
            // 在 bizconf 中记录一个标记，防止变更单对象重复开启
            IObjectDescribe changeOrderDescribe = changeOrderMasterAndDetailDescribes.getObjectDescribe();
            String changeOrderDescribeApiName = changeOrderDescribe.getApiName();
            saveChangeOrderObjectFlag(user, originalDescribeApiName, changeOrderDescribeApiName);
            sendChangeOrderOpenMsg(user, originalDescribeApiName, changeOrderDescribeApiName);
            return openChangeOrderResult;
        } finally {
            redissonService.unlock(lock);
        }
    }

    private void sendChangeOrderOpenMsg(User user, String originalDescribeApiName, String changeOrderDescribeApiName) {
        ChangeOrderOpenMsg msg = ChangeOrderOpenMsg.of(user.getTenantId(), originalDescribeApiName, changeOrderDescribeApiName);
        try {
            changeOrderOpenProducer.sendMessage(msg.toMessageData());
        } catch (Exception e) {
            log.warn("sendChangeOrderOpenMsg fail! msg:{}", JacksonUtils.toJson(msg), e);
        }
    }

    private void updateOriginalDescribes(User user, MasterAndDetailDescribes masterAndDetailDescribes) {
        IObjectDescribe objectDescribe = masterAndDetailDescribes.getObjectDescribe();
        objectDescribe.addFieldDescribeList(ChangeOrderConfig.getOriginalDescribeFields(objectDescribe.getApiName()));
        describeLogicService.update(user, objectDescribe);
        for (IObjectDescribe describe : masterAndDetailDescribes.getDetailDescribes().values()) {
            describe.addFieldDescribeList(ChangeOrderConfig.getOriginalDescribeFields(describe.getApiName()));
            describeLogicService.update(user, describe);
        }
    }

    private void createChangeOrderDescribes(User user, MasterAndDetailDescribes changeOrderMasterAndDetailDescribes) {
        IObjectDescribe changeOrderDescribes = changeOrderMasterAndDetailDescribes.getObjectDescribe();
        ObjectDescribeExt.of(changeOrderDescribes).setUpdateDescribeDefaultValue(user);
        describeLogicService.create(true, changeOrderDescribes);
        for (IObjectDescribe objectDescribe : changeOrderMasterAndDetailDescribes.getDetailDescribes().values()) {
            ObjectDescribeExt.of(objectDescribe).setUpdateDescribeDefaultValue(user);
            describeLogicService.create(true, objectDescribe);
        }
    }

    private RLock tryLock(User user, String apiName) {
        String key = getChangeOrderLockKey(user.getTenantId(), apiName);
        return redissonService.tryLockWithErrorMsg(0, 30, TimeUnit.SECONDS, key,
                I18NExt.text(I18NKey.NOT_REPEAT_OPENING), AppFrameworkErrorCode.PROCESSING_ALERT.getCode());
    }

    private OpenChangeOrderResult canOpenChangeOrder(User user, MasterAndDetailDescribes masterAndDetailDescribes) {
        IObjectDescribe objectDescribe = masterAndDetailDescribes.getObjectDescribe();
        if (ObjectDescribeExt.of(objectDescribe).enabledChangeOrder()) {
            return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.NOT_REPEAT_OPENING));
        }
        OpenChangeOrderResult openChangeOrderResult = masterAndDetailDescribes.verifyChangeOrder(user);
        if (!openChangeOrderResult.success()) {
            return openChangeOrderResult;
        }
        OpenChangeOrderResult checkChangeOrderApiNameResult = checkChangeOrderRepeatApiName(user, masterAndDetailDescribes);
        if (!checkChangeOrderApiNameResult.success()) {
            return checkChangeOrderApiNameResult;
        }
        if (hasActiveUpdateApprovalFlowDefinitions(user, objectDescribe.getApiName())) {
            return OpenChangeOrderResult.buildHasFlowDefinitions();
        }
        return openChangeOrderResult;
    }

    private OpenChangeOrderResult checkChangeOrderRepeatApiName(User user, MasterAndDetailDescribes masterAndDetailDescribes) {
        Set<String> describeApiNameList = masterAndDetailDescribes.getObjectDescribes().keySet();
        List<String> changeOrderDescribeApiNameList = describeApiNameList.stream().map(ObjectDescribeExt::getChangeOrderApiName).collect(Collectors.toList());
        List<String> beyondMaxLengthApiName = changeOrderDescribeApiNameList.stream().filter(x -> x.length() > ObjectDescribeExt.DESCRIBE_API_NAME_LENGTH).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(beyondMaxLengthApiName)) {
            return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.APINAME_LENGTH_BEYOND_MAX_LIMIT, String.join(",", beyondMaxLengthApiName)));
        }
        Map<String, IObjectDescribe> existsDescribe = describeLogicService.findObjectsWithoutCopy(user.getTenantId(), changeOrderDescribeApiNameList);
        if (CollectionUtils.notEmpty(existsDescribe)) {
            return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.CHANGE_ORDER_OBJECT_API_NAME_EXIST, existsDescribe.values().stream().map(IObjectDescribe::getDisplayName).collect(Collectors.joining(","))));
        }
        return OpenChangeOrderResult.buildSuccess();
    }

    public boolean hasActiveUpdateApprovalFlowDefinitions(User user, String describeApiName) {
        return approvalFlowService.hasActiveApprovalFlowDefinitions(user, describeApiName, ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode());
    }

    @Override
    @Transactional
    public void closeChangeOrder(User user, String originalDescribeApiName) {
        String changeOrderDescribeApiName = findChangeOrderDescribeApiNameByOriginalApiName(user, originalDescribeApiName);
        if (Strings.isNullOrEmpty(changeOrderDescribeApiName)) {
            return;
        }
        MasterAndDetailDescribes masterAndDetailDescribes = getMasterAndDetailDescribes(user, originalDescribeApiName);
        updateOriginalDescribesWithClose(user, masterAndDetailDescribes);
        MasterAndDetailDescribes changeOrderDescribes = getMasterAndDetailDescribes(user, changeOrderDescribeApiName);
        deleteChangeOrderDescribesWithClose(user, changeOrderDescribes);
        deletedChangeOrderObjectFlag(user, originalDescribeApiName);
    }

    @Override
    @Transactional
    public void syncChangeOrderDescribe(User user, String describeApiName) {
        if (ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), describeApiName)) {
            syncChangeOrderDescribeWithOriginalDescribe(user, describeApiName);
        }
        // 相关对象和从对象
        List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribesWithoutCopyIfGray(user.getTenantId(), describeApiName);
        List<IObjectDescribe> describes = relatedDescribes.stream()
                // 过滤自关联的对象和不支持变更单的对象
                .filter(describe -> !Objects.equals(describe.getApiName(), describeApiName)
                        && ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), describe.getApiName()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(describes)) {
            return;
        }
        // 有引用字段引用了当前描述变更的对象的对象apiName
        Set<String> describeApiNameSet = Sets.newHashSet();
        describes.stream()
                .map(ObjectDescribeExt::of)
                .forEach(objectDescribeExt -> {
                    for (ObjectReferenceWrapper objectReferenceWrapper : objectDescribeExt.getLookupFieldDescribes()) {
                        if (!Objects.equals(describeApiName, objectReferenceWrapper.getTargetApiName())) {
                            continue;
                        }
                        List<Quote> quotes = objectDescribeExt.getQuoteFieldDescribes(objectReferenceWrapper.getApiName());
                        if (CollectionUtils.notEmpty(quotes)) {
                            describeApiNameSet.add(objectDescribeExt.getApiName());
                            break;
                        }
                    }
                });

        for (String objectApiName : describeApiNameSet) {
            syncChangeOrderDescribeWithOriginalDescribe(user, objectApiName);
        }
    }


    @Override
    @Transactional
    public void syncChangeOrderDescribeWithOriginalDescribe(User user, String originalDescribeApiName) {
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribeApiName)) {
            return;
        }
        // 查询描述的时候不走缓存
        Object describeSkipStatic = Optional.ofNullable(RequestContextManager.getContext())
                .map(requestContext -> {
                    Object attribute = requestContext.getAttribute(RequestContext.Attributes.DESCRIBE_SKIP_STATIC);
                    requestContext.setAttribute(RequestContext.Attributes.DESCRIBE_SKIP_STATIC, true);
                    return attribute;
                }).orElse(null);

        try {
            IObjectDescribe originalDescribe = describeLogicService.findObject(user.getTenantId(), originalDescribeApiName);
            if (!ObjectDescribeExt.of(originalDescribe).enabledChangeOrder()) {
                return;
            }
            MasterAndDetailDescribes masterAndDetailDescribes = ObjectDescribeExt.of(originalDescribe)
                    .getMasterDetailFieldDescribe()
                    .map(MasterDetailFieldDescribe::getTargetApiName)
                    .map(masterApiName -> describeLogicService.findObject(user.getTenantId(), masterApiName))
                    .map(masterDescribe -> MasterAndDetailDescribes.of(masterDescribe, Lists.newArrayList(originalDescribe), true))
                    .orElseGet(() -> MasterAndDetailDescribes.of(originalDescribe, Collections.emptyList(), true));

            MasterAndDetailDescribes changeOrderMasterAndDetailDescribes = masterAndDetailDescribes.convert2ChangeOrderDescribes();
            IObjectDescribe describe = changeOrderMasterAndDetailDescribes.getObjectDescribes().values().stream()
                    .filter(it -> Objects.equals(originalDescribeApiName, it.getOriginalDescribeApiName()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(describe)) {
                return;
            }
            IObjectDescribe oldDescribe = describeLogicService.findObject(user.getTenantId(), describe.getApiName());
            List<String> newChangeFields = ObjectDescribeExt.of(describe).stream()
                    .filter(it -> isChangeFieldName(user, it))
                    .map(IFieldDescribe::getApiName)
                    .collect(Collectors.toList());
            Collection<String> toDeletedFields = getToDeletedFields(oldDescribe, newChangeFields);

            for (String field : newChangeFields) {
                // changeField 有则合并，无则新增
                IFieldDescribe oldFieldDescribe = oldDescribe.getFieldDescribe(field);
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(field);
                if (Objects.isNull(oldFieldDescribe)) {
                    oldDescribe.addFieldDescribe(fieldDescribe);
                } else {
                    FieldDescribeExt.of(oldFieldDescribe).mergeFrom(fieldDescribe);
                }
                // originalField 有则合并，无则不处理
                String originalFieldName = FieldDescribeExt.getOriginalFieldNameByChangeField(field);
                ObjectDescribeExt.of(oldDescribe).getFieldDescribeSilently(originalFieldName)
                        .ifPresent(oldOriginalFieldDescribe -> {
                            IFieldDescribe originalField = FieldDescribeExt.of(fieldDescribe).generatedChangeOrderOriginalField(originalFieldName);
                            FieldDescribeExt.of(oldOriginalFieldDescribe).mergeFrom(originalField);
                        });
            }
            describeLogicService.update(user, oldDescribe);
            describeLogicService.deleteFieldDirect(user, oldDescribe, toDeletedFields);

            syncDescribeExtra(user, originalDescribe, oldDescribe, newChangeFields);
            // 同步多语言词条、只在词条不存在的时候同步
            syncFieldI18n(user, oldDescribe, originalDescribe, newChangeFields);
        } finally {
            // 还原 DESCRIBE_SKIP_STATIC 的设置
            Optional.ofNullable(RequestContextManager.getContext())
                    .ifPresent(requestContext -> requestContext.setAttribute(RequestContext.Attributes.DESCRIBE_SKIP_STATIC, describeSkipStatic));
        }
    }

    private void syncDescribeExtra(User user, IObjectDescribe originalDescribe, IObjectDescribe describe, List<String> changeFields) {
        Map<String, List<IObjectFieldExtra>> describeExtra = describeLogicService.findDescribeExtra(user, Lists.newArrayList(originalDescribe.getApiName()));
        if (CollectionUtils.empty(describeExtra)) {
            return;
        }
        Map<String, IObjectFieldExtra> fieldExtraMap = CollectionUtils.nullToEmpty(describeExtra.get(originalDescribe.getApiName())).stream()
                .collect(Collectors.toMap(IObjectFieldExtra::getFieldApiName, Function.identity()));

        List<IObjectFieldExtra> result = Lists.newArrayList();
        for (String field : changeFields) {
            String fieldName = FieldDescribeExt.getFieldNameByChangeField(field);
            IObjectFieldExtra fieldExtra = fieldExtraMap.get(fieldName);
            if (Objects.isNull(fieldExtra)) {
                continue;
            }
            Map map = ((ObjectFieldExtra) fieldExtra).getContainerDocument();
            ObjectFieldExtra extra = new ObjectFieldExtra(Maps.newHashMap(map));
            extra.setFieldApiName(field);
            result.add(extra);
            // originalField 有则 copy
            String originalFieldName = FieldDescribeExt.getOriginalFieldName(fieldName);
            ObjectDescribeExt.of(describe).getFieldDescribeSilently(originalFieldName)
                    .ifPresent(oldOriginalFieldDescribe -> {
                        ObjectFieldExtra originalFieldExtra = new ObjectFieldExtra(Maps.newHashMap(map));
                        originalFieldExtra.setFieldApiName(oldOriginalFieldDescribe.getApiName());
                        result.add(originalFieldExtra);
                    });
        }
        describeLogicService.upsertObjectFieldExtra(user, describe.getApiName(), result);
    }

    @Override
    public void syncFieldI18n(User user, IObjectDescribe describe, List<String> changeFieldNames) {
        if (!ObjectDescribeExt.of(describe).isChangeOrderObject()) {
            return;
        }
        IObjectDescribe originalDescribe = describeLogicService.findObject(user.getTenantId(), describe.getOriginalDescribeApiName());
        syncFieldI18n(user, describe, originalDescribe, changeFieldNames);
    }

    public void syncFieldI18n(User user, IObjectDescribe describe, IObjectDescribe originalDescribe, List<String> changeFieldNames) {
        I18nClient i18nClient = I18nClient.getInstance();
        List<Language> language = LanguageClientUtil.getLanguages(user.getTenantId());
        if (CollectionUtils.empty(language) || language.size() == 1) {
            return;
        }
        I18nKeyContainer i18nKeyContainer = getFieldI18nKeys(describe, originalDescribe, changeFieldNames);
        // 已经配置的词条不需要同步
        Set<String> changeObjectFieldKeys = i18nKeyContainer.getChangeObjectFieldKeys();
        Map<String, Localization> localizationMap = i18nClient.get(changeObjectFieldKeys, user.getTenantIdInt());
        changeObjectFieldKeys.removeIf(localizationMap::containsKey);
        if (CollectionUtils.empty(changeObjectFieldKeys)) {
            return;
        }

        Set<String> sourceObjectFieldKeys = i18nKeyContainer.getSourceObjectFieldKeys(changeObjectFieldKeys);
        Map<String, Localization> originalLocalizationMap = i18nClient.get(sourceObjectFieldKeys, user.getTenantIdInt());
        List<Localization> list = Lists.newArrayList();
        // 更新 change 的多语言词条
        originalLocalizationMap.forEach((key, value) -> {
            i18nKeyContainer.getBySourceObjectFieldKey(key).ifPresent(i18nKey -> {
                list.addAll(i18nKey.buildLocalizations(value));
            });
        });
        i18nClient.save4Translate(user.getTenantIdInt(), list, false);
    }

    /**
     * @param describe
     * @param originalDescribe
     * @param changeFieldNames
     * @return key changeObjectI18nKey value originalObjectI18nKey
     */
    private I18nKeyContainer getFieldI18nKeys(IObjectDescribe describe, IObjectDescribe originalDescribe, List<String> changeFieldNames) {
        I18nKeyContainer i18nKeyContainer = new I18nKeyContainer();
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        ObjectDescribeExt originalDescribeExt = ObjectDescribeExt.of(originalDescribe);
        for (String changeFieldName : changeFieldNames) {
            IFieldDescribe fieldDescribe = objectDescribeExt.getFieldDescribe(changeFieldName);
            if (Objects.isNull(fieldDescribe)) {
                continue;
            }
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            String originalFieldNameByChangeField = fieldDescribeExt.isRecordType() ? fieldDescribe.getApiName() :
                    FieldDescribeExt.getFieldNameByChangeField(changeFieldName);
            IFieldDescribe originalField = originalDescribeExt.getFieldDescribe(originalFieldNameByChangeField);
            if (Objects.isNull(originalField)) {
                continue;
            }
            Optional<IFieldDescribe> fieldOptional = objectDescribeExt.getFieldDescribeSilently(
                    FieldDescribeExt.getOriginalFieldNameByChangeField(changeFieldName));
            // 字段 label
            String changeFieldLabelKey = GetI18nKeyUtil.getFieldLabelKey(describe.getApiName(), fieldDescribe.getApiName());
            String sourceFieldLabelKey = GetI18nKeyUtil.getFieldLabelKey(originalDescribe.getApiName(), originalField.getApiName());
            String originalFieldLabelKey = fieldOptional.map(field ->
                    GetI18nKeyUtil.getFieldLabelKey(describe.getApiName(), field.getApiName())).orElse("");
            i18nKeyContainer.add(sourceFieldLabelKey, originalFieldLabelKey, changeFieldLabelKey, I18nKey.TYPE_FIELD);
            // 查找关联字段的 target_related_list_label
            if (fieldDescribeExt.isRefObjectField()) {
                String changeFieldReferenceLabelKey = GetI18nKeyUtil.getFieldReferenceLabelKey(describe.getApiName(), fieldDescribe.getApiName());
                String originalFieldReferenceLabelKey = fieldOptional.map(field ->
                        GetI18nKeyUtil.getFieldReferenceLabelKey(describe.getApiName(), field.getApiName())).orElse("");
                String sourceFieldReferenceLabelKey = GetI18nKeyUtil.getFieldReferenceLabelKey(originalDescribe.getApiName(), originalField.getApiName());
                i18nKeyContainer.add(sourceFieldReferenceLabelKey, originalFieldReferenceLabelKey, changeFieldReferenceLabelKey, I18nKey.TYPE_TARGET_RELATED_LIST_LABEL);
            }
            // 单选的选项
            if (JsonFieldHandler.optionJsonField.contains(fieldDescribe.getType()) && !fieldDescribeExt.isGeneralOptions()) {
                List<Map> options = (List<Map>) fieldDescribe.get(SelectOneFieldDescribe.OPTIONS);
                for (Map optionMap : CollectionUtils.nullToEmpty(options)) {
                    ISelectOption selectOption = new SelectOption(optionMap);
                    // 业务类型 需要取 api_name
                    String apiName = fieldDescribeExt.isRecordType() ? selectOption.get(IRecordTypeOption.API_NAME, String.class) : selectOption.getValue();
                    String changeOptionNameKey = GetI18nKeyUtil.getOptionNameKey(describe.getApiName(), fieldDescribeExt.getApiName(), apiName);
                    String originalOptionNameKey = fieldOptional.map(field ->
                            GetI18nKeyUtil.getOptionNameKey(describe.getApiName(), field.getApiName(), apiName)).orElse("");
                    String sourceOptionNameKey = GetI18nKeyUtil.getOptionNameKey(originalDescribe.getApiName(), originalField.getApiName(), apiName);
                    i18nKeyContainer.add(sourceOptionNameKey, originalOptionNameKey, changeOptionNameKey, I18nKey.TYPE_OPTION);
                }
            }
        }
        return i18nKeyContainer;
    }

    private boolean isChangeFieldName(User user, IFieldDescribe fieldDescribe) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SYNC_CHANGE_ORDER_MASTER_DETAIL_FIELD_GRAY, user.getTenantId())
                && FieldDescribeExt.of(fieldDescribe).isMasterDetailField()) {
            return true;
        }
        String fieldName = fieldDescribe.getApiName();
        return FieldDescribeExt.isChangeFieldName(fieldName) || FieldDescribeExt.RECORD_TYPE.equals(fieldName);
    }

    private Collection<String> getToDeletedFields(IObjectDescribe oldDescribe, List<String> newChangeFields) {
        List<String> oldChangeFields = ObjectDescribeExt.of(oldDescribe).stream()
                .map(IFieldDescribe::getApiName)
                .filter(FieldDescribeExt::isChangeFieldName)
                .collect(Collectors.toList());
        Collection<String> toDeletedFields = org.apache.commons.collections4.CollectionUtils.subtract(oldChangeFields, newChangeFields);
        List<String> result = Lists.newArrayList(toDeletedFields);
        for (String field : toDeletedFields) {
            String originalFieldName = FieldDescribeExt.getOriginalFieldNameByChangeField(field);
            if (oldDescribe.containsField(originalFieldName)) {
                result.add(originalFieldName);
            }
        }
        return result;
    }

    @Override
    public List<IObjectData> findChangeOrderOriginalData(User user, String apiName, Collection<String> dataIds) {
        List<DataExtra> dataExtras = dataExtraMapper.findByFieldApiNameAndDataId(user.getTenantId(), apiName, dataIds);
        if (CollectionUtils.empty(dataExtras)) {
            return Lists.newArrayList();
        }
        return dataExtras.stream()
                .filter(it -> Objects.equals(CHANGE_ORDER_SNAPSHOT, it.getFieldApiName()))
                .map(this::convertToObjectData)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveChangeOrderOriginalData(User user, String describeApiName, Collection<IObjectData> dataList, boolean isUpdate) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<String> dataIds = Lists.newArrayList();
        List<DataExtra> dataExtras = Lists.newArrayList();
        for (IObjectData objectData : dataList) {
            DataExtra dataExtra = convertToDataExtra(user, describeApiName, objectData);
            dataExtras.add(dataExtra);
            dataIds.add(objectData.getId());
        }
        if (isUpdate) {
            dataExtraMapper.setTenantId(user.getTenantId())
                    .deleteByFieldApiNameAndDataId(user.getTenantId(), describeApiName, Lists.newArrayList(CHANGE_ORDER_SNAPSHOT), dataIds);
        }
        dataExtraMapper.setTenantId(user.getTenantId()).upsertByIdDescribeAndField(dataExtras);
    }

    @Override
    public List<IObjectData> findAndMergeObjectDataWithOriginalData(User user, IObjectDescribe describe, List<IObjectData> dataList) {
        String originalDescribeApiName = describe.getOriginalDescribeApiName();
        if (Strings.isNullOrEmpty(originalDescribeApiName)) {
            return dataList;
        }
        ObjectDescribeExt originalDescribe = ObjectDescribeExt.of(describeLogicService.findObject(user.getTenantId(), originalDescribeApiName));

        String describeApiName = originalDescribe.getMasterDetailFieldDescribe()
                .map(MasterDetailFieldDescribe::getTargetApiName)
                .orElse(originalDescribeApiName);
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), describeApiName)) {
            return dataList;
        }
        Map<String, IObjectData> objectDataMap = dataList.stream()
                .collect(Collectors.toMap(IObjectData::getId, Function.identity()));

        List<IObjectData> changeOrderSnapshot = findChangeOrderSnapshot(user, describe, objectDataMap.keySet());

        OriginalAndChangeDescribes describes = OriginalAndChangeDescribes.builder()
                .originalDescribes(MasterAndDetailDescribes.of(originalDescribe, Collections.emptyList()))
                .changeOrderDescribes(MasterAndDetailDescribes.of(describe, Collections.emptyList()))
                .build();
        Map<String, String> changeFieldMapping = HashBiMap.create(describes.getChangeFieldNameMapping(originalDescribeApiName, describe.getApiName())).inverse();

        for (IObjectData objectData : changeOrderSnapshot) {
            IObjectData data = objectDataMap.get(objectData.getId());
            if (Objects.isNull(data)) {
                continue;
            }
            Map<String, Object> diff = ObjectDataExt.of(data).diff(objectData, describe);
            diff.forEach((fieldName, value) -> {
                String apiName = changeFieldMapping.get(fieldName);
                if (Strings.isNullOrEmpty(apiName)) {
                    return;
                }
                String originalFieldName = FieldDescribeExt.getOriginalFieldName(apiName);
                data.set(originalFieldName, value);
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldName);
                // 单选其他字段需要补充__o
                if (FieldDescribeExt.isSelectField(fieldDescribe.getType()) && ObjectDataExt.hasOtherValue(value)) {
                    String otherFieldName = FieldDescribeExt.getSelectOther(fieldName);
                    String originalFieldNameOtherFieldName = FieldDescribeExt.getSelectOther(originalFieldName);
                    data.set(originalFieldNameOtherFieldName, diff.get(otherFieldName));
                }
                // 富文本字段需要补充__o
                if (RichTextExt.isProcessableRichText(fieldDescribe)) {
                    ObjectDataExt.of(data).extractAbstractOfRichText(Sets.newHashSet(originalFieldName));
                }
            });
        }
        return dataList;
    }

    @Override
    public List<IObjectData> findChangeOrderSnapshot(User user, IObjectDescribe describe, Collection<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return Collections.emptyList();
        }
        List<DataExtra> dataExtras = dataExtraMapper.setTenantId(user.getTenantId()).findByFieldApiNameAndDataId(user.getTenantId(),
                describe.getApiName(), Lists.newArrayList(ids));

        List<IObjectData> result = Lists.newArrayList();
        for (DataExtra dataExtra : dataExtras) {
            if (!CHANGE_ORDER_SNAPSHOT.equals(dataExtra.getFieldApiName())) {
                continue;
            }
            IObjectData objectData = new ObjectData();
            objectData.fromJsonString(dataExtra.getExtraInfo());
            objectData.setTenantId(user.getTenantId());
            objectData.setDescribeApiName(describe.getApiName());
            objectData.setId(dataExtra.getDataId());
            result.add(objectData);
        }
        return result;
    }

    private ILayout handleLayoutWithChangeOrder(IObjectDescribe describe, IObjectDescribe originalDescribe, ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (!layoutExt.isDetailLayout()) {
            return layout;
        }
        OriginalAndChangeDescribes describes = OriginalAndChangeDescribes.builder()
                .originalDescribes(MasterAndDetailDescribes.of(originalDescribe, Collections.emptyList()))
                .changeOrderDescribes(MasterAndDetailDescribes.of(describe, Collections.emptyList()))
                .build();
        // 替换布局中的字段
        Map<String, IFieldDescribe> changeFieldMapping = describes.getChangeFieldMapping(originalDescribe.getApiName(), describe.getApiName());
        for (FormComponentExt formComponent : layoutExt.getFormComponents()) {
            Set<String> toRemoveFields = replaceFieldNameAndReturnToRemoveFields(changeFieldMapping, formComponent.getFormFields());
            formComponent.removeFields(toRemoveFields);
        }
        layoutExt.getFormTables().forEach(formTable -> {
            Set<String> toRemoveFields = replaceFieldNameAndReturnToRemoveFields(changeFieldMapping, formTable.getFields());
            formTable.removeFields(toRemoveFields);
        });
        // 关键信息组件
        for (SummaryKeyComponentInfo summaryKeyComponentInfo : layoutExt.getSummaryKeyComponentInfos()) {
            Set<String> toRemoveFields = replaceFieldNameAndReturnToRemoveFields(changeFieldMapping, summaryKeyComponentInfo.getFieldSections());
            summaryKeyComponentInfo.removeFields(toRemoveFields);
        }
        getTopInfoComponentSilently(layoutExt).ifPresent(topInfo -> {
            FormComponentExt formComponentExt = FormComponentExt.of(topInfo);
            Set<String> toRemoveFields = replaceFieldNameAndReturnToRemoveFields(changeFieldMapping, formComponentExt.getFormFields());
            formComponentExt.removeFields(toRemoveFields);
            //新布局的摘要信息组件使用过滤之后的摘要字段
            if (layoutExt.isNewLayout()) {
                layoutExt.getNewTopInfoComponent().ifPresent(x ->
                        x.setFieldSections(LayoutComponents.getTopInfoFieldSection(topInfo)));
            }
        });
        layout.setRefObjectApiName(describe.getApiName());
        // 补充变更单分组
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getFieldByApiNames(ChangeOrderConfig.getChangeOrderPackageFields());
        layoutExt.addChangeOrderSection(fieldDescribes, true);
        // 启用了移动端布局，也需要修改移动端布局的字段
        if (layoutExt.isEnableMobileLayout()) {
            LayoutExt mobileLayout = LayoutExt.of(layoutExt.getMobileLayout());
            handleLayoutWithChangeOrder(describe, originalDescribe, mobileLayout);
        }
        return layout;
    }

    private Optional<SimpleComponent> getTopInfoComponentSilently(LayoutExt layoutExt) {
        if (layoutExt.isNewLayout()) {
            ITopInfoComponent topInfo = layoutExt.getNewTopInfoComponent().orElse(null);
            return Optional.ofNullable(LayoutComponents.convertToSimpleComponent(topInfo));
        }
        return layoutExt.getTopInfoComponentSilently();
    }

    @Override
    public ILayout findLayoutWithChangeOrder(User user, String describeApiName, String layoutType, Function<String, ILayout> function) {
        if (!ILayout.DETAIL_LAYOUT_TYPE.equals(layoutType) && !LayoutTypes.EDIT.equals(layoutType)) {
            return function.apply(describeApiName);
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        String originalDescribeApiName = describe.getOriginalDescribeApiName();
        if (Strings.isNullOrEmpty(originalDescribeApiName)) {
            return function.apply(describeApiName);
        }
        ObjectDescribeExt originalDescribe = ObjectDescribeExt.of(describeLogicService.findObjectWithoutCopy(user.getTenantId(), originalDescribeApiName));
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribeApiName)) {
            return function.apply(describeApiName);
        }
        ILayout layout = function.apply(originalDescribeApiName);
        return handleLayoutWithChangeOrder(describe, originalDescribe, layout);
    }

    @Override
    public Map<String, Layout> findDetailLayoutsWithChangeOrder(User user, IObjectDescribe describe,
                                                                Function<String, Map<String, Layout>> function) {
        String describeApiName = describe.getApiName();
        String originalDescribeApiName = describe.getOriginalDescribeApiName();
        if (Strings.isNullOrEmpty(originalDescribeApiName)) {
            return function.apply(describeApiName);
        }
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), originalDescribeApiName)) {
            return function.apply(describeApiName);
        }
        ObjectDescribeExt originalDescribe = ObjectDescribeExt.of(describeLogicService.findObjectWithoutCopy(user.getTenantId(), originalDescribeApiName));
        Map<String, Layout> layoutMap = function.apply(originalDescribeApiName);
        layoutMap.values().forEach(layout -> handleLayoutWithChangeOrder(describe, originalDescribe, layout));
        return layoutMap;
    }


    @Override
    public boolean isOpenChangeOrder(User user, String describeApiName) {
        return !Strings.isNullOrEmpty(findChangeOrderDescribeApiNameByOriginalApiName(user, describeApiName));
    }

    @Override
    public void addFieldForOriginalDescribe(User user, IObjectDescribe originalDescribe) {
        if (!licenseService.isSupportChangeOrder(user.getTenantId()) || !ObjectDescribeExt.of(originalDescribe).isSlaveObject()) {
            return;
        }
        MasterAndDetailDescribes masterAndDetailDescribes = MasterAndDetailDescribes.of(originalDescribe, Lists.newArrayList());
        // 校验是否支持开启变更单对象
        OpenChangeOrderResult openChangeOrderResult = canOpenChangeOrder(user, masterAndDetailDescribes);
        if (!openChangeOrderResult.success()) {
            throw new ValidateException(I18nMessage.of("CHANGE_ORDER_OPEN_ERROR", openChangeOrderResult.getMessage()));
        }
        originalDescribe.addFieldDescribeList(ChangeOrderConfig.getOriginalDescribeFields(originalDescribe.getApiName()));
    }

    @Override
    public String findOriginalApiNameByChangeOrder(User user, String changeOrderApiName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), changeOrderApiName);
        return describe.getOriginalDescribeApiName();
//        return findChangeOrderDescribeApiNameByOriginalApiName(user, changeOrderApiName);
    }

    @Override
    public Map<String, String> batchFindOriginalApiNameByChangOrderList(User user, List<String> changeOrderApiNameList) {
        List<String> configKeys = changeOrderApiNameList.stream().map(x -> getChangeOrderLockKey(user.getTenantId(), x)).collect(Collectors.toList());
        return configService.queryTenantConfigs(user, configKeys);
    }


    @Override
    public boolean canStartChangeOrder(User user, IObjectDescribe describe, IObjectData objectData) {
        return canStartChangeOrder(user, describe, objectData, false);
    }

    @Override
    public boolean canStartChangeOrder(User user, IObjectDescribe describe, IObjectData objectData, boolean allowUnderReview) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.RE_CHANGE_ORDER_GRAY, user.getTenantId())) {
            return false;
        }
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        if (!objectDescribeExt.isChangeOrderObject()) {
            return false;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);

        // 检查生效状态
        ChangeOrderEffectiveStatus effectiveStatus = objectDataExt.getChangeOrderEffectiveStatus();
        if (!isChangeOrderDataEditable(allowUnderReview, effectiveStatus, objectDataExt)) {
            return false;
        }

        SearchTemplateQuery searchTemplateQuery = buildSearchQuery(objectDataExt);
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQueryIgnoreAll(user, describe.getApiName(), searchTemplateQuery);
        List<IObjectData> dataList = queryResult.getData();
        if (CollectionUtils.empty(dataList)) {
            return false;
        }
        IObjectData data = dataList.get(0);
        if (!Objects.equals(data.getId(), objectData.getId())) {
            return false;
        }

        ObjectDataExt dataExt = ObjectDataExt.of(data);
        ChangeOrderEffectiveStatus dataEffectiveStatus = dataExt.getChangeOrderEffectiveStatus();
        return isChangeOrderDataEditable(allowUnderReview, dataEffectiveStatus, dataExt);
    }

    private boolean isChangeOrderDataEditable(boolean allowUnderReview, ChangeOrderEffectiveStatus dataEffectiveStatus, ObjectDataExt dataExt) {
        if (ChangeOrderEffectiveStatus.REJECTED == dataEffectiveStatus) {
            return true;
        }
        return allowUnderReview && ChangeOrderEffectiveStatus.INEFFECTIVE == dataEffectiveStatus
                && ObjectLifeStatus.UNDER_REVIEW == dataExt.getLifeStatus();
    }

    private SearchTemplateQuery buildSearchQuery(ObjectDataExt objectDataExt) {
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        queryExt.setOffset(0);
        queryExt.setLimit(1);
        queryExt.resetOrderBy(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, false)));

        String originalDataId = objectDataExt.getOriginalDataId();
        queryExt.addFilter(Operator.EQ, ObjectDataExt.ORIGINAL_DATA, originalDataId);
        return queryExt.toSearchTemplateQuery();
    }

    private Set<String> replaceFieldNameAndReturnToRemoveFields(Map<String, IFieldDescribe> changeFieldMapping, List<IFormField> fields) {
        Set<String> toRemoveFields = Sets.newHashSet();
        for (IFormField field : fields) {
            String fieldName = field.getFieldName();
            // 判断是否为查找关联对象下的字段
            if (StringUtils.contains(fieldName, ".")) {
                String[] parts = StringUtils.split(fieldName, ".");
                Tuple<String, String> tuple = Tuple.of(StringUtils.substringBeforeLast(parts[0], "__r"), parts[1]);
                IFieldDescribe fieldDescribe = changeFieldMapping.get(tuple.getKey());
                // 这里先不考虑主对象下的字段，后续如果支持需要修改
                if (FieldDescribeExt.of(fieldDescribe).isRefObjectField()) {
                    String name = StringUtils.substringAfter(fieldName, ".");
                    field.setFieldName(fieldDescribe.getApiName() + "__r" + "." + name);
                    continue;
                }
            }
            // 本对象下的字段
            IFieldDescribe fieldDescribe = changeFieldMapping.get(fieldName);
            if (Objects.nonNull(fieldDescribe)) {
                field.setFieldName(fieldDescribe.getApiName());
                field.setRenderType(fieldDescribe.getType());
            } else {
                toRemoveFields.add(fieldName);
            }
        }
        return toRemoveFields;
    }

    private DataExtra convertToDataExtra(User user, String describeApiName, IObjectData objectData) {
        return DataExtra.builder()
                .id(IdGenerator.get())
                .dataId(objectData.getId())
                .describeApiName(describeApiName)
                .tenantId(user.getTenantId())
                .fieldApiName(CHANGE_ORDER_SNAPSHOT)
                // 需要保留data中的 null 值
                .extraInfo(JacksonUtils.toJson(ObjectDataExt.of(objectData).toMap()))
                .build();
    }

    private IObjectData convertToObjectData(DataExtra dataExtra) {
        IObjectData objectData = new ObjectData();
        objectData.fromJsonString(dataExtra.getExtraInfo());
        objectData.setId(dataExtra.getDataId());
        objectData.setTenantId(dataExtra.getTenantId());
        objectData.setDescribeApiName(dataExtra.getDescribeApiName());
        return objectData;
    }

    private void updateOriginalDescribesWithClose(User user, MasterAndDetailDescribes masterAndDetailDescribes) {
        IObjectDescribe objectDescribe = masterAndDetailDescribes.getObjectDescribe();
        List<String> fieldNames = CollectionUtils.nullToEmpty(ChangeOrderConfig.getOriginalDescribeFields(null)).stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        // 删除开启变更单后在原单上新加的字段
        describeLogicService.deleteFieldDirect(user, objectDescribe, fieldNames);
        for (IObjectDescribe describe : masterAndDetailDescribes.getDetailDescribes().values()) {
            describeLogicService.deleteFieldDirect(user, describe, fieldNames);
        }
    }

    private void deleteChangeOrderDescribesWithClose(User user, MasterAndDetailDescribes changeOrderMasterAndDetailDescribes) {
        IObjectDescribe changeOrderDescribes = changeOrderMasterAndDetailDescribes.getObjectDescribe();
        for (IObjectDescribe objectDescribe : changeOrderMasterAndDetailDescribes.getDetailDescribes().values()) {
            describeLogicService.deleteDescribeDirect(user, objectDescribe.getApiName());
        }
        describeLogicService.deleteDescribeDirect(user, changeOrderDescribes.getApiName());
    }

    private MasterAndDetailDescribes getMasterAndDetailDescribes(User user, String describeApiName) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        List<IObjectDescribe> details = describeLogicService.findDetailDescribesCreateWithMaster(user.getTenantId(), describeApiName);
        return MasterAndDetailDescribes.of(describe, details);
    }

    private MasterAndDetailDescribes getMasterAndAllDetailDescribes(User user, String describeApiName) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        List<IObjectDescribe> details = describeLogicService.findDetailDescribes(user.getTenantId(), describeApiName);
        return MasterAndDetailDescribes.of(describe, details);
    }

    private static class I18nKeyContainer {
        private final List<I18nKey> i18nKeys = Lists.newArrayList();

        private final Map<String, I18nKey> changeObjectFieldKeyMap = Maps.newHashMap();
        private final Map<String, I18nKey> sourceObjectFieldKeyMap = Maps.newHashMap();

        public void add(String sourceKey, String originalKey, String changeKey, String type) {
            I18nKey i18nKey = new I18nKey(sourceKey, originalKey, changeKey, type);
            i18nKeys.add(i18nKey);
            sourceObjectFieldKeyMap.putIfAbsent(sourceKey, i18nKey);
            changeObjectFieldKeyMap.putIfAbsent(changeKey, i18nKey);
            if (Strings.isNullOrEmpty(originalKey)) {
                changeObjectFieldKeyMap.putIfAbsent(originalKey, i18nKey);
            }
        }

        public boolean isEmpty() {
            return i18nKeys.isEmpty();
        }

        public Set<String> getChangeObjectFieldKeys() {
            return Sets.newHashSet(changeObjectFieldKeyMap.keySet());
        }

        public Set<String> getSourceObjectFieldKeys(Collection<String> changeObjectFieldKeys) {
            if (CollectionUtils.empty(changeObjectFieldKeys)) {
                return Sets.newHashSet();
            }
            return changeObjectFieldKeys.stream()
                    .map(changeObjectFieldKeyMap::get)
                    .filter(Objects::nonNull)
                    .map(I18nKey::getSourceKey)
                    .collect(Collectors.toSet());
        }

        public Optional<I18nKey> getBySourceObjectFieldKey(String sourceObjectFieldKey) {
            return Optional.ofNullable(sourceObjectFieldKeyMap.get(sourceObjectFieldKey));
        }
    }

    @Getter
    private static class I18nKey {
        public static final String TYPE_TARGET_RELATED_LIST_LABEL = "target_related_list_label";
        public static final String TYPE_FIELD = "field";
        public static final String TYPE_OPTION = "option";

        private String sourceKey;
        private String originalKey;
        private String changeKey;
        private String type;

        public I18nKey(String sourceKey, String originalKey, String changeKey, String type) {
            this.sourceKey = sourceKey;
            this.originalKey = originalKey;
            this.changeKey = changeKey;
            this.type = type;
        }

        public Collection<? extends Localization> buildLocalizations(Localization value) {
            Set<Localization> result = Sets.newHashSet();
            result.add(buildLocalization(value, changeKey, false));
            if (!Strings.isNullOrEmpty(originalKey)) {
                result.add(buildLocalization(value, originalKey, true));
            }
            return result;
        }

        private Localization buildLocalization(Localization localization, String key, boolean isOriginalKey) {
            Localization result = Localization.builder()
                    .key(key)
                    .tags(localization.getTags())
                    .tenantId(localization.getTenantId())
                    .build();
            I18nClient i18nClient = I18nClient.getInstance();
            for (Language language : i18nClient.getLanguage(localization.getTenantId())) {
                String value = localization.get(language.getCode(), false);
                // 查找关联字段 target_related_list_label 需要添加「变更对象」后缀
                if (TYPE_TARGET_RELATED_LIST_LABEL.equals(type)) {
                    value = value + I18NExt.text(I18NKey.CHANGE_OBJECT);
                } else if (isOriginalKey && TYPE_FIELD.equals(type)) {
                    // 记录原值字段名称需要 「原」前缀
                    value = I18NExt.text(I18NKey.ORIGINAL) + value;
                }
                i18nClient.build(result, language.getCode(), value);
            }
            return result;
        }
    }
}
