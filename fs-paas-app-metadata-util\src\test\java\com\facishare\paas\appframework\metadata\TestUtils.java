package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.google.common.collect.Maps;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;
import static org.mockito.Mockito.lenient;

/**
 * 测试工具类 - 提供统一的Mock对象创建和测试数据构造
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 创建标准化的Mock对象
 * - 提供通用的测试数据构造方法
 * - 统一Mock对象的行为配置
 * 
 * 使用场景：
 * - 所有JUnit5测试类的Mock对象创建
 * - 测试数据的标准化构造
 * - 减少重复的Mock配置代码
 */
public class TestUtils {
    
    // 测试常量
    public static final String TEST_TENANT_ID = "test-tenant-123";
    public static final String TEST_USER_ID = "test-user-456";
    public static final String TEST_API_NAME = "TestObj";
    public static final String TEST_FIELD_NAME = "test_field";
    public static final String TEST_LABEL = "测试标签";
    
    /**
     * 创建Mock的IObjectData对象
     * 
     * @param data 数据Map
     * @return Mock的IObjectData对象
     */
    public static IObjectData createMockObjectData(Map<String, Object> data) {
        IObjectData mockObjectData = Mockito.mock(IObjectData.class);

        if (data != null) {
            // 配置get方法
            data.forEach((key, value) -> {
                when(mockObjectData.get(key)).thenReturn(value);
            });

            // IObjectData没有containsKey方法，这里不需要Mock
        }

        return mockObjectData;
    }
    
    /**
     * 创建Mock的IFieldDescribe对象
     * 
     * @param apiName 字段API名称
     * @param type 字段类型
     * @return Mock的IFieldDescribe对象
     */
    public static IFieldDescribe createMockFieldDescribe(String apiName, String type) {
        IFieldDescribe mockField = Mockito.mock(IFieldDescribe.class);
        when(mockField.getApiName()).thenReturn(apiName);
        when(mockField.getType()).thenReturn(type);
        when(mockField.getLabel()).thenReturn(TEST_LABEL);
        when(mockField.isRequired()).thenReturn(false);
        when(mockField.isUnique()).thenReturn(false);
        return mockField;
    }
    
    /**
     * 创建Mock的IObjectDescribe对象
     * 
     * @param apiName 对象API名称
     * @return Mock的IObjectDescribe对象
     */
    public static IObjectDescribe createMockObjectDescribe(String apiName) {
        IObjectDescribe mockDescribe = Mockito.mock(IObjectDescribe.class);
        when(mockDescribe.getApiName()).thenReturn(apiName);
        when(mockDescribe.getDisplayName()).thenReturn(TEST_LABEL);
        return mockDescribe;
    }
    
    /**
     * 创建Mock的User对象
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return Mock的User对象
     */
    public static User createMockUser(String tenantId, String userId) {
        User mockUser = Mockito.mock(User.class);
        // 使用lenient()模式避免UnnecessaryStubbing异常
        lenient().when(mockUser.getTenantId()).thenReturn(tenantId);
        lenient().when(mockUser.getUserId()).thenReturn(userId);
        lenient().when(mockUser.isOutUser()).thenReturn(false);
        return mockUser;
    }
    
    /**
     * 创建默认的Mock User对象
     * 
     * @return Mock的User对象
     */
    public static User createDefaultMockUser() {
        return createMockUser(TEST_TENANT_ID, TEST_USER_ID);
    }
    
    /**
     * 创建Mock的IUdefButton对象
     * 
     * @param apiName 按钮API名称
     * @return Mock的IUdefButton对象
     */
    public static IUdefButton createMockButton(String apiName) {
        IUdefButton mockButton = Mockito.mock(IUdefButton.class);
        when(mockButton.getApiName()).thenReturn(apiName);
        when(mockButton.getLabel()).thenReturn(TEST_LABEL);
        when(mockButton.isDeleted()).thenReturn(false);
        return mockButton;
    }
    
    /**
     * 创建Mock的ISearchTemplate对象
     * 
     * @param apiName 搜索模板API名称
     * @return Mock的ISearchTemplate对象
     */
    public static ISearchTemplate createMockSearchTemplate(String apiName) {
        ISearchTemplate mockTemplate = Mockito.mock(ISearchTemplate.class);
        when(mockTemplate.getApiName()).thenReturn(apiName);
        when(mockTemplate.getLabel()).thenReturn(TEST_LABEL);
        return mockTemplate;
    }
    
    /**
     * 创建测试用的数据Map
     * 
     * @return 包含测试数据的Map
     */
    public static Map<String, Object> createTestDataMap() {
        Map<String, Object> data = Maps.newHashMap();
        data.put("id", "test-id-123");
        data.put("name", "测试名称");
        data.put("description", "测试描述");
        data.put("_id", "test-_id-456");
        return data;
    }
    
    /**
     * 创建空的数据Map
     * 
     * @return 空的HashMap
     */
    public static Map<String, Object> createEmptyDataMap() {
        return new HashMap<>();
    }
}
