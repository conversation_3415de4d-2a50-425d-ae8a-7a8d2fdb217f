package com.facishare.paas.appframework.metadata.util;

import com.google.common.collect.Maps;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for VelocityUtil
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("VelocityUtil 单元测试")
class VelocityUtilJUnit5Test {

    // ==================== replacePlaceholder 方法测试 ====================

    @Test
    @DisplayName("替换占位符 - 正常情况")
    void testReplacePlaceholder_Success() {
        // Arrange
        String template = "Hello ${name}, welcome to ${company}!";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("name", "John");
        contextData.put("company", "FaceShare");

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Hello John, welcome to FaceShare!", result);
    }

    @Test
    @DisplayName("替换占位符 - 空模板")
    void testReplacePlaceholder_EmptyTemplate() {
        // Arrange
        String template = "";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("name", "John");

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    @DisplayName("替换占位符 - null模板")
    void testReplacePlaceholder_NullTemplate() {
        // Arrange
        String template = null;
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("name", "John");

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    @DisplayName("替换占位符 - 空白模板")
    void testReplacePlaceholder_BlankTemplate() {
        // Arrange
        String template = "   ";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("name", "John");

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    @DisplayName("替换占位符 - 无占位符模板")
    void testReplacePlaceholder_NoPlaceholders() {
        // Arrange
        String template = "Hello World!";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("name", "John");

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Hello World!", result);
    }

    @Test
    @DisplayName("替换占位符 - 不存在的占位符")
    void testReplacePlaceholder_NonExistentPlaceholders() {
        // Arrange
        String template = "Hello ${name}, your age is ${age}";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("name", "John");
        // 注意：age不存在于contextData中

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        // 由于使用了$!{}语法，不存在的变量会显示为空字符串
        assertEquals("Hello John, your age is ", result);
    }

    @Test
    @DisplayName("替换占位符 - 空上下文数据")
    void testReplacePlaceholder_EmptyContextData() {
        // Arrange
        String template = "Hello ${name}!";
        Map<String, Object> contextData = Maps.newHashMap();

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Hello !", result);
    }

    @Test
    @DisplayName("替换占位符 - null上下文数据")
    void testReplacePlaceholder_NullContextData() {
        // Arrange
        String template = "Hello ${name}!";
        Map<String, Object> contextData = null;

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert - 实际上不会抛出异常，而是返回处理结果
        assertNotNull(result);
        assertEquals("Hello !", result); // 不存在的变量会显示为空字符串
    }

    @Test
    @DisplayName("替换占位符 - 复杂模板")
    void testReplacePlaceholder_ComplexTemplate() {
        // Arrange
        String template = "Dear ${user.name}, your order ${order.id} for ${order.amount} has been ${status}. " +
                "Please contact ${support.email} if you have any questions.";
        Map<String, Object> contextData = Maps.newHashMap();
        
        Map<String, Object> user = Maps.newHashMap();
        user.put("name", "Alice");
        contextData.put("user", user);
        
        Map<String, Object> order = Maps.newHashMap();
        order.put("id", "ORD-12345");
        order.put("amount", "$99.99");
        contextData.put("order", order);
        
        contextData.put("status", "confirmed");
        
        Map<String, Object> support = Maps.newHashMap();
        support.put("email", "<EMAIL>");
        contextData.put("support", support);

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Dear Alice, your order ORD-12345 for $99.99 has been confirmed. " +
                "<NAME_EMAIL> if you have any questions.", result);
    }

    @Test
    @DisplayName("替换占位符 - 数字类型数据")
    void testReplacePlaceholder_NumericData() {
        // Arrange
        String template = "Price: ${price}, Quantity: ${quantity}, Total: ${total}";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("price", 19.99);
        contextData.put("quantity", 3);
        contextData.put("total", 59.97);

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Price: 19.99, Quantity: 3, Total: 59.97", result);
    }

    @Test
    @DisplayName("替换占位符 - 布尔类型数据")
    void testReplacePlaceholder_BooleanData() {
        // Arrange
        String template = "Is active: ${isActive}, Is premium: ${isPremium}";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("isActive", true);
        contextData.put("isPremium", false);

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Is active: true, Is premium: false", result);
    }

    @Test
    @DisplayName("替换占位符 - 特殊字符")
    void testReplacePlaceholder_SpecialCharacters() {
        // Arrange
        String template = "Message: ${message}";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("message", "Hello \"World\"! & <Welcome> to 'FaceShare'");

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Message: Hello \"World\"! & <Welcome> to 'FaceShare'", result);
    }

    @Test
    @DisplayName("替换占位符 - 国际化字符内容")
    void testReplacePlaceholder_InternationalCharacters() {
        // Arrange - Velocity变量名必须是ASCII字符，但内容可以是国际化字符
        String template = "欢迎 ${userName}！こんにちは ${userNameJp}！مرحبا ${userNameAr}！";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("userName", "张三");
        contextData.put("userNameJp", "田中");
        contextData.put("userNameAr", "أحمد");

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("欢迎 张三！こんにちは 田中！مرحبا أحمد！", result);
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 极长模板")
    void testEdgeCase_VeryLongTemplate() {
        // Arrange
        StringBuilder longTemplate = new StringBuilder();
        Map<String, Object> contextData = Maps.newHashMap();
        
        for (int i = 0; i < 1000; i++) {
            longTemplate.append("Item ").append(i).append(": ${item").append(i).append("} ");
            contextData.put("item" + i, "value" + i);
        }

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String result = VelocityUtil.replacePlaceholder(longTemplate.toString(), contextData);
            assertNotNull(result);
            assertTrue(result.contains("Item 0: value0"));
            assertTrue(result.contains("Item 999: value999"));
        });
    }

    @Test
    @DisplayName("边界测试 - 对象属性访问")
    void testEdgeCase_ObjectPropertyAccess() {
        // Arrange - 测试Velocity支持的对象属性访问语法
        String template = "Value: ${outer.key}";
        Map<String, Object> contextData = Maps.newHashMap();

        Map<String, Object> outer = Maps.newHashMap();
        outer.put("key", "nested_value");
        contextData.put("outer", outer);

        // Act
        String result = VelocityUtil.replacePlaceholder(template, contextData);

        // Assert
        assertNotNull(result);
        assertEquals("Value: nested_value", result);
    }

    // ==================== 性能测试 ====================

    @Test
    @DisplayName("性能测试 - 大量占位符替换")
    void testPerformance_MassivePlaceholderReplacement() {
        // Arrange
        int testCount = 1000;
        StringBuilder template = new StringBuilder();
        Map<String, Object> contextData = Maps.newHashMap();
        
        for (int i = 0; i < testCount; i++) {
            template.append("${var").append(i).append("} ");
            contextData.put("var" + i, "value" + i);
        }

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        String result = VelocityUtil.replacePlaceholder(template.toString(), contextData);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("value0"));
        assertTrue(result.contains("value999"));
        
        // 性能断言：1000个占位符替换应该在2秒内完成
        assertTrue(duration < 2000, "Performance test failed: took " + duration + "ms for " + testCount + " placeholders");
    }

    @Test
    @DisplayName("性能测试 - 重复模板处理")
    void testPerformance_RepeatedTemplateProcessing() {
        // Arrange
        String template = "Hello ${name}, your score is ${score}!";
        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put("name", "TestUser");
        contextData.put("score", 95);
        
        int iterations = 1000;

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            String result = VelocityUtil.replacePlaceholder(template, contextData);
            assertNotNull(result);
            assertEquals("Hello TestUser, your score is 95!", result);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：1000次模板处理应该在5秒内完成（Velocity相对较慢）
        assertTrue(duration < 5000, "Performance test failed: took " + duration + "ms for " + iterations + " iterations");
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的模板处理流程")
    void testIntegration_CompleteTemplateProcessingFlow() {
        // Arrange - 模拟真实业务场景
        String emailTemplate = "Dear ${customer.name},\n\n" +
                "Thank you for your order #${order.number}.\n" +
                "Order Details:\n" +
                "- Product: ${product.name}\n" +
                "- Quantity: ${order.quantity}\n" +
                "- Unit Price: $${product.price}\n" +
                "- Total Amount: $${order.total}\n" +
                "- Order Date: ${order.date}\n" +
                "- Delivery Address: ${customer.address}\n\n" +
                "Your order will be delivered within ${delivery.days} business days.\n" +
                "Tracking number: ${tracking.number}\n\n" +
                "If you have any questions, please contact us at ${support.email}.\n\n" +
                "Best regards,\n" +
                "${company.name}";

        Map<String, Object> contextData = Maps.newHashMap();
        
        // 客户信息
        Map<String, Object> customer = Maps.newHashMap();
        customer.put("name", "John Smith");
        customer.put("address", "123 Main St, New York, NY 10001");
        contextData.put("customer", customer);
        
        // 订单信息
        Map<String, Object> order = Maps.newHashMap();
        order.put("number", "ORD-2025-001");
        order.put("quantity", 2);
        order.put("total", 199.98);
        order.put("date", "2025-08-19");
        contextData.put("order", order);
        
        // 产品信息
        Map<String, Object> product = Maps.newHashMap();
        product.put("name", "Premium Laptop");
        product.put("price", 99.99);
        contextData.put("product", product);
        
        // 配送信息
        Map<String, Object> delivery = Maps.newHashMap();
        delivery.put("days", 3);
        contextData.put("delivery", delivery);
        
        // 跟踪信息
        Map<String, Object> tracking = Maps.newHashMap();
        tracking.put("number", "TRK123456789");
        contextData.put("tracking", tracking);
        
        // 支持信息
        Map<String, Object> support = Maps.newHashMap();
        support.put("email", "<EMAIL>");
        contextData.put("support", support);
        
        // 公司信息
        Map<String, Object> company = Maps.newHashMap();
        company.put("name", "FaceShare Technology");
        contextData.put("company", company);

        // Act - 执行完整的模板处理流程
        String result = VelocityUtil.replacePlaceholder(emailTemplate, contextData);

        // Assert - 验证完整流程结果
        assertNotNull(result, "处理结果不应为null");
        
        // 验证客户信息
        assertTrue(result.contains("Dear John Smith"), "应包含客户姓名");
        assertTrue(result.contains("123 Main St, New York, NY 10001"), "应包含客户地址");
        
        // 验证订单信息
        assertTrue(result.contains("order #ORD-2025-001"), "应包含订单号");
        assertTrue(result.contains("Quantity: 2"), "应包含订单数量");
        assertTrue(result.contains("Total Amount: $199.98"), "应包含订单总额");
        assertTrue(result.contains("Order Date: 2025-08-19"), "应包含订单日期");
        
        // 验证产品信息
        assertTrue(result.contains("Product: Premium Laptop"), "应包含产品名称");
        assertTrue(result.contains("Unit Price: $99.99"), "应包含产品价格");
        
        // 验证配送信息
        assertTrue(result.contains("within 3 business days"), "应包含配送天数");
        assertTrue(result.contains("Tracking number: TRK123456789"), "应包含跟踪号");
        
        // 验证支持信息
        assertTrue(result.contains("<EMAIL>"), "应包含支持邮箱");
        
        // 验证公司信息
        assertTrue(result.contains("FaceShare Technology"), "应包含公司名称");
        
        // 验证模板结构完整性
        assertTrue(result.contains("Dear "), "应包含邮件开头");
        assertTrue(result.contains("Best regards,"), "应包含邮件结尾");
        assertFalse(result.contains("${"), "不应包含未替换的占位符");
        
        // 验证业务逻辑正确性
        assertTrue(result.length() > 0, "处理结果应有内容");
        assertFalse(result.trim().isEmpty(), "处理结果不应为空");
    }
}
