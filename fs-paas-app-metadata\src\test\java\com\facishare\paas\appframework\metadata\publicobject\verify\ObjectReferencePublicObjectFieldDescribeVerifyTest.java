package com.facishare.paas.appframework.metadata.publicobject.verify;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.WheresExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectTestBase;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicFieldType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ObjectReferencePublicObjectFieldDescribeVerify的JUnit5测试类
 * 
 * GenerateByAI
 * 
 * 测试目标：
 * - 覆盖ObjectReferencePublicObjectFieldDescribeVerify的所有public和protected方法
 * - 测试对象引用字段的特定验证逻辑
 * - 验证过滤条件和函数依赖的验证
 * - 确保Mock配置正确，达到80%以上覆盖率
 * 
 * 测试策略：
 * - 使用AAA模式组织测试代码
 * - 覆盖正常流程、异常流程、边界条件
 * - 重点测试对象引用字段的验证逻辑
 * - 验证与DescribeLogicService的交互
 * 
 * 覆盖率目标：80%以上
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectReferencePublicObjectFieldDescribeVerify JUnit5 测试")
class ObjectReferencePublicObjectFieldDescribeVerifyTest extends PublicObjectTestBase {

    @InjectMocks
    private ObjectReferencePublicObjectFieldDescribeVerify objectReferenceVerify;

    @Mock
    private DescribeLogicService describeLogicService;
    @Mock
    private IFieldDescribe objectReferenceFieldDescribe;
    @Mock
    private IObjectDescribe targetObjectDescribe;

    /**
     * 测试前的特定设置
     */
    @Override
    protected void specificSetUp() {
        // 配置常用的Mock行为
        setupCommonMockBehaviors();
    }

    /**
     * 配置常用的Mock行为
     */
    private void setupCommonMockBehaviors() {
        // 配置字段描述的基本属性
        when(objectReferenceFieldDescribe.getApiName()).thenReturn("reference_field");
        when(objectReferenceFieldDescribe.getLabel()).thenReturn("引用字段");
        when(objectReferenceFieldDescribe.getDescribeApiName()).thenReturn(TEST_OBJECT_API_NAME);
        when(objectReferenceFieldDescribe.getType()).thenReturn(IFieldType.OBJECT_REFERENCE);
        
        // 配置对象描述的基本属性
        when(mockObjectDescribe.getApiName()).thenReturn(TEST_OBJECT_API_NAME);
        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");
        when(mockObjectDescribe.getFieldDescribe(anyString())).thenReturn(objectReferenceFieldDescribe);
        
        // 配置目标对象描述
        when(targetObjectDescribe.getApiName()).thenReturn("target_object");
        when(targetObjectDescribe.getDisplayName()).thenReturn("目标对象");
        when(targetObjectDescribe.getFieldDescribe(anyString())).thenReturn(objectReferenceFieldDescribe);
        
        // 配置DescribeLogicService
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put("target_object", targetObjectDescribe);
        when(describeLogicService.findObjects(anyString(), anySet())).thenReturn(describeMap);
    }

    // ==================== getSupportedFieldTypes方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支持的字段类型 - 返回对象引用相关类型
     */
    @Test
    @DisplayName("正常场景 - 获取支持的字段类型")
    void testGetSupportedFieldTypes_Success() {
        // Act
        List<String> result = objectReferenceVerify.getSupportedFieldTypes();

        // Assert
        assertNotNull(result);
        assertEquals(8, result.size());
        assertTrue(result.contains(IFieldType.OBJECT_REFERENCE));
        assertTrue(result.contains(IFieldType.OBJECT_REFERENCE_MANY));
        assertTrue(result.contains(IFieldType.DEPARTMENT));
        assertTrue(result.contains(IFieldType.DEPARTMENT_MANY));
        assertTrue(result.contains(IFieldType.OUT_DEPARTMENT));
        assertTrue(result.contains(IFieldType.EMPLOYEE));
        assertTrue(result.contains(IFieldType.EMPLOYEE_MANY));
        assertTrue(result.contains(IFieldType.OUT_EMPLOYEE));
        assertEquals(ImmutableList.of(IFieldType.OBJECT_REFERENCE, IFieldType.OBJECT_REFERENCE_MANY,
                IFieldType.DEPARTMENT, IFieldType.DEPARTMENT_MANY, IFieldType.OUT_DEPARTMENT,
                IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY, IFieldType.OUT_EMPLOYEE), result);
    }

    // ==================== verifyPublicFieldByDependency方法测试 ====================

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖验证 - 非公共字段，返回null
     */
    @Test
    @DisplayName("边界场景 - 非公共字段返回null")
    void testVerifyPublicFieldByDependency_NotPublicField() {
        // Arrange
        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPublicField(anyString())).thenReturn(java.util.Optional.empty());

            // Act
            VerifyResult result = objectReferenceVerify.verifyPublicFieldByDependency(mockUser, mockObjectDescribe, objectReferenceFieldDescribe);

            // Assert
            assertNull(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖验证 - 公共字段，验证成功
     */
    @Test
    @DisplayName("正常场景 - 公共字段依赖验证成功")
    void testVerifyPublicFieldByDependency_PublicField_Success() {
        // Arrange
        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<PublicFieldType> mockedPublicFieldType = mockStatic(PublicFieldType.class)) {
            
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPublicField(anyString())).thenReturn(java.util.Optional.of(objectReferenceFieldDescribe));

            // Mock字段配置为没有过滤条件
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList());
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.RELATION_WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList());
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERE_TYPE)))
                    .thenReturn(null);
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.RELATION_WHERES_TYPE)))
                    .thenReturn(null);
            
            PublicFieldType mockPublicFieldType = mock(PublicFieldType.class);
            mockedPublicFieldType.when(() -> PublicFieldType.fromFieldDescribe(any(IObjectDescribe.class), anyString()))
                    .thenReturn(mockPublicFieldType);
            when(mockPublicFieldType.dependentOn(any(PublicFieldType.class))).thenReturn(true);

            // Act
            VerifyResult result = objectReferenceVerify.verifyPublicFieldByDependency(mockUser, mockObjectDescribe, objectReferenceFieldDescribe);

            // Assert
            assertNotNull(result);
            assertTrue(result.success());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖验证 - 包含函数过滤条件，验证失败
     */
    @Test
    @DisplayName("异常场景 - 包含函数过滤条件")
    void testVerifyPublicFieldByDependency_WithFunctionFilter() {
        // Arrange
        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPublicField(anyString())).thenReturn(java.util.Optional.of(objectReferenceFieldDescribe));

            // Mock字段配置包含函数过滤条件
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERE_TYPE)))
                    .thenReturn(FilterExt.FUNCTION_WHERE_TYPE);

            // Mock过滤条件包含函数
            Map<String, Object> filterMap = Maps.newHashMap();
            filterMap.put("valueType", FilterExt.FilterValueTypes.FUNCTION_VARIABLE);
            filterMap.put("fieldValues", Lists.newArrayList("test_function"));

            Map<String, Object> wheresMap = Maps.newHashMap();
            wheresMap.put("filters", Lists.newArrayList(filterMap));

            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList(wheresMap));
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.RELATION_WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList());

            // Act
            VerifyResult result = objectReferenceVerify.verifyPublicFieldByDependency(mockUser, mockObjectDescribe, objectReferenceFieldDescribe);

            // Assert
            assertNotNull(result);
            assertFalse(result.success());
            assertFalse(result.getMessages().isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖验证 - 字段依赖关系错误
     */
    @Test
    @DisplayName("异常场景 - 字段依赖关系错误")
    void testVerifyPublicFieldByDependency_InvalidFieldDependency() {
        // Arrange
        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<PublicFieldType> mockedPublicFieldType = mockStatic(PublicFieldType.class)) {
            
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPublicField(anyString())).thenReturn(java.util.Optional.of(objectReferenceFieldDescribe));

            // Mock字段配置包含字段依赖
            Map<String, Object> filterMap = Maps.newHashMap();
            filterMap.put("valueType", "FIELD");
            filterMap.put("fieldName", "dependent_field");
            filterMap.put("objectApiName", "target_object");

            Map<String, Object> wheresMap = Maps.newHashMap();
            wheresMap.put("filters", Lists.newArrayList(filterMap));

            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList(wheresMap));
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.RELATION_WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList());
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERE_TYPE)))
                    .thenReturn(null);
            
            // Mock PublicFieldType依赖关系错误
            PublicFieldType mockPublicFieldType = mock(PublicFieldType.class);
            PublicFieldType mockDependentFieldType = mock(PublicFieldType.class);
            mockedPublicFieldType.when(() -> PublicFieldType.fromFieldDescribe(eq(mockObjectDescribe), anyString()))
                    .thenReturn(mockPublicFieldType);
            mockedPublicFieldType.when(() -> PublicFieldType.fromFieldDescribe(eq(targetObjectDescribe), anyString()))
                    .thenReturn(mockDependentFieldType);
            
            when(mockPublicFieldType.dependentOn(mockDependentFieldType)).thenReturn(false);
            when(mockPublicFieldType.getName()).thenReturn("公共字段");
            when(mockPublicFieldType.getType()).thenReturn("PUBLIC_FIELD");
            when(mockDependentFieldType.getName()).thenReturn("私有字段");
            when(mockDependentFieldType.getType()).thenReturn("PRIVATE_FIELD");

            // Act
            VerifyResult result = objectReferenceVerify.verifyPublicFieldByDependency(mockUser, mockObjectDescribe, objectReferenceFieldDescribe);

            // Assert
            assertNotNull(result);
            assertFalse(result.success());
            assertFalse(result.getMessages().isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖验证 - 关系过滤条件包含函数
     */
    @Test
    @DisplayName("异常场景 - 关系过滤条件包含函数")
    void testVerifyPublicFieldByDependency_WithRelationFunction() {
        // Arrange
        try (MockedStatic<ObjectDescribeExt> mockedDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            mockedDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                    .thenReturn(mockDescribeExt);
            when(mockDescribeExt.getPublicField(anyString())).thenReturn(java.util.Optional.of(objectReferenceFieldDescribe));

            // Mock字段配置
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList());
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.WHERE_TYPE)))
                    .thenReturn(null);

            // Mock关系过滤条件包含函数，但不是FUNCTION_WHERE_TYPE
            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.RELATION_WHERES_TYPE)))
                    .thenReturn("OTHER_TYPE");

            // Mock关系过滤条件包含函数
            Map<String, Object> filterMap = Maps.newHashMap();
            filterMap.put("valueType", FilterExt.FilterValueTypes.FUNCTION_VARIABLE);
            filterMap.put("fieldValues", Lists.newArrayList("relation_function"));

            Map<String, Object> wheresMap = Maps.newHashMap();
            wheresMap.put("filters", Lists.newArrayList(filterMap));

            when(objectReferenceFieldDescribe.get(eq(ObjectReferenceFieldDescribe.RELATION_WHERES), eq(List.class)))
                    .thenReturn(Lists.newArrayList(wheresMap));

            // Act
            VerifyResult result = objectReferenceVerify.verifyPublicFieldByDependency(mockUser, mockObjectDescribe, objectReferenceFieldDescribe);

            // Assert
            assertNotNull(result);
            assertFalse(result.success());
            assertFalse(result.getMessages().isEmpty());
        }
    }
}
