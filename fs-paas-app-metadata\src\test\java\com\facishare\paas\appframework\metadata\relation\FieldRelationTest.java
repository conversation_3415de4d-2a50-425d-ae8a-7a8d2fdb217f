package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FieldRelationTest {

    @Mock(lenient = true)
    private FieldRelationGraph mockGraph;

    @Mock(lenient = true)
    private FieldNode mockCalcFieldNode;

    @Mock(lenient = true)
    private FieldNode mockDependentFieldNode;

    private FieldRelation fieldRelation;
    private String calcObjectApiName = "CalcObject";
    private String calcFieldName = "calcField";
    private String dependentObjectApiName = "DependentObject";
    private String dependentFieldName = "dependentField";

    @BeforeEach
    void setUp() {
        fieldRelation = new FieldRelation();

        when(mockCalcFieldNode.getObjectApiName()).thenReturn(calcObjectApiName);
        when(mockCalcFieldNode.getFieldApiName()).thenReturn(calcFieldName);
        when(mockCalcFieldNode.toRelateField()).thenReturn(RelateField.of(calcFieldName, 1, "FORMULA"));

        when(mockDependentFieldNode.getObjectApiName()).thenReturn(dependentObjectApiName);
        when(mockDependentFieldNode.getFieldApiName()).thenReturn(dependentFieldName);
        when(mockDependentFieldNode.toRelateField()).thenReturn(RelateField.of(dependentFieldName, 1, "FORMULA"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldRelation构造函数创建对象的正常场景
     */
    @Test
    @DisplayName("构造函数 - 正常创建对象")
    void testConstructor_正常创建对象() {
        // 执行被测试方法
        FieldRelation relation = new FieldRelation();

        // 验证结果
        assertNotNull(relation);
        assertNotNull(relation.getCalculateRelationMap());
        assertTrue(relation.getCalculateRelationMap().isEmpty());
        assertNull(relation.getGraph());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用FieldNode添加关系的正常场景
     */
    @Test
    @DisplayName("添加关系 - 使用FieldNode正常场景")
    void testAddRelation_使用FieldNode正常场景() {
        // 执行被测试方法
        fieldRelation.addRelation(mockCalcFieldNode, mockDependentFieldNode);

        // 验证结果
        assertNotNull(fieldRelation.getCalculateRelationMap());
        assertTrue(fieldRelation.getCalculateRelationMap().containsKey(dependentObjectApiName));
        assertTrue(fieldRelation.getCalculateRelationMap().get(dependentObjectApiName).containsKey(dependentFieldName));

        // 验证Mock交互 - 修正调用次数
        verify(mockCalcFieldNode, times(3)).getObjectApiName(); // 第37、39行调用2次，第40行调用1次
        verify(mockCalcFieldNode, times(2)).getFieldApiName(); // 第37、40行调用
        verify(mockCalcFieldNode).toRelateField();
        verify(mockDependentFieldNode, times(3)).getObjectApiName(); // 第37、38、41行调用
        verify(mockDependentFieldNode, times(2)).getFieldApiName(); // 第37、41行调用
        verify(mockDependentFieldNode).toRelateField();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用FieldNode集合添加关系的正常场景
     */
    @Test
    @DisplayName("添加关系 - 使用FieldNode集合正常场景")
    void testAddRelation_使用FieldNode集合正常场景() {
        // 准备测试数据
        FieldNode mockCalcFieldNode2 = mock(FieldNode.class);
        when(mockCalcFieldNode2.getObjectApiName()).thenReturn("CalcObject2");
        when(mockCalcFieldNode2.getFieldApiName()).thenReturn("calcField2");
        when(mockCalcFieldNode2.toRelateField()).thenReturn(RelateField.of("calcField2", 1, "FORMULA"));

        Collection<FieldNode> calcFields = Lists.newArrayList(mockCalcFieldNode, mockCalcFieldNode2);

        // 执行被测试方法
        fieldRelation.addRelation(calcFields, mockDependentFieldNode);

        // 验证结果
        assertNotNull(fieldRelation.getCalculateRelationMap());
        assertTrue(fieldRelation.getCalculateRelationMap().containsKey(dependentObjectApiName));

        // 验证Mock交互 - 修正调用次数，集合中每个元素都会调用addRelation方法
        verify(mockCalcFieldNode, times(3)).getObjectApiName(); // 每个calcField调用3次
        verify(mockCalcFieldNode, times(2)).getFieldApiName(); // 每个calcField调用2次
        verify(mockCalcFieldNode2, times(3)).getObjectApiName(); // 每个calcField调用3次
        verify(mockCalcFieldNode2, times(2)).getFieldApiName(); // 每个calcField调用2次
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用空FieldNode集合添加关系的场景
     */
    @Test
    @DisplayName("添加关系 - 使用空FieldNode集合")
    void testAddRelation_使用空FieldNode集合() {
        // 准备测试数据
        Collection<FieldNode> emptyCalcFields = Lists.newArrayList();

        // 执行被测试方法
        fieldRelation.addRelation(emptyCalcFields, mockDependentFieldNode);

        // 验证结果
        assertTrue(fieldRelation.getCalculateRelationMap().isEmpty());

        // 验证Mock交互
        verify(mockDependentFieldNode, never()).getObjectApiName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用字符串参数添加关系的正常场景
     */
    @Test
    @DisplayName("添加关系 - 使用字符串参数正常场景")
    void testAddRelation_使用字符串参数正常场景() {
        // 执行被测试方法
        fieldRelation.addRelation(calcObjectApiName, calcFieldName, dependentObjectApiName, dependentFieldName);

        // 验证结果
        assertNotNull(fieldRelation.getCalculateRelationMap());
        assertTrue(fieldRelation.getCalculateRelationMap().containsKey(dependentObjectApiName));
        assertTrue(fieldRelation.getCalculateRelationMap().get(dependentObjectApiName).containsKey(dependentFieldName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试添加循环依赖关系时抛出异常
     */
    @Test
    @DisplayName("添加关系 - 循环依赖抛出异常")
    void testAddRelationThrowsMetaDataBusinessException_循环依赖() {
        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            fieldRelation.addRelation(calcObjectApiName, calcFieldName, calcObjectApiName, calcFieldName);
        });

        // 验证异常不为空
        assertNotNull(exception);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用字符串集合添加计算字段关系的正常场景
     */
    @Test
    @DisplayName("添加关系 - 使用字符串集合添加计算字段")
    void testAddRelation_使用字符串集合添加计算字段() {
        // 准备测试数据
        Collection<String> calcFieldNames = Sets.newHashSet("calcField1", "calcField2");

        // 执行被测试方法
        fieldRelation.addRelation(calcObjectApiName, calcFieldNames, dependentObjectApiName, dependentFieldName);

        // 验证结果
        assertNotNull(fieldRelation.getCalculateRelationMap());
        assertTrue(fieldRelation.getCalculateRelationMap().containsKey(dependentObjectApiName));
        assertTrue(fieldRelation.getCalculateRelationMap().get(dependentObjectApiName).containsKey(dependentFieldName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用空字符串集合添加计算字段关系的场景
     */
    @Test
    @DisplayName("添加关系 - 使用空字符串集合添加计算字段")
    void testAddRelation_使用空字符串集合添加计算字段() {
        // 准备测试数据
        Collection<String> emptyCalcFieldNames = Sets.newHashSet();

        // 执行被测试方法
        fieldRelation.addRelation(calcObjectApiName, emptyCalcFieldNames, dependentObjectApiName, dependentFieldName);

        // 验证结果
        assertTrue(fieldRelation.getCalculateRelationMap().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用字符串集合添加依赖字段关系的正常场景
     */
    @Test
    @DisplayName("添加关系 - 使用字符串集合添加依赖字段")
    void testAddRelation_使用字符串集合添加依赖字段() {
        // 准备测试数据
        Collection<String> dependentFieldNames = Sets.newHashSet("dependentField1", "dependentField2");

        // 执行被测试方法
        fieldRelation.addRelation(calcObjectApiName, calcFieldName, dependentObjectApiName, dependentFieldNames);

        // 验证结果
        assertNotNull(fieldRelation.getCalculateRelationMap());
        assertTrue(fieldRelation.getCalculateRelationMap().containsKey(dependentObjectApiName));
        assertEquals(2, fieldRelation.getCalculateRelationMap().get(dependentObjectApiName).size());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用空字符串集合添加依赖字段关系的场景
     */
    @Test
    @DisplayName("添加关系 - 使用空字符串集合添加依赖字段")
    void testAddRelation_使用空字符串集合添加依赖字段() {
        // 准备测试数据
        Collection<String> emptyDependentFieldNames = Sets.newHashSet();

        // 执行被测试方法
        fieldRelation.addRelation(calcObjectApiName, calcFieldName, dependentObjectApiName, emptyDependentFieldNames);

        // 验证结果
        assertTrue(fieldRelation.getCalculateRelationMap().isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置和获取FieldRelationGraph的正常场景
     */
    @Test
    @DisplayName("设置获取图 - 正常场景")
    void testSetAndGetGraph_正常场景() {
        // 执行被测试方法
        fieldRelation.setGraph(mockGraph);

        // 验证结果
        assertEquals(mockGraph, fieldRelation.getGraph());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试设置null FieldRelationGraph的场景
     */
    @Test
    @DisplayName("设置获取图 - 设置null")
    void testSetAndGetGraph_设置null() {
        // 执行被测试方法
        fieldRelation.setGraph(null);

        // 验证结果
        assertNull(fieldRelation.getGraph());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCalculateRelationMap方法返回的Map是可修改的
     */
    @Test
    @DisplayName("获取计算关系映射 - 返回可修改的Map")
    void testGetCalculateRelationMap_返回可修改的Map() {
        // 先添加一些关系
        fieldRelation.addRelation(calcObjectApiName, calcFieldName, dependentObjectApiName, dependentFieldName);

        // 执行被测试方法
        Map<String, Map<String, CalculateRelation>> relationMap = fieldRelation.getCalculateRelationMap();

        // 验证结果
        assertNotNull(relationMap);
        assertFalse(relationMap.isEmpty());

        // 验证Map是可修改的
        assertDoesNotThrow(() -> {
            relationMap.put("testKey", null);
        });
    }
}
