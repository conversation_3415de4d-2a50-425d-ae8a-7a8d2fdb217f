package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupFieldComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.NavigationComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ComponentExt.NAME_COMPONENT;
import static com.facishare.paas.appframework.metadata.layout.LayoutStructure.LAYOUT;

/**
 * Created by zhaooju on 2024/8/20
 */
@Builder
public class SidebarLayoutBuilder {
    private static final List<String> LAYOUT_STRUCTURE_PROPS_TO_SYNC = Lists.newArrayList("show_tag");

    private PageType pageType;
    private LayoutExt webLayout;
    private ObjectDescribeExt describeExt;
    private IObjectData objectData;
    private List<IComponent> componentConfig;

    private static final List<String> targets = Collections.unmodifiableList(Lists.newArrayList(ComponentExt.NAVIGATION_COMPONENT_NAME, ComponentExt.FORM_COMPONENT));


    public LayoutExt getSidebarLayout() {
        return new SidebarLayoutProcessorImpl(pageType, webLayout, describeExt, objectData, componentConfig).processLayout();
    }

}
