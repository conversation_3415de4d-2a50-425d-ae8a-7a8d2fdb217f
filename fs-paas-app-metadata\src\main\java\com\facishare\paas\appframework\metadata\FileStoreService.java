package com.facishare.paas.appframework.metadata;

import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhouwr on 2018/3/6
 */
public interface FileStoreService {

    /**
     * 上传临时文件
     *
     * @param tenantId 企业账号
     * @param userId   用户id
     * @param fileData 文件内容
     * @return 临时文件的存储信息
     */
    String uploadTempFile(String tenantId, String userId, byte[] fileData);

    /**
     * 将临时文件转成正式文件
     *
     * @param tenantId 企业id
     * @param userId   用户id
     * @param tmpPath  图片临时存储路径
     * @param fileExt  图片后缀
     * @return 正式文件的存储信息
     */
    String saveFileFromTempFile(String tenantId, String userId, String tmpPath, String fileExt);

    String saveFileFromTempFile(String tenantId, String userId, PathOriginNames pathOriginNames, String fileExt);

    List<PathPair> saveFileFromTempFiles(String enterpriseAccount, String userId, List<PathOriginNames> pathOriginNamesList, String fileExt);

    /**
     * 使用附件接口保存图片，预设对象使用
     *
     * @param tenantId     企业id
     * @param userId       用户id
     * @param tmpPath      图片临时存储路径
     * @param fileExt      图片后缀
     * @param addThumbnail 是否生成缩略图
     * @return 图片正式文件的存储信息
     */
    String saveImageFromTempFile(String tenantId, String userId, String tmpPath, String fileExt, boolean addThumbnail);

    /**
     * 使用新接口保存图片，自定义对象使用
     *
     * @param tenantId 企业id
     * @param userId   用户id
     * @param tmpPaths 图片临时存储路径
     * @param fileExt  图片后缀
     * @return 图片正式文件的存储信息
     */
    List<String> saveImageFromTempFiles(String tenantId, String userId, List<String> tmpPaths, String fileExt);

    List<PathPair> saveImageFromTempFilesAndNames(String tenantId, String userId, List<PathOriginNames> pathOriginNames,
                                                  String fileExt);

    /**
     * 通过网盘的带有权限租的npath，获取无权限组的npath
     *
     * @param ea     企业id
     * @param userId 用户id
     * @return newPath
     */
    List<V5FileInfo> getNPathsWithoutPermission(String ea, String userId, List<V5FileInfo> files);


    /**
     * 通过网盘的带有权限租的NPath，获取无权限组的NPath
     *
     * @return newPath
     */
    List<V5FileInfo> getNPathsWithoutPermission(User user, List<V5FileInfo> files);

    /**
     *
     * 对图片文件 NPath 进行签名
     * @see #generateNPathSignedUrl(IActionContext, AuthModel, List)
     * @param ctx 上下文
     * @param imgList 图片信息
     */
    @Deprecated
    void generateImageSignUrl(IActionContext ctx, List<Map<String, Object>> imgList);


    /**
     * @see #generateNPathSignedUrl(IActionContext, Set, IObjectDescribe, List)
     */
    @Deprecated
    void generateImageSignUrl(IActionContext ctx, IObjectDescribe describe, List<IObjectData> dataList);

    /**
     * 通过文件路径获取文件名称
     *
     * @param paths
     * @param user
     */
    Map<String, String> getFileNameList(List<String> paths, User user);

    @Data
    @AllArgsConstructor(staticName = "of")
    class PathOriginNames {
        private String path;
        private String originNames;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class PathPair {
        private String tempPath;
        private String finalNPath;
        private Long size;

        @Deprecated
        public static PathPair of(String tempPath, String finalNPath) {
            return new PathPair(tempPath, finalNPath, null); // 或默认值
        }
    }

    /**
     * 生成cpath签名
     */
    String generateCpathSignature(User user, String cPath);

    /**
     * 生成cpath跨租户访问url
     */
    String generateCpathAccessUrl(User user, String cPath, String signature);


    /**
     * 根据 NPath 文件的 signature 生成 signedUrl，用于无身份访问
     * @param ctx 上下文
     * @param list 含有 NPath 的列表
     */
    void generateNPathSignedUrl(IActionContext ctx, AuthModel authModel, List<Map<String, Object>> list);


    /**
     * 处理数据中的图片和附件字段，对其进行生成 signedUrl
     * @param ctx 上下文
     * @param fieldTypes 字段类型
     * @param describe 对象描述信息
     * @param dataList 对象数据列表
     */
    void generateNPathSignedUrl(IActionContext ctx, Set<String> fieldTypes, IObjectDescribe describe, List<IObjectData> dataList);

    void generateNPathSignedUrlForEdit(IActionContext ctx, Set<String> fieldTypes, IObjectDescribe describe, List<IObjectData> dataList);

    /**
     * 根据字段类型，区分 AuthModel
     * @param fieldType 字段类型
     * @return AuthModel
     */
    AuthModel getAuthModel(String fieldType);

    /**
     * 生成 signedUrl
     * @param objData field -> value
     * @param authModel 模式
     * @param ctx 上下文
     */
    public void generateSignedUrl(Map<String, Object> objData, AuthModel authModel, IActionContext ctx);

    /**
     * 生成 signedUrl
     * @param nested obj -> field -> value
     * @param authModel 模式
     * @param ctx 上下文
     */
    public void generateNestedSignedUrl(Map<String, Map<String, Object>> nested, AuthModel authModel, IActionContext ctx);

    /**
     * 清除 signedUrl
     * @param objData field -> value
     */
    public void cleanSignedUrl(Map<String, Object> objData);

    /**
     * 清除 signedUrl
     * @param nested obj -> field -> value
     */
    public Map<String, Map<String, Object>> cleanNestedSignedUrl(Map<String, Map<String, Object>> nested);
}
