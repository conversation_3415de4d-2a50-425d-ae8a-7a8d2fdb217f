package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import com.facishare.paas.appframework.metadata.publicobject.verify.PublicObjectJobVerifyManger;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobResultDTO;
import com.facishare.paas.foundation.boot.Injector;
import com.facishare.paas.appframework.metadata.publicobject.verify.PublicObjectJobVerify;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectEnterpriseRelationService;
import com.facishare.paas.appframework.metadata.publicobject.verify.PublicObjectFieldDescribeVerifyManager;
import com.facishare.paas.appframework.metadata.relation.FieldRelationGraphService;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraphService;
import com.facishare.paas.metadata.service.impl.MetadataTransactionService;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;
import org.springframework.context.ApplicationContext;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * PublicObject包专用测试基类 - 提供PublicObject相关的测试配置和Mock设置
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 继承BaseJUnit5Test的通用功能
 * - 提供PublicObject专用的Mock对象配置
 * - 统一的灰度配置Mock策略
 * - PublicObject业务对象的Mock工厂方法
 * 
 * 使用方式：
 * - PublicObject包的测试类继承此基类
 * - 自动获得所有必要的Mock对象配置
 * - 提供统一的测试数据构造方法
 * 
 * 覆盖率目标：为PublicObject包达到80%以上覆盖率提供基础支持
 */
@ExtendWith(MockitoExtension.class)
public abstract class PublicObjectTestBase {
    
    // 测试常量 - PublicObject专用
    protected static final String TEST_TENANT_ID = "74255";
    protected static final String TEST_USER_ID = "test-user-456";
    protected static final String TEST_UPSTREAM_TENANT_ID = "12345";
    protected static final String TEST_OBJECT_API_NAME = "TestPublicObj";
    protected static final String TEST_JOB_ID = "test-job-id-001";
    protected static final String TEST_FIELD_API_NAME = "test_field";
    protected static final String TEST_TOKEN = "test-invitation-token";
    
    // 通用Mock对象
    protected User mockUser;
    protected User mockUpstreamUser;
    protected IObjectDescribe mockObjectDescribe;
    protected ApplicationContext mockApplicationContext;
    
    // PublicObject核心服务Mock对象
    @Mock
    protected DescribeLogicService mockDescribeLogicService;
    @Mock
    protected PublicObjectJobService mockPublicObjectJobService;
    @Mock
    protected PublicObjectVerifyService mockPublicObjectVerifyService;
    @Mock
    protected PublicObjectInviteService mockPublicObjectInviteService;
    @Mock
    protected PublicObjectEnterpriseRelationService mockPublicObjectEnterpriseRelationService;
    @Mock
    protected EnterpriseEditionService mockEnterpriseEditionService;
    @Mock
    protected EnterpriseRelationLogicService mockEnterpriseRelationLogicService;
    @Mock
    protected LogService mockLogService;
    @Mock
    protected UserRoleInfoService mockUserRoleInfoService;
    
    // 验证相关Mock对象
    @Mock
    protected PublicObjectJobVerifyManger mockPublicObjectJobVerifyManger;
    @Mock
    protected PublicObjectJobVerify mockPublicObjectJobVerify;
    @Mock
    protected MetadataTransactionService mockMetadataTransactionService;
    @Mock
    protected ObjectRelationGraphService mockObjectRelationGraphService;
    @Mock
    protected FieldRelationGraphService mockFieldRelationGraphService;
    @Mock
    protected PublicObjectFieldDescribeVerifyManager mockPublicObjectFieldDescribeVerifyManager;
    
    // 静态Mock
    protected MockedStatic<SpringUtil> mockedSpringUtil;
    protected MockedStatic<Injector> mockedInjector;
    protected MockedStatic<ConfigFactory> mockedConfigFactory;
    protected MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig;
    
    // 灰度配置Mock
    @Mock
    protected FsGrayReleaseBiz mockFsGrayReleaseBiz;
    @Mock
    protected IConfig mockConfig;
    
    // 配置存储
    private static final Map<String, String> mockConfigs = new ConcurrentHashMap<>();
    
    /**
     * 测试前的通用设置
     */
    @BeforeEach
    void publicObjectSetUp() {
        // 创建通用Mock对象
        setupBasicMocks();
        
        // 设置Spring相关Mock
        setupSpringMocks();
        
        // 设置灰度配置Mock
        setupGrayConfigMocks();
        
        // 设置PublicObject配置Mock
        setupPublicObjectConfigMocks();
        
        // 调用子类的具体设置
        specificSetUp();
    }
    
    /**
     * 创建基础Mock对象
     */
    private void setupBasicMocks() {
        // 创建用户Mock
        mockUser = PublicObjectMockFactory.createMockUser(TEST_TENANT_ID, TEST_USER_ID);
        mockUpstreamUser = PublicObjectMockFactory.createMockUser(TEST_UPSTREAM_TENANT_ID, TEST_USER_ID);
        
        // 创建对象描述Mock
        mockObjectDescribe = PublicObjectMockFactory.createMockObjectDescribe(TEST_OBJECT_API_NAME);
        
        // 创建ApplicationContext Mock
        mockApplicationContext = mock(ApplicationContext.class);
    }
    
    /**
     * 设置Spring相关的Mock
     */
    private void setupSpringMocks() {
        try {
            // Mock SpringUtil
            mockedSpringUtil = mockStatic(SpringUtil.class);
            mockedSpringUtil.when(SpringUtil::getContext).thenReturn(mockApplicationContext);
            
            // Mock Injector
            mockedInjector = mockStatic(Injector.class);
            mockedInjector.when(Injector::get).thenReturn(mockApplicationContext);
            
        } catch (Exception e) {
            System.err.println("Warning: Failed to setup Spring mocks: " + e.getMessage());
        }
    }
    
    /**
     * 设置灰度配置Mock
     */
    private void setupGrayConfigMocks() {
        try {
            // Mock FsGrayReleaseBiz
            lenient().when(mockFsGrayReleaseBiz.isAllow(any(), any())).thenReturn(true);
            lenient().when(mockFsGrayReleaseBiz.isAllow(any(), anyString())).thenReturn(true);

            // 使用Whitebox设置UdobjGrayConfig的内部状态
            Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", mockFsGrayReleaseBiz);

        } catch (Exception e) {
            System.err.println("Warning: Failed to setup gray config mocks: " + e.getMessage());
        }
    }
    
    /**
     * 设置PublicObject配置Mock
     */
    private void setupPublicObjectConfigMocks() {
        try {
            // Mock ConfigFactory
            mockedConfigFactory = mockStatic(ConfigFactory.class);
            
            // Mock IConfig
            lenient().when(mockConfig.get(anyString())).thenAnswer(invocation -> {
                String key = invocation.getArgument(0);
                return mockConfigs.getOrDefault(key, "");
            });
            lenient().when(mockConfig.getString()).thenReturn("{}");

            mockedConfigFactory.when(() -> ConfigFactory.getConfig(anyString(), any()))
                    .thenAnswer(invocation -> {
                        Object callback = invocation.getArgument(1);
                        if (callback instanceof java.util.function.Consumer) {
                            ((java.util.function.Consumer<IConfig>) callback).accept(mockConfig);
                        }
                        return null;
                    });
                    
        } catch (Exception e) {
            System.err.println("Warning: Failed to setup PublicObject config mocks: " + e.getMessage());
        }
    }
    
    /**
     * 子类可以重写此方法进行特定的设置
     */
    protected void specificSetUp() {
        // 默认空实现，子类可以重写
    }
    
    /**
     * 清理静态Mock
     */
    @AfterEach
    void cleanupPublicObjectMocks() {
        if (mockedSpringUtil != null) {
            mockedSpringUtil.close();
        }
        if (mockedInjector != null) {
            mockedInjector.close();
        }
        if (mockedConfigFactory != null) {
            mockedConfigFactory.close();
        }
        if (mockedUdobjGrayConfig != null) {
            mockedUdobjGrayConfig.close();
        }
        
        // 清理配置
        mockConfigs.clear();
    }
    
    /**
     * 添加Mock配置
     */
    protected void addMockConfig(String key, String value) {
        mockConfigs.put(key, value);
    }
    
    /**
     * 创建测试用的PublicObjectJobInfo
     */
    protected PublicObjectJobInfo createTestJobInfo() {
        return PublicObjectMockFactory.createMockJobInfo(TEST_OBJECT_API_NAME, PublicObjectJobType.OPEN_JOB);
    }
    
    /**
     * 创建测试用的PublicObjectJobParamVerifyInfo
     */
    protected PublicObjectJobParamVerifyInfo createTestJobParamVerifyInfo() {
        return PublicObjectMockFactory.createMockJobParamVerifyInfo(PublicObjectJobType.OPEN_JOB, TEST_UPSTREAM_TENANT_ID);
    }
    
    /**
     * 创建测试用的PublicFieldDTO列表
     */
    protected List<PublicFieldDTO> createTestPublicFields() {
        return PublicObjectMockFactory.createMockPublicFields();
    }
}
