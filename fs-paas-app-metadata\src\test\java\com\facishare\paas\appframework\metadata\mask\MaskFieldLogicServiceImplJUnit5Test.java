package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * JUnit5测试类 for MaskFieldLogicServiceImpl
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("MaskFieldLogicServiceImpl 单元测试")
class MaskFieldLogicServiceImplJUnit5Test {

    @InjectMocks
    private MaskFieldLogicServiceImpl maskFieldLogicService;

    @Mock
    private UserRoleInfoService userRoleInfoService;
    @Mock
    private OrgService orgService;
    @Mock
    private MaskFieldEncryptService maskFieldEncryptService;

    private User testUser;
    private User superAdminUser;
    private String testTenantId = "123456789";
    private IObjectDescribe mockObjectDescribe;
    private IFieldDescribe mockFieldDescribe;
    private List<IObjectData> testDataList;

    @BeforeEach
    void setUp() {
        testUser = new User(testTenantId, "test-user-123");
        // 使用正确的超级管理员用户ID
        superAdminUser = new User(testTenantId, User.SUPPER_ADMIN_USER_ID);
        
        // Mock对象描述
        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObject");
        when(mockObjectDescribe.getTenantId()).thenReturn(testTenantId);

        // Mock字段描述
        mockFieldDescribe = mock(IFieldDescribe.class);
        when(mockFieldDescribe.getApiName()).thenReturn("test_field");
        // when(mockFieldDescribe.getFieldType()).thenReturn(IFieldType.TEXT); // 方法不存在，移除

        // 创建测试数据
        testDataList = Lists.newArrayList();
        IObjectData testData = new ObjectData();
        testData.set("id", "test-id-123");
        testData.set("test_field", "test value");
        // 设置描述API名称，避免NullPointerException
        testData.setDescribeApiName("TestObject");
        testDataList.add(testData);
    }

    // ==================== getMaskFields 方法测试 ====================

    @Test
    @DisplayName("获取掩码字段 - 超级管理员")
    void testGetMaskFields_SuperAdmin() {
        // Arrange
        Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);

        // 超级管理员用户已经在setUp中正确设置

        // Act
        Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                superAdminUser, describeList, "owner-123");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(userRoleInfoService);
        verifyNoInteractions(orgService);
    }

    @Test
    @DisplayName("获取掩码字段 - 普通用户")
    void testGetMaskFields_NormalUser() {
        // Arrange
        Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);
        // testUser是普通用户，不需要Mock

        // Act
        Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                testUser, describeList, "owner-123");

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey("TestObject"));
    }

    @Test
    @DisplayName("获取掩码字段 - 空描述列表")
    void testGetMaskFields_EmptyDescribeList() {
        // Arrange
        Collection<IObjectDescribe> emptyDescribeList = Lists.newArrayList();
        // testUser是普通用户，不需要Mock

        // Act
        Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                testUser, emptyDescribeList, "owner-123");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("获取掩码字段 - null拥有者ID")
    void testGetMaskFields_NullOwnerId() {
        // Arrange
        Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);
        // testUser是普通用户，不需要Mock

        // Act
        Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                testUser, describeList, null);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey("TestObject"));
    }

    // ==================== fillMaskFieldValue 方法测试 ====================

    @Test
    @DisplayName("填充掩码字段值 - 空掩码字段API名称")
    void testFillMaskFieldValue_EmptyMaskFieldApiNames() {
        // Arrange
        List<String> emptyMaskFieldApiNames = Lists.newArrayList();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe, 
                    emptyMaskFieldApiNames, testDataList);
        });

        verifyNoInteractions(maskFieldEncryptService);
    }

    @Test
    @DisplayName("填充掩码字段值 - null掩码字段API名称")
    void testFillMaskFieldValue_NullMaskFieldApiNames() {
        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe, 
                    null, testDataList);
        });

        verifyNoInteractions(maskFieldEncryptService);
    }

    @Test
    @DisplayName("填充掩码字段值 - 有效掩码字段")
    void testFillMaskFieldValue_ValidMaskFields() {
        // Arrange
        List<String> maskFieldApiNames = Lists.newArrayList("test_field");

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe, 
                    maskFieldApiNames, testDataList);
        });
    }

    @Test
    @DisplayName("填充掩码字段值 - 带配置参数")
    void testFillMaskFieldValue_WithConfig() {
        // Arrange
        List<IFieldDescribe> encryptFields = Lists.newArrayList(mockFieldDescribe);
        MaskFieldLogicService.MaskFieldConfig config = 
                MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig();

        // Act & Assert - 由于数据中可能存在null值，期望抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe,
                    testDataList, encryptFields, config);
        });
    }

    @Test
    @DisplayName("填充掩码字段值 - 空数据列表")
    void testFillMaskFieldValue_EmptyDataList() {
        // Arrange
        List<IObjectData> emptyDataList = Lists.newArrayList();
        List<IFieldDescribe> encryptFields = Lists.newArrayList(mockFieldDescribe);
        MaskFieldLogicService.MaskFieldConfig config = 
                MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe, 
                    emptyDataList, encryptFields, config);
        });

        verifyNoInteractions(maskFieldEncryptService);
    }

    // ==================== encodeDefaultValueByFieldDescribe 方法测试 ====================

    @Test
    @DisplayName("编码字段默认值 - 正常情况")
    void testEncodeDefaultValueByFieldDescribe_Success() {
        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.encodeDefaultValueByFieldDescribe(testUser, mockFieldDescribe);
        });
    }

    @Test
    @DisplayName("编码字段默认值 - null字段描述")
    void testEncodeDefaultValueByFieldDescribe_NullFieldDescribe() {
        // Act & Assert
        assertThrows(Exception.class, () -> {
            maskFieldLogicService.encodeDefaultValueByFieldDescribe(testUser, null);
        });
    }

    // ==================== decodeMaskFieldEncryptValue 方法测试 ====================

    @Test
    @DisplayName("解码掩码字段加密值 - 正常情况")
    void testDecodeMaskFieldEncryptValue_Success() {
        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.decodeMaskFieldEncryptValue(testUser, testDataList, mockObjectDescribe);
        });
    }

    @Test
    @DisplayName("解码掩码字段加密值 - 空数据列表")
    void testDecodeMaskFieldEncryptValue_EmptyDataList() {
        // Arrange
        List<IObjectData> emptyDataList = Lists.newArrayList();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.decodeMaskFieldEncryptValue(testUser, emptyDataList, mockObjectDescribe);
        });

        verifyNoInteractions(maskFieldEncryptService);
    }

    @Test
    @DisplayName("解码掩码字段加密值 - null数据列表")
    void testDecodeMaskFieldEncryptValue_NullDataList() {
        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            maskFieldLogicService.decodeMaskFieldEncryptValue(testUser, null, mockObjectDescribe);
        });

        verifyNoInteractions(maskFieldEncryptService);
    }

    // ==================== getMaskFieldTypes 方法测试 ====================

    @Test
    @DisplayName("获取掩码字段类型 - 正常情况")
    void testGetMaskFieldTypes_Success() {
        // Act
        List<String> result = maskFieldLogicService.getMaskFieldTypes(testTenantId, "TestObject");

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.contains(IFieldType.CURRENCY));
        assertTrue(result.contains(IFieldType.EMAIL));
        assertTrue(result.contains(IFieldType.PHONE_NUMBER));
    }

    @Test
    @DisplayName("获取掩码字段类型 - 空描述API名称")
    void testGetMaskFieldTypes_EmptyDescribeApiName() {
        // Act
        List<String> result = maskFieldLogicService.getMaskFieldTypes(testTenantId, "");

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 应该使用默认的UDOBJ
    }

    @Test
    @DisplayName("获取掩码字段类型 - null描述API名称")
    void testGetMaskFieldTypes_NullDescribeApiName() {
        // Act
        List<String> result = maskFieldLogicService.getMaskFieldTypes(testTenantId, null);

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 应该使用默认的UDOBJ
    }

    // ==================== maskFieldRoleFilter 方法测试 ====================

    @Test
    @DisplayName("掩码字段角色过滤 - 正常情况")
    void testMaskFieldRoleFilter_Success() {
        // Arrange
        List<IFieldDescribe> maskFields = Lists.newArrayList(mockFieldDescribe);

        // Act
        List<IFieldDescribe> result = maskFieldLogicService.maskFieldRoleFilter(testUser, maskFields);

        // Assert
        assertNotNull(result);
    }

    @Test
    @DisplayName("掩码字段角色过滤 - 空掩码字段列表")
    void testMaskFieldRoleFilter_EmptyMaskFields() {
        // Arrange
        List<IFieldDescribe> emptyMaskFields = Lists.newArrayList();

        // Act
        List<IFieldDescribe> result = maskFieldLogicService.maskFieldRoleFilter(testUser, emptyMaskFields);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("掩码字段角色过滤 - null掩码字段列表")
    void testMaskFieldRoleFilter_NullMaskFields() {
        // Act & Assert - null参数会抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            maskFieldLogicService.maskFieldRoleFilter(testUser, null);
        });
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 用户为null")
    void testGetMaskFields_NullUser() {
        // Arrange
        Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);

        // Act & Assert
        assertThrows(Exception.class, () -> {
            maskFieldLogicService.getMaskFields(null, describeList, "owner-123");
        });
    }

    @Test
    @DisplayName("边界测试 - 描述列表为null")
    void testGetMaskFields_NullDescribeList() {
        // Act & Assert
        assertThrows(Exception.class, () -> {
            maskFieldLogicService.getMaskFields(testUser, null, "owner-123");
        });
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的掩码处理流程")
    void testCompleteMaskProcessingFlow() {
        // Arrange
        Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);
        List<String> maskFieldApiNames = Lists.newArrayList("test_field");
        // testUser是普通用户，不需要Mock

        // Act - 执行完整流程
        // 1. 获取掩码字段
        Map<String, List<IFieldDescribe>> maskFields = maskFieldLogicService.getMaskFields(
                testUser, describeList, "owner-123");
        assertNotNull(maskFields);

        // 2. 填充掩码字段值
        assertDoesNotThrow(() -> {
            maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe, 
                    maskFieldApiNames, testDataList);
        });

        // 3. 获取掩码字段类型
        List<String> maskFieldTypes = maskFieldLogicService.getMaskFieldTypes(testTenantId, "TestObject");
        assertNotNull(maskFieldTypes);
        assertFalse(maskFieldTypes.isEmpty());

        // 4. 解码掩码字段
        assertDoesNotThrow(() -> {
            maskFieldLogicService.decodeMaskFieldEncryptValue(testUser, testDataList, mockObjectDescribe);
        });
    }
}
