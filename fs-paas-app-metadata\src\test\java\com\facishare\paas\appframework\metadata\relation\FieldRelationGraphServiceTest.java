package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FieldRelationGraphServiceTest {

    @Mock(lenient = true)
    private DescribeLogicService describeLogicService;

    @InjectMocks
    private FieldRelationGraphService fieldRelationGraphService;

    private IObjectDescribe masterDescribe;
    private List<IObjectDescribe> detailDescribes;

    @BeforeEach
    void setUp() {
        masterDescribe = new ObjectDescribe();
        masterDescribe.setApiName("MasterObject");
        masterDescribe.setTenantId("74255");

        detailDescribes = Lists.newArrayList();
        IObjectDescribe detailDescribe = new ObjectDescribe();
        detailDescribe.setApiName("DetailObject");
        detailDescribe.setTenantId("74255");
        detailDescribes.add(detailDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildGraphWithOnlyLookupObjects方法的正常场景
     */
    @Test
    @DisplayName("构建仅查找对象图 - 正常场景")
    void testBuildGraphWithOnlyLookupObjects_正常场景() {
        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                masterDescribe, detailDescribes);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildGraphWithOnlyLookupObjects方法带参数的重载版本
     */
    @Test
    @DisplayName("构建仅查找对象图 - 带参数重载版本")
    void testBuildGraphWithOnlyLookupObjects_带参数重载版本() {
        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                masterDescribe, detailDescribes, true, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildGraphWithOnlyLookupObjects方法的完整参数版本
     */
    @Test
    @DisplayName("构建仅查找对象图 - 完整参数版本")
    void testBuildGraphWithOnlyLookupObjects_完整参数版本() {
        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                masterDescribe, detailDescribes, true, false, true, true);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildFullDependencyGraph方法的正常场景
     */
    @Test
    @DisplayName("构建完整依赖图 - 正常场景")
    void testBuildFullDependencyGraph_正常场景() {
        // 准备测试数据
        List<IObjectDescribe> associationDescribes = Lists.newArrayList();
        List<IObjectDescribe> relatedDescribes = Lists.newArrayList();

        // 配置Mock行为
        when(describeLogicService.findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class)))
                .thenReturn(associationDescribes);
        when(describeLogicService.findRelatedDescribesWithoutCopyIfGray(anyString(), anyString()))
                .thenReturn(relatedDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildFullDependencyGraph(
                Lists.newArrayList(), masterDescribe, false, true, false, true, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());

        // 验证Mock交互
        verify(describeLogicService).findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class));
        verify(describeLogicService).findRelatedDescribesWithoutCopyIfGray(anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildFullDependencyGraph方法的简化参数版本
     */
    @Test
    @DisplayName("构建完整依赖图 - 简化参数版本")
    void testBuildFullDependencyGraph_简化参数版本() {
        // 准备测试数据
        List<IObjectDescribe> associationDescribes = Lists.newArrayList();
        List<IObjectDescribe> relatedDescribes = Lists.newArrayList();

        // 配置Mock行为
        when(describeLogicService.findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class)))
                .thenReturn(associationDescribes);
        when(describeLogicService.findRelatedDescribesWithoutCopyIfGray(anyString(), anyString()))
                .thenReturn(relatedDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildFullDependencyGraph(
                Lists.newArrayList(), masterDescribe, false, true, false, true, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseFullDependencyGraph方法的正常场景
     */
    @Test
    @DisplayName("构建反向完整依赖图 - 正常场景")
    void testBuildReverseFullDependencyGraph_正常场景() {
        // 准备测试数据
        List<IObjectDescribe> associationDescribes = Lists.newArrayList();
        List<IObjectDescribe> relatedDescribes = Lists.newArrayList();

        // 配置Mock行为
        when(describeLogicService.findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class)))
                .thenReturn(associationDescribes);
        when(describeLogicService.findRelatedDescribesWithoutCopyIfGray(anyString(), anyString()))
                .thenReturn(relatedDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseFullDependencyGraph(
                masterDescribe, detailDescribes, false, true, false, true, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());

        // 验证Mock交互
        verify(describeLogicService, atLeastOnce()).findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseFullDependencyGraph方法在detailDescribes为空时的场景
     */
    @Test
    @DisplayName("构建反向完整依赖图 - detailDescribes为空")
    void testBuildReverseFullDependencyGraph_detailDescribes为空() {
        // 准备测试数据
        List<IObjectDescribe> emptyDetailDescribes = Lists.newArrayList();
        List<IObjectDescribe> associationDescribes = Lists.newArrayList();
        List<IObjectDescribe> relatedDescribes = Lists.newArrayList();

        // 配置Mock行为
        when(describeLogicService.findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class)))
                .thenReturn(associationDescribes);
        when(describeLogicService.findRelatedDescribesWithoutCopyIfGray(anyString(), anyString()))
                .thenReturn(relatedDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseFullDependencyGraph(
                masterDescribe, emptyDetailDescribes, false, true, false, true, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildDependencyGraph方法的正常场景
     */
    @Test
    @DisplayName("构建依赖图 - 正常场景")
    void testBuildDependencyGraph_正常场景() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);
        describes.addAll(detailDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(describes, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildDependencyGraph方法带graph参数的重载版本
     */
    @Test
    @DisplayName("构建依赖图 - 带graph参数重载版本")
    void testBuildDependencyGraph_带graph参数重载版本() {
        // 准备测试数据 - 先创建一个基础图，然后在此基础上构建
        List<IObjectDescribe> baseDescribes = Lists.newArrayList(masterDescribe);
        FieldRelationGraph existingGraph = fieldRelationGraphService.buildDependencyGraph(baseDescribes, false);

        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(
                existingGraph, describes, false);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseDependencyGraph方法的正常场景
     */
    @Test
    @DisplayName("构建反向依赖图 - 正常场景")
    void testBuildReverseDependencyGraph_正常场景() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);
        describes.addAll(detailDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(describes, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseDependencyGraph方法带includeQuoteField参数的重载版本
     */
    @Test
    @DisplayName("构建反向依赖图 - 带includeQuoteField参数")
    void testBuildReverseDependencyGraph_带includeQuoteField参数() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);
        describes.addAll(detailDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(describes, false, true);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseDependencyGraph方法的完整参数版本
     */
    @Test
    @DisplayName("构建反向依赖图 - 完整参数版本")
    void testBuildReverseDependencyGraph_完整参数版本() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);
        describes.addAll(detailDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(
                describes, false, true, true);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试传入null masterDescribe时的异常处理
     */
    @Test
    @DisplayName("异常处理 - masterDescribe为null")
    void testBuildGraphWithNullMasterDescribe() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            fieldRelationGraphService.buildGraphWithOnlyLookupObjects(null, detailDescribes);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试传入null detailDescribes时的正常处理
     */
    @Test
    @DisplayName("边界条件 - detailDescribes为null")
    void testBuildGraphWithNullDetailDescribes() {
        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                masterDescribe, null);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildGraphWithOnlyLookupObjects方法的不同参数组合
     */
    @Test
    @DisplayName("构建仅查找对象图 - 不同参数组合")
    void testBuildGraphWithOnlyLookupObjects_不同参数组合() {
        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildGraphWithOnlyLookupObjects(
                masterDescribe, detailDescribes, true, false, true, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseDependencyGraph方法的三参数版本
     */
    @Test
    @DisplayName("构建反向依赖图 - 三参数版本")
    void testBuildReverseDependencyGraph_三参数版本() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);
        describes.addAll(detailDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(describes, false, true);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseDependencyGraph方法的四参数版本
     */
    @Test
    @DisplayName("构建反向依赖图 - 四参数版本")
    void testBuildReverseDependencyGraph_四参数版本() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);
        describes.addAll(detailDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseDependencyGraph(describes, false, true, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildDependencyGraph方法的三参数版本
     */
    @Test
    @DisplayName("构建依赖图 - 三参数版本")
    void testBuildDependencyGraph_三参数版本() {
        // 准备测试数据
        List<IObjectDescribe> describes = Lists.newArrayList(masterDescribe);
        describes.addAll(detailDescribes);

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(describes, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildReverseFullDependencyGraph方法的完整参数版本
     */
    @Test
    @DisplayName("构建反向完整依赖图 - 完整参数版本")
    void testBuildReverseFullDependencyGraph_完整参数版本() {
        // 准备测试数据
        List<IObjectDescribe> emptyDetailDescribes = Lists.newArrayList();

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildReverseFullDependencyGraph(
                masterDescribe, emptyDetailDescribes, true, false, true, false, true);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件 - 空的描述列表
     */
    @Test
    @DisplayName("边界条件 - 空的描述列表")
    void testBuildDependencyGraph_空的描述列表() {
        // 准备测试数据
        List<IObjectDescribe> emptyDescribes = Lists.newArrayList();

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(emptyDescribes, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试性能场景 - 大量描述对象
     */
    @Test
    @DisplayName("性能测试 - 大量描述对象")
    void testBuildDependencyGraph_大量描述对象() {
        // 准备测试数据 - 创建多个描述对象
        List<IObjectDescribe> manyDescribes = Lists.newArrayList();
        for (int i = 0; i < 10; i++) {
            IObjectDescribe describe = mock(IObjectDescribe.class);
            when(describe.getApiName()).thenReturn("TestObject" + i);
            when(describe.getTenantId()).thenReturn("74255");
            manyDescribes.add(describe);
        }

        // 执行被测试方法
        FieldRelationGraph result = fieldRelationGraphService.buildDependencyGraph(manyDescribes, false);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDescribeMap());
    }
}
