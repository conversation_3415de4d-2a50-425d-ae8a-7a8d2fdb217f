package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutRuleService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isEnableCheckEnterpriseResourcesQuote;

@Slf4j
@Service("layoutRuleLogicService")
public class LayoutRuleLogicServiceImpl implements LayoutRuleLogicService {
    @Autowired
    private ILayoutRuleService layoutRuleService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private LayoutLogicService layoutLogicService;

    @Override
    public void createLayoutRule(User user, LayoutRuleInfo layoutRuleInfo) {
        List<LayoutRuleInfo> list = findLayoutRuleByDescribe(user, layoutRuleInfo.getObjectDescribeApiName());

        LayoutRuleExt.of(layoutRuleInfo).validatePageTypeRuleByLayout(list);

        list.add(layoutRuleInfo);
        validateFieldType(user, layoutRuleInfo, list);
        try {
            LayoutRuleExt layoutRuleExt = LayoutRuleExt.of(layoutRuleInfo);
            layoutRuleExt.fillSystemInfo(user);
            layoutRuleExt.enableRule();
            updateVersion(user, layoutRuleExt);
            layoutRuleService.create(user.getTenantId(), layoutRuleExt.getRule());
        } catch (MetadataServiceException e) {
            log.warn("Error in createLayoutRule layout rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    private void updateVersion(User user, LayoutRuleExt layoutRuleExt) {
        if (!layoutRuleExt.isPageTypeRule()) {
            return;
        }
        layoutLogicService.updateLayoutVersion(user, layoutRuleExt.getObjectDescribeApiName(), layoutRuleExt.getLayoutApiName());
    }

    @Override
    public void batchCreateRule(User user, List<LayoutRuleInfo> layoutRuleInfos) {
        if (CollectionUtils.empty(layoutRuleInfos)) {
            return;
        }
        try {
            layoutRuleInfos.forEach(x -> {
                LayoutRuleExt layoutRuleExt = LayoutRuleExt.of(x);
                layoutRuleExt.fillSystemInfo(user);
                updateVersion(user, layoutRuleExt);
            });
            layoutRuleService.batchCreate(user.getTenantId(), layoutRuleInfos);
        } catch (MetadataServiceException e) {
            log.warn("Error in batchCreate layout rule,tenantId:{},layoutRuleInfos:{}", user.getTenantId(), layoutRuleInfos, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    private void validateFieldType(User user, LayoutRuleInfo layoutRuleInfo, List<LayoutRuleInfo> list) {
        if ("page".equals(layoutRuleInfo.getType())) {
            return;
        }

        if (LayoutRuleExt.existCycle(list)) {
            throw new ValidateException(I18nMessage.of(I18NKey.LAYOUT_RULE_RING_FORMING, I18N.text(I18NKey.LAYOUT_RULE_RING_FORMING)));
        }

        validateLeafField(user, layoutRuleInfo.getObjectDescribeApiName(), Lists.newArrayList(layoutRuleInfo));
    }


    @Override
    public void updateLayoutRule(User user, LayoutRuleInfo layoutRuleInfo) {
        List<LayoutRuleInfo> list = findLayoutRuleByDescribe(user, layoutRuleInfo.getObjectDescribeApiName());
        list.removeIf(a -> Objects.equals(a.getApiName(), layoutRuleInfo.getApiName()));
        LayoutRuleExt.of(layoutRuleInfo).validatePageTypeRuleByLayout(list);
        list.add(layoutRuleInfo);
        validateFieldType(user, layoutRuleInfo, list);
        // 更新最后修改人
        layoutRuleInfo.setLastModifiedBy(user.getUserId());
        try {
            updateVersion(user, LayoutRuleExt.of(layoutRuleInfo));
            layoutRuleService.update(user.getTenantId(), layoutRuleInfo);
        } catch (MetadataServiceException e) {
            log.warn("Error in updateLayoutRule layout rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public void enableLayoutRule(User user, String apiName, String describeApiName) {
        try {
            if (isEnableCheckEnterpriseResourcesQuote(user.getTenantId())) {
                //布局规则列表
                List<LayoutRuleInfo> layoutRuleList = findLayoutRuleByDescribe(user, describeApiName);
                layoutRuleList.stream().filter(x -> Objects.isNull(x.getType())).forEach(x -> x.setType("field"));
                //布局规则列表
                List<String> layoutApiNames = layoutRuleList.stream().map(LayoutRuleInfo::getLayoutApiName).distinct().collect(Collectors.toList());
                Map<String, Layout> layoutMap = layoutLogicService.findLayoutByApiNames(user.getTenantId(), layoutApiNames, describeApiName);
                layoutRuleList.removeIf(x -> LayoutRuleExt.of(x).isPageTypeRule()
                        && layoutMap.containsKey(x.getLayoutApiName())
                        && LayoutExt.of(layoutMap.get(x.getLayoutApiName())).isDetailLayout());

                Map<String, Integer> layoutRuleCountMap = getLayoutRuleCountByLayoutApiName(layoutRuleList);
                checkLayoutRuleCount(user, layoutRuleCountMap);
            }
            LayoutRuleInfo layoutRuleInfo = findLayoutRuleByApiName(user, apiName);
            updateVersion(user, LayoutRuleExt.of(layoutRuleInfo));
            layoutRuleService.updateStatus(user.getTenantId(), apiName, true, buildActionContext(user));
        } catch (MetadataServiceException e) {
            log.warn("Error in enableLayoutRule layout rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    private Map<String, Integer> getLayoutRuleCountByLayoutApiName(List<LayoutRuleInfo> layoutRuleList) {
        HashMap<String, Integer> layoutRuleCountByLayoutApiNameMap = Maps.newHashMap();
        layoutRuleList.forEach(layoutRuleInfo -> {
            Integer count = layoutRuleCountByLayoutApiNameMap.getOrDefault(layoutRuleInfo.getLayoutApiName(), 0);
            layoutRuleCountByLayoutApiNameMap.put(layoutRuleInfo.getLayoutApiName(), ++count);
        });

        return layoutRuleCountByLayoutApiNameMap;
    }

    @Override
    public void disableLayoutRule(User user, String apiName) {
        try {
            LayoutRuleInfo layoutRuleInfo = findLayoutRuleByApiName(user, apiName);
            if(Objects.isNull(layoutRuleInfo)) {
                return;
            }
            updateVersion(user, LayoutRuleExt.of(layoutRuleInfo));
            layoutRuleService.updateStatus(user.getTenantId(), apiName, false, buildActionContext(user));
        } catch (MetadataServiceException e) {
            log.warn("Error in disable layout rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    private IActionContext buildActionContext(User user) {
        return ActionContextExt.of(user).getContext();
    }

    @Override
    public void deleteLayoutRule(User user, String apiName) {
        try {
            layoutRuleService.delete(user.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in delete layout rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public List<LayoutRuleInfo> findLayoutRuleByDescribe(User user, String describeApiName) {
        try {
            return layoutRuleService.findByDescribe(user.getTenantId(), describeApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in findLayoutRuleByDescribe, ei:{}, apiName:{}", user.getTenantId(), describeApiName, e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<LayoutRuleInfo> findLayoutRuleByLayout(User user, String layoutApiName) {
        try {
            return layoutRuleService.findByLayout(user.getTenantId(), layoutApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in findLayoutRuleByLayout, ei:{}, apiName:{}", user.getTenantId(), layoutApiName, e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<LayoutRuleInfo> findLayoutRuleByLayout(User user, String describeApiName, String layoutApiName) {
        Objects.requireNonNull(layoutApiName, "layoutApiName");
        Objects.requireNonNull(describeApiName, "describeApiName");
        try {
            List<LayoutRuleInfo> layoutRuleInfos = layoutRuleService.findByLayout(user.getTenantId(), layoutApiName);
            return CollectionUtils.nullToEmpty(layoutRuleInfos).stream()
                    .filter(it -> Objects.equals(describeApiName, it.getObjectDescribeApiName()))
                    .collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("Error in findLayoutRuleByLayout, ei:{}, apiName:{}", user.getTenantId(), layoutApiName, e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<LayoutRuleInfo> findValidLayoutRuleByLayout(User user, String layoutApiName) {
        List<LayoutRuleInfo> list = findLayoutRuleByLayout(user, layoutApiName);
        return list.stream().filter(a -> LayoutRuleExt.of(a).isEnabled()).collect(Collectors.toList());
    }

    @Override
    public List<LayoutRuleInfo> findValidLayoutRuleByLayout(User user, String describeApiName, String layoutApiName) {
        return findLayoutRuleByLayout(user, describeApiName, layoutApiName).stream()
                .filter(it -> LayoutRuleExt.of(it).isEnabled())
                .collect(Collectors.toList());
    }

    @Override
    public LayoutRuleInfo findLayoutRuleByApiName(User user, String layoutRuleApiName) {
        try {
            return layoutRuleService.findByApiNameAndTenantId(user.getTenantId(), layoutRuleApiName);
        } catch (MetadataServiceException e) {
            log.warn("Error in findLayoutRuleByApiName, ei:{}, ruleApiName:{}", user.getTenantId(), layoutRuleApiName, e);
        }
        return null;
    }

    @Override
    public void deleteLayoutRuleByLayout(User user, String describeApiName, String layoutApiName) {
        try {
            layoutRuleService.deleteByLayoutApiName(user.getTenantId(), describeApiName, layoutApiName);
        } catch (MetadataServiceException e) {
            log.warn("error in deleteLayoutRuleByLayout, ei:{}, apiName:{}, layuout:{}",
                    user.getTenantId(), describeApiName, layoutApiName, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public void deleteLayoutRuleByDescribeApiName(User user, String describeApiName) {
        try {
            IActionContext actionContext = new ActionContext();
            actionContext.setUserId(user.getUserId());
            actionContext.setEnterpriseId(user.getTenantId());
            layoutRuleService.deletedByDescribe(describeApiName, actionContext);
        } catch (MetadataServiceException e) {
            log.warn("error in deleteLayoutRuleByDescribeApiName, ei:{}, describeApiName:{}",
                    user.getTenantId(), describeApiName, e);
//            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public void checkLayoutRuleCount(User user, Map<String, Integer> layoutRuleCountMap) {
        TenantLicenseInfo licenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        for (String layoutApiName : layoutRuleCountMap.keySet()) {
            licenseInfo.checkLayoutRuleCount(layoutRuleCountMap.getOrDefault(layoutApiName, 0));
        }
    }

    private void validateLeafField(User user, String objectDescribeApiName, List<LayoutRuleInfo> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectDescribeApiName);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        list.forEach(a -> {
            List<LayoutRuleExt.FieldBranch> mainBranches = LayoutRuleExt.of(a).getMainBranches();
            if (CollectionUtils.empty(mainBranches)) {
                return;
            }
            mainBranches.forEach(b -> b.getBranches().forEach(c -> {
                LayoutRuleExt.RuleResult result = c.getResult();
                List<LayoutRuleExt.RequiredFieldResult> requiredFieldList = result.getRequiredFieldList();
                requiredFieldList.forEach(d -> checkFieldValid(describeExt, a, d.getFieldApiName()));
                List<LayoutRuleExt.ShowFieldResult> showFieldList = result.getShowFieldList();
                showFieldList.forEach(e -> checkFieldValid(describeExt, a, e.getFieldApiName()));
            }));
        });
    }

    private void checkFieldValid(ObjectDescribeExt describeExt, LayoutRuleInfo layoutRuleInfo, String fieldApiName) {
        Optional<IFieldDescribe> field = describeExt.getFieldDescribeSilently(fieldApiName);
        if (!field.isPresent()) {
            throw new ValidateException(I18nMessage.of(I18NKey.FIELD_DELETED_IN_LAYOUT, I18N.text(I18NKey.FIELD_DELETED_IN_LAYOUT, layoutRuleInfo.getLabel()), layoutRuleInfo.getLabel()));
        }

        IFieldDescribe fieldDescribe = field.get();
        if (Objects.equals(fieldDescribe.isActive(), Boolean.FALSE)) {
            throw new ValidateException(I18nMessage.of(I18NKey.FIELD_INVALID_IN_LAYOUT,
                    I18N.text(I18NKey.FIELD_INVALID_IN_LAYOUT, layoutRuleInfo.getLabel(), fieldDescribe.getLabel()),
                    I18nMessage.of(layoutRuleInfo.getLabel()), I18nMessageExt.buildFieldLabel(fieldDescribe)));
        }
    }

    @Override
    public boolean layoutRuleValidate(User user, List<DescribeLayoutValidateModel> describeLayoutValidateModels) {
        for (DescribeLayoutValidateModel describeLayoutValidateModel : describeLayoutValidateModels) {
            String objectApiName = describeLayoutValidateModel.getObjectApiName();
            Map<String, Long> layoutAndModifiedTimeMap = CollectionUtils.nullToEmpty(describeLayoutValidateModel.getLayoutRuleValidateInfos()).stream()
                    .collect(Collectors.toMap(DescribeLayoutValidateModel.LayoutRuleValidateInfo::getLayoutApiName,
                            DescribeLayoutValidateModel.LayoutRuleValidateInfo::getLayoutLastModifiedTime, (x, y) -> y));
            Map<String, Layout> layouts = layoutLogicService.findLayoutByApiNames(user.getTenantId(), Lists.newArrayList(layoutAndModifiedTimeMap.keySet()), objectApiName);
            if (BooleanUtils.isTrue(describeLayoutValidateModel.getIsMaster())) {
                List<DescribeLayoutValidateModel.LayoutRuleValidateInfo> layoutRuleValidateInfos = describeLayoutValidateModel.getLayoutRuleValidateInfos();
                if (layoutRuleValidateInfos.size() == 1) {
                    Layout layout = layouts.get(layoutRuleValidateInfos.get(0).getLayoutApiName());
                    if (Objects.isNull(layout)) {
                        log.warn("Layout not found for master object, objectApiName: {}, layoutApiName: {}",
                                objectApiName, layoutRuleValidateInfos.get(0).getLayoutApiName());
                        return false;
                    }
                    Integer layoutVersion = describeLayoutValidateModel.getLayoutVersion();
                    if (Objects.nonNull(layoutVersion) && !Objects.equals(layoutVersion, layout.getVersion())) {
                        log.warn("Version mismatch for master object, objectApiName: {}, versionInRequest: {}, versionInDB: {}",
                                objectApiName, layoutVersion, layout.getVersion());
                        return false;
                    }
                }
            }
            boolean layoutUpdateRequired = isLayoutUpdateRequired(layouts, layoutAndModifiedTimeMap);
            if (layoutUpdateRequired) {
                log.info("Layout update required for object: {}, validation failed", objectApiName);
                return false;
            }

            boolean ruleTimeValidationFailed = validateLayoutRuleLastModifiedTime(user, describeLayoutValidateModel, objectApiName);
            if (ruleTimeValidationFailed) {
                log.info("Layout rule time validation failed for object: {}", objectApiName);
                return false;
            }
        }
        return true;
    }

    private boolean isLayoutUpdateRequired(Map<String, Layout> layouts, Map<String, Long> layoutAndModifiedTimeMap) {
        if (layouts.size() != layoutAndModifiedTimeMap.size() || !layoutAndModifiedTimeMap.keySet().containsAll(layouts.keySet())) {
            log.info("Layout update required: size mismatch or missing keys. DB size: {}, Request size: {}",
                    layouts.size(), layoutAndModifiedTimeMap.size());
            return true;
        }

        for (Map.Entry<String, Layout> entry : layouts.entrySet()) {
            String layoutApiName = entry.getKey();
            Layout layout = entry.getValue();
            Long lastModifiedTime = layoutAndModifiedTimeMap.get(layoutApiName);

            if (!Objects.equals(layout.getLastModifiedTime(), lastModifiedTime)) {
                log.info("Layout update required for layoutApiName: {}. DB time: {}, Request time: {}",
                        layoutApiName, layout.getLastModifiedTime(), lastModifiedTime);
                return true;
            }
        }

        return false;
    }

    /**
     * 验证布局规则的最后修改时间
     *
     * @param user                        用户信息
     * @param describeLayoutValidateModel 布局验证模型
     * @param objectApiName               对象API名称
     * @return true-需要更新缓存(验证不通过)，false-缓存有效(验证通过)
     */
    private boolean validateLayoutRuleLastModifiedTime(User user, DescribeLayoutValidateModel describeLayoutValidateModel, String objectApiName) {

        List<DescribeLayoutValidateModel.LayoutRuleValidateInfo> layoutRuleValidateInfos =
                CollectionUtils.nullToEmpty(describeLayoutValidateModel.getLayoutRuleValidateInfos());
        return layoutRuleValidateInfos.stream()
                .anyMatch(layoutInfo -> {
                    String layoutApiName = layoutInfo.getLayoutApiName();
                    Map<String, LayoutRuleInfo> dbRules = getDbRules(user, objectApiName, layoutInfo);
                    Map<String, LayoutRuleInfo> paramRules = getParamRules(layoutInfo);
                    if (isRuleUpdateRequired(dbRules, paramRules)) {
                        log.info("Rule update required for layout: {}, object: {}, reason: size mismatch or missing keys",
                                layoutApiName, objectApiName);
                        return true;
                    }
                    boolean timeMismatch = isRuleTimeMismatch(dbRules, paramRules);
                    if (timeMismatch) {
                        log.info("Rule time mismatch detected for layout: {}, object: {}", layoutApiName, objectApiName);
                    }

                    return timeMismatch;
                });
    }

    private Map<String, LayoutRuleInfo> getDbRules(User user, String objectApiName, DescribeLayoutValidateModel.LayoutRuleValidateInfo layoutInfo) {
        List<LayoutRuleInfo> rules = findValidLayoutRuleByLayout(user, objectApiName, layoutInfo.getLayoutApiName());
        return rules.stream()
                .filter(rule -> !LayoutRuleExt.of(rule).isPageTypeRule())
                .collect(Collectors.toMap(LayoutRuleInfo::getApiName, x -> x));
    }

    private Map<String, LayoutRuleInfo> getParamRules(DescribeLayoutValidateModel.LayoutRuleValidateInfo layoutInfo) {
        List<LayoutRuleInfo> rules = CollectionUtils.nullToEmpty(layoutInfo.getLayoutRuleInfos())
                .stream()
                .map(x -> LayoutRuleExt.of(x).getRuleInfo())
                .collect(Collectors.toList());
        return rules.stream()
                .collect(Collectors.toMap(LayoutRuleInfo::getApiName, x -> x));
    }

    private boolean isRuleUpdateRequired(Map<String, LayoutRuleInfo> dbRules, Map<String, LayoutRuleInfo> paramRules) {
        boolean updateRequired = dbRules.size() != paramRules.size() || !dbRules.keySet().containsAll(paramRules.keySet());
        if (updateRequired && dbRules.size() != paramRules.size()) {
            log.info("Rule update required: size mismatch. DB size: {}, Request size: {}",
                    dbRules.size(), paramRules.size());
        }
        return updateRequired;
    }

    private boolean isRuleTimeMismatch(Map<String, LayoutRuleInfo> dbRules, Map<String, LayoutRuleInfo> paramRules) {
        return dbRules.entrySet().stream().anyMatch(entry -> {
            String ruleApiName = entry.getKey();
            LayoutRuleInfo dbRule = entry.getValue();
            LayoutRuleInfo paramRule = paramRules.get(ruleApiName);
            if (Objects.isNull(paramRule)) {
                log.info("Rule not found in request: {}", ruleApiName);
                return true;
            }
            Long dbTime = dbRule.getLastModifiedTime();
            Long paramTime = paramRule.getLastModifiedTime();
            boolean mismatch = !Objects.equals(dbTime, paramTime);
            if (mismatch) {
                log.info("Rule time mismatch for rule: {}, DB time: {}, Request time: {}",
                        ruleApiName, dbTime, paramTime);
            }
            return mismatch;
        });
    }
}
