package com.facishare.paas.appframework.metadata.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for CRMRestServiceCodec
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CRMRestServiceCodec 单元测试")
class CRMRestServiceCodecJUnit5Test {

    private CRMRestServiceCodec codec;

    @BeforeEach
    void setUp() {
        codec = new CRMRestServiceCodec();
    }

    // ==================== encodeArg 方法测试 ====================

    @Test
    @DisplayName("编码参数 - 字符串对象")
    void testEncodeArg_StringObject() {
        // Arrange
        String obj = "Hello World";
        String expectedJson = JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        byte[] expectedBytes = expectedJson.getBytes(StandardCharsets.UTF_8);

        // Act
        byte[] result = codec.encodeArg(obj);

        // Assert
        assertNotNull(result);
        assertArrayEquals(expectedBytes, result);
        assertEquals(expectedJson, new String(result, StandardCharsets.UTF_8));
    }

    @Test
    @DisplayName("编码参数 - Map对象")
    void testEncodeArg_MapObject() {
        // Arrange
        Map<String, Object> obj = Maps.newHashMap();
        obj.put("name", "John");
        obj.put("age", 30);
        obj.put("active", true);
        obj.put("address", null); // 测试null值处理
        
        String expectedJson = JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        byte[] expectedBytes = expectedJson.getBytes(StandardCharsets.UTF_8);

        // Act
        byte[] result = codec.encodeArg(obj);

        // Assert
        assertNotNull(result);
        assertArrayEquals(expectedBytes, result);
        
        // 验证包含null值
        String resultJson = new String(result, StandardCharsets.UTF_8);
        assertTrue(resultJson.contains("\"address\":null"));
    }

    @Test
    @DisplayName("编码参数 - List对象")
    void testEncodeArg_ListObject() {
        // Arrange
        List<String> obj = Lists.newArrayList("item1", "item2", "item3");
        String expectedJson = JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        byte[] expectedBytes = expectedJson.getBytes(StandardCharsets.UTF_8);

        // Act
        byte[] result = codec.encodeArg(obj);

        // Assert
        assertNotNull(result);
        assertArrayEquals(expectedBytes, result);
        assertEquals(expectedJson, new String(result, StandardCharsets.UTF_8));
    }

    @Test
    @DisplayName("编码参数 - null对象")
    void testEncodeArg_NullObject() {
        // Arrange
        Object obj = null;
        String expectedJson = JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        byte[] expectedBytes = expectedJson.getBytes(StandardCharsets.UTF_8);

        // Act
        byte[] result = codec.encodeArg(obj);

        // Assert
        assertNotNull(result);
        assertArrayEquals(expectedBytes, result);
        assertEquals("null", new String(result, StandardCharsets.UTF_8));
    }

    @Test
    @DisplayName("编码参数 - 复杂对象")
    void testEncodeArg_ComplexObject() {
        // Arrange
        TestObject obj = new TestObject();
        obj.setName("Test");
        obj.setValue(100);
        obj.setActive(true);
        obj.setDescription(null);
        
        String expectedJson = JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        byte[] expectedBytes = expectedJson.getBytes(StandardCharsets.UTF_8);

        // Act
        byte[] result = codec.encodeArg(obj);

        // Assert
        assertNotNull(result);
        assertArrayEquals(expectedBytes, result);
        
        // 验证包含null值
        String resultJson = new String(result, StandardCharsets.UTF_8);
        assertTrue(resultJson.contains("\"description\":null"));
    }

    // ==================== decodeResult 方法测试 ====================

    @Test
    @DisplayName("解码结果 - String类型")
    void testDecodeResult_StringClass() throws Exception {
        // Arrange
        int statusCode = 200;
        Map<String, List<String>> headers = Maps.newHashMap();
        String originalString = "Hello World";
        byte[] bytes = originalString.getBytes("UTF-8");
        Class<String> clazz = String.class;

        // Act
        String result = codec.decodeResult(statusCode, headers, bytes, clazz);

        // Assert
        assertNotNull(result);
        assertEquals(originalString, result);
    }

    @Test
    @DisplayName("解码结果 - Map类型")
    void testDecodeResult_MapClass() throws Exception {
        // Arrange
        int statusCode = 200;
        Map<String, List<String>> headers = Maps.newHashMap();
        
        Map<String, Object> originalMap = Maps.newHashMap();
        originalMap.put("name", "John");
        originalMap.put("age", 30);
        originalMap.put("active", true);
        
        String json = JSON.toJSONString(originalMap);
        byte[] bytes = json.getBytes("UTF-8");
        Class<Map> clazz = Map.class;

        // Act
        Map result = codec.decodeResult(statusCode, headers, bytes, clazz);

        // Assert
        assertNotNull(result);
        assertEquals("John", result.get("name"));
        assertEquals(30, result.get("age"));
        assertEquals(true, result.get("active"));
    }

    @Test
    @DisplayName("解码结果 - 自定义对象类型")
    void testDecodeResult_CustomObjectClass() throws Exception {
        // Arrange
        int statusCode = 200;
        Map<String, List<String>> headers = Maps.newHashMap();
        
        TestObject originalObject = new TestObject();
        originalObject.setName("Test");
        originalObject.setValue(100);
        originalObject.setActive(true);
        originalObject.setDescription("Test Description");
        
        String json = JSON.toJSONString(originalObject);
        byte[] bytes = json.getBytes("UTF-8");
        Class<TestObject> clazz = TestObject.class;

        // Act
        TestObject result = codec.decodeResult(statusCode, headers, bytes, clazz);

        // Assert
        assertNotNull(result);
        assertEquals("Test", result.getName());
        assertEquals(100, result.getValue());
        assertTrue(result.isActive());
        assertEquals("Test Description", result.getDescription());
    }

    @Test
    @DisplayName("解码结果 - 空字节数组")
    void testDecodeResult_EmptyBytes() throws Exception {
        // Arrange
        int statusCode = 200;
        Map<String, List<String>> headers = Maps.newHashMap();
        byte[] bytes = "".getBytes("UTF-8");
        Class<String> clazz = String.class;

        // Act
        String result = codec.decodeResult(statusCode, headers, bytes, clazz);

        // Assert
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    @DisplayName("解码结果 - 无效JSON格式")
    void testDecodeResult_InvalidJson() {
        // Arrange
        int statusCode = 200;
        Map<String, List<String>> headers = Maps.newHashMap();
        byte[] bytes = "invalid json".getBytes(StandardCharsets.UTF_8);
        Class<Map> clazz = Map.class;

        // Act & Assert
        assertThrows(MetaDataException.class, () -> {
            codec.decodeResult(statusCode, headers, bytes, clazz);
        });
    }

    @Test
    @DisplayName("解码结果 - 类型不匹配")
    void testDecodeResult_TypeMismatch() {
        // Arrange
        int statusCode = 200;
        Map<String, List<String>> headers = Maps.newHashMap();
        String json = "{\"name\":\"John\",\"age\":30}";
        byte[] bytes = json.getBytes(StandardCharsets.UTF_8);
        Class<List> clazz = List.class; // 期望List但实际是Map

        // Act & Assert
        assertThrows(MetaDataException.class, () -> {
            codec.decodeResult(statusCode, headers, bytes, clazz);
        });
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 极大字符串编码")
    void testEdgeCase_LargeStringEncoding() {
        // Arrange
        StringBuilder largeString = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            largeString.append("Large string content ").append(i).append(" ");
        }
        String obj = largeString.toString();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            byte[] result = codec.encodeArg(obj);
            assertNotNull(result);
            assertTrue(result.length > 0);
        });
    }

    @Test
    @DisplayName("边界测试 - 特殊字符处理")
    void testEdgeCase_SpecialCharacters() throws Exception {
        // Arrange
        String specialString = "特殊字符测试: 中文, 日本語, العربية, 🚀📝✨, quotes, apostrophes, backslashes";

        // Act
        byte[] encoded = codec.encodeArg(specialString);
        String decoded = codec.decodeResult(200, Maps.newHashMap(), encoded, String.class);

        // Assert
        assertNotNull(encoded);
        assertNotNull(decoded);
        // 对于String类型，FastJSON会保留JSON格式的引号，所以需要去掉引号比较
        String expectedDecoded = "\"" + specialString + "\"";
        assertEquals(expectedDecoded, decoded);
    }

    @Test
    @DisplayName("边界测试 - 不同状态码处理")
    void testEdgeCase_DifferentStatusCodes() throws Exception {
        // Arrange
        String json = "{\"message\":\"success\"}";
        byte[] bytes = json.getBytes("UTF-8");
        Map<String, List<String>> headers = Maps.newHashMap();

        // Act & Assert - 不同状态码都应该正常处理
        int[] statusCodes = {200, 201, 400, 404, 500};
        for (int statusCode : statusCodes) {
            Map result = codec.decodeResult(statusCode, headers, bytes, Map.class);
            assertNotNull(result);
            assertEquals("success", result.get("message"));
        }
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的编码解码流程")
    void testIntegration_CompleteEncodeDecodeFlow() throws Exception {
        // Arrange - 模拟真实业务场景
        TestObject originalObject = new TestObject();
        originalObject.setName("Business Object");
        originalObject.setValue(12345);
        originalObject.setActive(true);
        originalObject.setDescription("This is a business object for testing");

        // Act - 执行完整的编码解码流程
        // 1. 编码对象
        byte[] encoded = codec.encodeArg(originalObject);
        
        // 2. 解码回对象
        TestObject decoded = codec.decodeResult(200, Maps.newHashMap(), encoded, TestObject.class);

        // Assert - 验证完整流程结果
        assertNotNull(encoded, "编码结果不应为null");
        assertNotNull(decoded, "解码结果不应为null");
        
        // 验证对象属性完全一致
        assertEquals(originalObject.getName(), decoded.getName(), "名称应该一致");
        assertEquals(originalObject.getValue(), decoded.getValue(), "值应该一致");
        assertEquals(originalObject.isActive(), decoded.isActive(), "活跃状态应该一致");
        assertEquals(originalObject.getDescription(), decoded.getDescription(), "描述应该一致");
        
        // 验证编码格式
        String encodedJson = new String(encoded, StandardCharsets.UTF_8);
        assertTrue(encodedJson.contains("\"name\":\"Business Object\""), "编码JSON应包含名称");
        assertTrue(encodedJson.contains("\"value\":12345"), "编码JSON应包含值");
        assertTrue(encodedJson.contains("\"active\":true"), "编码JSON应包含活跃状态");
        assertTrue(encodedJson.contains("\"description\":\"This is a business object for testing\""), "编码JSON应包含描述");
    }

    // ==================== 测试辅助类 ====================

    public static class TestObject {
        private String name;
        private int value;
        private boolean active;
        private String description;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public int getValue() { return value; }
        public void setValue(int value) { this.value = value; }
        
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
}
