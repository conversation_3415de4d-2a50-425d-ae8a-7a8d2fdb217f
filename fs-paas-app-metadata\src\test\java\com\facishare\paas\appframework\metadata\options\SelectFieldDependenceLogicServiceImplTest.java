package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.options.FieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicServiceImpl;
// import com.facishare.paas.appframework.metadata.options.test.OptionsTestBase;
import com.facishare.paas.appframework.metadata.reference.RefFieldService;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldDependence;
// import com.facishare.paas.appframework.distributed.lock.DistributedLockService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SelectFieldDependenceLogicServiceImpl的单元测试
 * 
 * 重构后的版本，采用类级别统一管理静态Mock，避免复杂的try-with-resources嵌套
 * 
 * 测试覆盖的主要方法：
 * - create - 创建字段依赖
 * - update - 更新字段依赖
 * - deleted - 删除字段依赖
 * - findChildFields - 查找子字段
 * - findAll - 查找所有依赖
 * - findFieldDependenceWitchObjectApiName - 根据对象API名称查找字段依赖
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class SelectFieldDependenceLogicServiceImplTest {
    
    @Mock
    private FieldDependenceLogicService fieldDependenceLogicService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private RefFieldService refFieldService;

    @Mock
    private com.facishare.paas.metadata.api.service.IDistributedLockService distributedLockService;

    @Mock
    private com.facishare.paas.metadata.service.impl.MetadataTransactionService metadataTransactionService;

    @InjectMocks
    private SelectFieldDependenceLogicServiceImpl selectFieldDependenceLogicService;
    
    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String DESCRIBE_API_NAME = "TestObject";
    private static final String PARENT_FIELD_API_NAME = "parentField";
    private static final String CHILD_FIELD_API_NAME = "childField";
    
    // Mock对象
    private User testUser;
    private IObjectDescribe mockObjectDescribe;
    private IFieldDescribe mockParentField;
    private SelectOne mockChildField; // 修改为SelectOne类型，因为findChildFields方法期望SelectOne
    private SelectOne mockSelectOne;
    private SelectFieldDependence testSelectFieldDependence;
    private MtFieldDependence mockMtFieldDependence;
    
    // 静态Mock管理 - 类级别
    private static MockedStatic<UdobjGrayConfig> staticMockedUdobjGrayConfig;
    private static MockedStatic<ObjectDescribeExt> staticMockedObjectDescribeExt;
    private static MockedStatic<FieldDescribeExt> staticMockedFieldDescribeExt;
    
    @BeforeAll
    static void setUpClass() {
        // 在类级别初始化静态Mock，避免冲突
        staticMockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class);
        staticMockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(anyString(), anyString())).thenReturn(false);
        
        // 初始化ObjectDescribeExt静态Mock
        staticMockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);

        // 初始化FieldDescribeExt静态Mock
        staticMockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
    }
    
    @AfterAll
    static void tearDownClass() {
        // 在类级别清理静态Mock
        if (staticMockedUdobjGrayConfig != null) {
            staticMockedUdobjGrayConfig.close();
        }
        if (staticMockedObjectDescribeExt != null) {
            staticMockedObjectDescribeExt.close();
        }
        if (staticMockedFieldDescribeExt != null) {
            staticMockedFieldDescribeExt.close();
        }
    }
    
    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = User.systemUser(TENANT_ID);
        
        // 创建Mock对象
        mockObjectDescribe = mock(IObjectDescribe.class);
        mockParentField = mock(IFieldDescribe.class);
        mockChildField = mock(SelectOne.class); // 修改为SelectOne类型
        mockSelectOne = mock(SelectOne.class);
        mockMtFieldDependence = mock(MtFieldDependence.class);
        
        // 创建测试数据 - SelectFieldDependence是不可变对象，使用构造函数
        testSelectFieldDependence = new SelectFieldDependence(DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME, Collections.emptyList());
        
        // 配置基础Mock行为
        when(mockObjectDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(mockObjectDescribe.getTenantId()).thenReturn(TENANT_ID);
        when(mockParentField.getApiName()).thenReturn(PARENT_FIELD_API_NAME);
        when(mockChildField.getApiName()).thenReturn(CHILD_FIELD_API_NAME);
        // 为SelectOne类型的mockChildField添加特定配置
        when(mockChildField.getCascadeParentApiName()).thenReturn(PARENT_FIELD_API_NAME);
        when(mockSelectOne.getApiName()).thenReturn(CHILD_FIELD_API_NAME);
        
        // 配置DescribeLogicService的基础行为
        when(describeLogicService.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);
        when(describeLogicService.findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockObjectDescribe);

        // 配置DistributedLockService的基础行为 - advisoryTransactionalLock是void方法
        doNothing().when(distributedLockService).advisoryTransactionalLock(anyString(), anyString());

        // 配置MetadataTransactionService的基础行为
        try {
            when(metadataTransactionService.executeWithOutMetadataTransaction(any())).thenAnswer(invocation -> {
                // 直接执行传入的Supplier
                return ((java.util.function.Supplier<?>) invocation.getArgument(0)).get();
            });
        } catch (Exception e) {
            // 处理可能的异常
        }
        
        // 配置ObjectDescribeExt的默认行为
        ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
        when(mockDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(false);
        when(mockDescribeExt.getSelectOneFields()).thenReturn(Arrays.asList(mockSelectOne));
        when(mockDescribeExt.getFieldDescribeSilently(PARENT_FIELD_API_NAME)).thenReturn(Optional.of(mockParentField));
        when(mockDescribeExt.getFieldDescribeSilently(CHILD_FIELD_API_NAME)).thenReturn(Optional.of(mockChildField));

        // 配置stream()方法以支持findChildFields方法 - 返回IFieldDescribe流
        when(mockDescribeExt.stream()).thenReturn(Arrays.asList((IFieldDescribe)mockParentField, (IFieldDescribe)mockChildField).stream());

        staticMockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class))).thenReturn(mockDescribeExt);

        // 配置mockObjectDescribe的getFieldDescribe方法，支持SelectFieldDependence.getParentField()
        when(mockObjectDescribe.getFieldDescribe(PARENT_FIELD_API_NAME)).thenReturn(mockParentField);
        when(mockObjectDescribe.getFieldDescribe(CHILD_FIELD_API_NAME)).thenReturn(mockChildField);

        // 配置FieldDescribeExt的静态Mock
        FieldDescribeExt mockParentFieldExt = mock(FieldDescribeExt.class);
        FieldDescribeExt mockChildFieldExt = mock(FieldDescribeExt.class);

        when(mockParentFieldExt.isCascadeParentField()).thenReturn(true);
        when(mockParentFieldExt.isGeneralOptions()).thenReturn(true);
        when(mockParentFieldExt.getFieldDescribe()).thenReturn(mockParentField);
        when(mockParentFieldExt.getApiName()).thenReturn(PARENT_FIELD_API_NAME);

        when(mockChildFieldExt.isCascadeChildField()).thenReturn(true);
        when(mockChildFieldExt.isGeneralOptions()).thenReturn(true);
        when(mockChildFieldExt.getFieldDescribe()).thenReturn(mockChildField); // mockChildField现在是SelectOne类型
        when(mockChildFieldExt.getApiName()).thenReturn(CHILD_FIELD_API_NAME);
        // 默认配置为create场景（级联父字段列表为空）
        when(mockChildFieldExt.getCascadeParentApiNames()).thenReturn(Collections.emptyList());

        staticMockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockParentField)).thenReturn(mockParentFieldExt);
        staticMockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockChildField)).thenReturn(mockChildFieldExt);
    }
    
    /**
     * 测试create方法 - 正常场景
     */
    @Test
    @DisplayName("测试create方法 - 正常场景")
    void testCreate_Success() {
        // Arrange
        when(describeLogicService.updateFieldDescribe(eq(testUser), eq(mockObjectDescribe), anyList()))
                .thenReturn(mockObjectDescribe);
        
        // Act
        assertDoesNotThrow(() -> {
            selectFieldDependenceLogicService.create(testUser, testSelectFieldDependence);
        }, "创建字段依赖不应抛出异常");
        
        // Assert
        verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(describeLogicService, times(1))
                .updateFieldDescribe(eq(testUser), eq(mockObjectDescribe), anyList());
    }
    
    /**
     * 测试create方法 - 公共对象异常
     */
    @Test
    @DisplayName("测试create方法 - 公共对象不支持修改字段依赖")
    void testCreate_ThrowsValidateExceptionForPublicObject() {
        // Arrange - 配置为公共对象
        ObjectDescribeExt mockPublicDescribeExt = mock(ObjectDescribeExt.class);
        when(mockPublicDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(true);
        staticMockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                .thenReturn(mockPublicDescribeExt);
        
        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            selectFieldDependenceLogicService.create(testUser, testSelectFieldDependence);
        }, "公共对象不支持修改字段依赖应该抛出ValidateException");
        
        // 恢复默认Mock行为
        ObjectDescribeExt defaultMockDescribeExt = mock(ObjectDescribeExt.class);
        when(defaultMockDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(false);
        staticMockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                .thenReturn(defaultMockDescribeExt);
    }
    
    /**
     * 测试update方法 - 正常场景
     */
    @Test
    @DisplayName("测试update方法 - 正常场景")
    void testUpdate_Success() {
        // Arrange
        when(describeLogicService.updateFieldDescribe(eq(testUser), eq(mockObjectDescribe), anyList()))
                .thenReturn(mockObjectDescribe);

        // 为update场景临时修改现有Mock的行为：子字段必须已经有级联父字段设置
        FieldDescribeExt mockChildFieldExtForUpdate = mock(FieldDescribeExt.class);
        when(mockChildFieldExtForUpdate.isCascadeChildField()).thenReturn(true);
        when(mockChildFieldExtForUpdate.isGeneralOptions()).thenReturn(true);
        when(mockChildFieldExtForUpdate.getFieldDescribe()).thenReturn(mockChildField);
        when(mockChildFieldExtForUpdate.getApiName()).thenReturn(CHILD_FIELD_API_NAME);
        // 关键：为update场景配置级联父字段列表包含父字段API名称
        when(mockChildFieldExtForUpdate.getCascadeParentApiNames()).thenReturn(Arrays.asList(PARENT_FIELD_API_NAME));

        // 临时修改静态Mock的行为
        staticMockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockChildField)).thenReturn(mockChildFieldExtForUpdate);

        // Act
        assertDoesNotThrow(() -> {
            selectFieldDependenceLogicService.update(testUser, testSelectFieldDependence);
        }, "更新字段依赖不应抛出异常");

        // Assert
        verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);

        // 恢复默认Mock行为（为后续测试做准备）
        FieldDescribeExt mockChildFieldExtDefault = mock(FieldDescribeExt.class);
        when(mockChildFieldExtDefault.isCascadeChildField()).thenReturn(true);
        when(mockChildFieldExtDefault.isGeneralOptions()).thenReturn(true);
        when(mockChildFieldExtDefault.getFieldDescribe()).thenReturn(mockChildField);
        when(mockChildFieldExtDefault.getApiName()).thenReturn(CHILD_FIELD_API_NAME);
        when(mockChildFieldExtDefault.getCascadeParentApiNames()).thenReturn(Collections.emptyList());
        staticMockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockChildField)).thenReturn(mockChildFieldExtDefault);
    }
    
    /**
     * 测试deleted方法 - 正常场景
     */
    @Test
    @DisplayName("测试deleted方法 - 正常场景")
    void testDeleted_Success() {
        // Arrange
        when(describeLogicService.updateFieldDescribe(eq(testUser), eq(mockObjectDescribe), anyList()))
                .thenReturn(mockObjectDescribe);
        
        // Act
        assertDoesNotThrow(() -> {
            selectFieldDependenceLogicService.deleted(testUser, DESCRIBE_API_NAME,
                    PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
        }, "删除字段依赖不应抛出异常");
        
        // Assert
        verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(fieldDependenceLogicService, times(1))
                .deleted(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
    }
    
    /**
     * 测试findChildFields方法 - 正常场景
     */
    @Test
    @DisplayName("测试findChildFields方法 - 正常场景")
    void testFindChildFields_Success() {
        // Act
        List<SelectOne> result = selectFieldDependenceLogicService
                .findChildFields(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME);

        // Assert
        assertNotNull(result, "查询结果不应为null");
        assertEquals(1, result.size(), "应该返回1个子字段");
        assertEquals(CHILD_FIELD_API_NAME, result.get(0).getApiName(), "子字段API名称应该匹配");

        verify(describeLogicService, times(1))
                .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
    }
    
    /**
     * 测试findFieldDependenceWitchObjectApiName方法 - 正常场景
     */
    @Test
    @DisplayName("测试findFieldDependenceWitchObjectApiName方法 - 正常场景")
    void testFindFieldDependenceWitchObjectApiName_Success() {
        // Act
        List<SelectFieldDependence> result = selectFieldDependenceLogicService
                .findFieldDependenceWitchObjectApiName(testUser, DESCRIBE_API_NAME);
        
        // Assert
        assertNotNull(result, "查询结果不应为null");
        
        verify(describeLogicService, times(1))
                .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
    }
    
    /**
     * 测试findAll方法 - 正常场景
     */
    @Test
    @DisplayName("测试findAll方法 - 正常场景")
    void testFindAll_Success() {
        // Arrange
        when(fieldDependenceLogicService.findAll(testUser, DESCRIBE_API_NAME))
                .thenReturn(Arrays.asList(mockMtFieldDependence));

        // Act
        List<SelectFieldDependence> result = selectFieldDependenceLogicService
                .findAll(testUser, DESCRIBE_API_NAME);

        // Assert
        assertNotNull(result, "查询结果不应为null");

        verify(describeLogicService, times(1))
                .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
        verify(fieldDependenceLogicService, times(1)).findAll(testUser, DESCRIBE_API_NAME);
    }

    /**
     * 测试findChildFields方法 - 父字段不存在异常
     */
    @Test
    @DisplayName("测试findChildFields方法 - 父字段不存在应抛出异常")
    void testFindChildFields_ParentFieldNotFound() {
        // Arrange - 配置父字段不存在
        ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
        when(mockDescribeExt.getFieldDescribeSilently(PARENT_FIELD_API_NAME))
                .thenReturn(Optional.empty());
        staticMockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                .thenReturn(mockDescribeExt);

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            selectFieldDependenceLogicService.findChildFields(testUser, DESCRIBE_API_NAME, PARENT_FIELD_API_NAME);
        }, "父字段不存在应该抛出ValidateException");

        verify(describeLogicService, times(1))
                .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * 测试findFieldDependenceWitchObjectApiName方法 - 空结果
     */
    @Test
    @DisplayName("测试findFieldDependenceWitchObjectApiName方法 - 空结果")
    void testFindFieldDependenceWitchObjectApiName_EmptyResult() {
        // Arrange - 配置空的SelectOne字段列表
        ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
        when(mockDescribeExt.getSelectOneFields()).thenReturn(Collections.emptyList());
        staticMockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                .thenReturn(mockDescribeExt);

        // Act
        List<SelectFieldDependence> result = selectFieldDependenceLogicService
                .findFieldDependenceWitchObjectApiName(testUser, DESCRIBE_API_NAME);

        // Assert
        assertNotNull(result, "查询结果不应为null");
        assertTrue(result.isEmpty(), "结果应该为空");

        verify(describeLogicService, times(1))
                .findObjectWithoutCopyIfGray(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * 测试copy方法 - 正常场景
     */
    @Test
    @DisplayName("测试copy方法 - 正常场景")
    void testCopy_Success() {
        // Arrange
        IObjectDescribe mockTargetObject = mock(IObjectDescribe.class);
        when(mockTargetObject.getApiName()).thenReturn("TargetObject");
        when(mockTargetObject.getFieldDescribe(PARENT_FIELD_API_NAME)).thenReturn(mockParentField);
        when(mockTargetObject.getFieldDescribe(CHILD_FIELD_API_NAME)).thenReturn(mockChildField);

        // Act
        assertDoesNotThrow(() -> {
            selectFieldDependenceLogicService.copy(testUser, mockTargetObject, testSelectFieldDependence);
        }, "复制字段依赖不应抛出异常");

        // Assert - copy方法内部会调用create(user, objectDescribe, convertInfo)，然后调用updateFieldDescribe
        verify(describeLogicService, times(1))
                .updateFieldDescribe(eq(testUser), eq(mockTargetObject), anyList());
    }

    /**
     * 测试update方法 - 公共对象异常
     */
    @Test
    @DisplayName("测试update方法 - 公共对象不支持修改字段依赖")
    void testUpdate_ThrowsValidateExceptionForPublicObject() {
        // Arrange - 配置为公共对象
        ObjectDescribeExt mockPublicDescribeExt = mock(ObjectDescribeExt.class);
        when(mockPublicDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(true);
        staticMockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                .thenReturn(mockPublicDescribeExt);

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            selectFieldDependenceLogicService.update(testUser, testSelectFieldDependence);
        }, "公共对象不支持修改字段依赖应该抛出ValidateException");

        verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * 测试deleted方法 - 公共对象异常
     */
    @Test
    @DisplayName("测试deleted方法 - 公共对象不支持修改字段依赖")
    void testDeleted_ThrowsValidateExceptionForPublicObject() {
        // Arrange - 配置为公共对象
        ObjectDescribeExt mockPublicDescribeExt = mock(ObjectDescribeExt.class);
        when(mockPublicDescribeExt.isDownstreamTenantWithPublicObject()).thenReturn(true);
        staticMockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(any(IObjectDescribe.class)))
                .thenReturn(mockPublicDescribeExt);

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            selectFieldDependenceLogicService.deleted(testUser, DESCRIBE_API_NAME,
                    PARENT_FIELD_API_NAME, CHILD_FIELD_API_NAME);
        }, "公共对象不支持修改字段依赖应该抛出ValidateException");

        verify(describeLogicService, times(1)).findObject(TENANT_ID, DESCRIBE_API_NAME);
    }
}
