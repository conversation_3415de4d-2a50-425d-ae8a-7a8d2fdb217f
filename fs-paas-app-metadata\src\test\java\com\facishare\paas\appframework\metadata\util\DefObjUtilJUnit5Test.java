package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * JUnit5测试类 for DefObjUtil
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DefObjUtil 单元测试")
class DefObjUtilJUnit5Test {

    @InjectMocks
    private DefObjUtil defObjUtil;

    @Mock
    private IObjectDescribe mockObjectDescribe;
    @Mock
    private IFieldDescribe mockFieldDescribe1;
    @Mock
    private IFieldDescribe mockFieldDescribe2;
    @Mock
    private IFieldDescribe mockFieldDescribe3;

    private List<IFieldDescribe> fieldDescribeList;

    @BeforeEach
    void setUp() {
        fieldDescribeList = Lists.newArrayList(mockFieldDescribe1, mockFieldDescribe2, mockFieldDescribe3);
    }

    // ==================== addFieldDescribeCreateTime 方法测试 ====================

    @Test
    @DisplayName("添加字段描述创建时间 - 正常情况")
    void testAddFieldDescribeCreateTime_Success() {
        // Arrange
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribeList);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe3.getCreateTime()).thenReturn(null);

        long beforeTime = System.currentTimeMillis();

        // Act
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        long afterTime = System.currentTimeMillis();

        // Assert
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).getCreateTime();
        verify(mockFieldDescribe2).getCreateTime();
        verify(mockFieldDescribe3).getCreateTime();
        
        // 验证setCreateTime被调用
        verify(mockFieldDescribe1).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
        verify(mockFieldDescribe2).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
        verify(mockFieldDescribe3).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
    }

    @Test
    @DisplayName("添加字段描述创建时间 - 部分字段已有创建时间")
    void testAddFieldDescribeCreateTime_PartialExistingTime() {
        // Arrange
        long existingTime = 1234567890L;
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribeList);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(existingTime); // 已有创建时间
        when(mockFieldDescribe2.getCreateTime()).thenReturn(null);         // 没有创建时间
        when(mockFieldDescribe3.getCreateTime()).thenReturn(null);         // 没有创建时间

        // Act
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // Assert
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).getCreateTime();
        verify(mockFieldDescribe2).getCreateTime();
        verify(mockFieldDescribe3).getCreateTime();
        
        // 验证只有null的字段被设置了创建时间
        verify(mockFieldDescribe1, never()).setCreateTime(anyLong()); // 已有时间，不应该被设置
        verify(mockFieldDescribe2).setCreateTime(anyLong());          // 应该被设置
        verify(mockFieldDescribe3).setCreateTime(anyLong());          // 应该被设置
    }

    @Test
    @DisplayName("添加字段描述创建时间 - 所有字段都已有创建时间")
    void testAddFieldDescribeCreateTime_AllExistingTime() {
        // Arrange
        long existingTime1 = 1234567890L;
        long existingTime2 = 1234567891L;
        long existingTime3 = 1234567892L;
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribeList);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(existingTime1);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(existingTime2);
        when(mockFieldDescribe3.getCreateTime()).thenReturn(existingTime3);

        // Act
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // Assert
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).getCreateTime();
        verify(mockFieldDescribe2).getCreateTime();
        verify(mockFieldDescribe3).getCreateTime();
        
        // 验证没有字段被设置创建时间
        verify(mockFieldDescribe1, never()).setCreateTime(anyLong());
        verify(mockFieldDescribe2, never()).setCreateTime(anyLong());
        verify(mockFieldDescribe3, never()).setCreateTime(anyLong());
    }

    @Test
    @DisplayName("添加字段描述创建时间 - 空字段列表")
    void testAddFieldDescribeCreateTime_EmptyFieldList() {
        // Arrange
        List<IFieldDescribe> emptyList = Lists.newArrayList();
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(emptyList);

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);
        });

        verify(mockObjectDescribe).getFieldDescribes();
    }

    @Test
    @DisplayName("添加字段描述创建时间 - null对象描述")
    void testAddFieldDescribeCreateTime_NullObjectDescribe() {
        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            defObjUtil.addFieldDescribeCreateTime(null);
        });

        // 验证没有任何交互
        verifyNoInteractions(mockObjectDescribe);
        verifyNoInteractions(mockFieldDescribe1);
        verifyNoInteractions(mockFieldDescribe2);
        verifyNoInteractions(mockFieldDescribe3);
    }

    @Test
    @DisplayName("添加字段描述创建时间 - 创建时间递增验证")
    void testAddFieldDescribeCreateTime_TimeIncrement() {
        // Arrange
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribeList);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe3.getCreateTime()).thenReturn(null);

        // Act
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);

        // Assert - 验证时间是递增的
        // 由于使用了ArgumentCaptor会比较复杂，这里简化验证
        verify(mockFieldDescribe1).setCreateTime(anyLong());
        verify(mockFieldDescribe2).setCreateTime(anyLong());
        verify(mockFieldDescribe3).setCreateTime(anyLong());
    }

    // ==================== getActionClassNameByActionCode 静态方法测试 ====================

    @Test
    @DisplayName("根据动作代码获取动作类名 - 正常情况")
    void testGetActionClassNameByActionCode_Success() {
        // Arrange
        String supportCode = "CREATE";
        String expectedClassName = "CREATECustomAction";

        // Act
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedClassName, result);
    }

    @Test
    @DisplayName("根据动作代码获取动作类名 - 复杂动作代码")
    void testGetActionClassNameByActionCode_ComplexCode() {
        // Arrange
        String supportCode = "CUSTOM_BUSINESS_ACTION";
        String expectedClassName = "CUSTOM_BUSINESS_ACTIONCustomAction";

        // Act
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedClassName, result);
    }

    @Test
    @DisplayName("根据动作代码获取动作类名 - 空字符串代码")
    void testGetActionClassNameByActionCode_EmptyCode() {
        // Arrange
        String supportCode = "";
        String expectedClassName = "CustomAction";

        // Act
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedClassName, result);
    }

    @Test
    @DisplayName("根据动作代码获取动作类名 - null代码")
    void testGetActionClassNameByActionCode_NullCode() {
        // Arrange
        String supportCode = null;
        String expectedClassName = "nullCustomAction";

        // Act
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedClassName, result);
    }

    @Test
    @DisplayName("根据动作代码获取动作类名 - 特殊字符代码")
    void testGetActionClassNameByActionCode_SpecialCharacters() {
        // Arrange
        String supportCode = "ACTION_WITH-SPECIAL.CHARS@123";
        String expectedClassName = "ACTION_WITH-SPECIAL.CHARS@123CustomAction";

        // Act
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedClassName, result);
    }

    @Test
    @DisplayName("根据动作代码获取动作类名 - 数字代码")
    void testGetActionClassNameByActionCode_NumericCode() {
        // Arrange
        String supportCode = "12345";
        String expectedClassName = "12345CustomAction";

        // Act
        String result = DefObjUtil.getActionClassNameByActionCode(supportCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedClassName, result);
    }

    // ==================== 组合测试 ====================

    @Test
    @DisplayName("组合测试 - 实例方法和静态方法协同工作")
    void testCombined_InstanceAndStaticMethods() {
        // Arrange
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribeList);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe3.getCreateTime()).thenReturn(null);

        String actionCode = "TEST_ACTION";

        // Act
        // 1. 使用实例方法设置字段创建时间
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);
        
        // 2. 使用静态方法获取动作类名
        String actionClassName = DefObjUtil.getActionClassNameByActionCode(actionCode);

        // Assert
        // 验证实例方法调用
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).setCreateTime(anyLong());
        verify(mockFieldDescribe2).setCreateTime(anyLong());
        verify(mockFieldDescribe3).setCreateTime(anyLong());
        
        // 验证静态方法结果
        assertEquals("TEST_ACTIONCustomAction", actionClassName);
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 极长动作代码")
    void testEdgeCase_VeryLongActionCode() {
        // Arrange
        StringBuilder longCode = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longCode.append("VERY_LONG_ACTION_CODE_SEGMENT_").append(i).append("_");
        }
        String supportCode = longCode.toString();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String result = DefObjUtil.getActionClassNameByActionCode(supportCode);
            assertNotNull(result);
            assertTrue(result.endsWith("Action"));
            assertTrue(result.contains(supportCode));
        });
    }

    @Test
    @DisplayName("边界测试 - 国际化字符动作代码")
    void testEdgeCase_InternationalCharacterActionCode() {
        // Arrange
        String chineseCode = "创建动作";
        String japaneseCode = "アクション作成";
        String arabicCode = "إنشاء_عمل";
        String emojiCode = "🚀📝✨";

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String chineseResult = DefObjUtil.getActionClassNameByActionCode(chineseCode);
            String japaneseResult = DefObjUtil.getActionClassNameByActionCode(japaneseCode);
            String arabicResult = DefObjUtil.getActionClassNameByActionCode(arabicCode);
            String emojiResult = DefObjUtil.getActionClassNameByActionCode(emojiCode);

            // 验证结果包含原始代码和CustomAction后缀
            assertEquals("创建动作CustomAction", chineseResult);
            assertEquals("アクション作成CustomAction", japaneseResult);
            assertEquals("إنشاء_عملCustomAction", arabicResult);
            assertEquals("🚀📝✨CustomAction", emojiResult);
        });
    }

    // ==================== 性能测试 ====================

    @Test
    @DisplayName("性能测试 - 大量动作类名生成")
    void testPerformance_MassiveActionClassNameGeneration() {
        // Arrange
        int testCount = 10000;
        String[] actionCodes = new String[testCount];
        
        for (int i = 0; i < testCount; i++) {
            actionCodes[i] = "ACTION_" + i;
        }

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            String result = DefObjUtil.getActionClassNameByActionCode(actionCodes[i]);
            assertNotNull(result);
            assertTrue(result.endsWith("CustomAction"));
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：10000次操作应该在500毫秒内完成
        assertTrue(duration < 500, "Performance test failed: took " + duration + "ms for " + testCount + " operations");
    }

    @Test
    @DisplayName("性能测试 - 大量字段创建时间设置")
    void testPerformance_MassiveFieldCreateTimeSet() {
        // Arrange
        int fieldCount = 1000;
        List<IFieldDescribe> largeFieldList = Lists.newArrayList();
        
        for (int i = 0; i < fieldCount; i++) {
            IFieldDescribe mockField = mock(IFieldDescribe.class);
            when(mockField.getCreateTime()).thenReturn(null);
            largeFieldList.add(mockField);
        }
        
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(largeFieldList);

        // Act & Assert
        long startTime = System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);
        });
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 性能断言：1000个字段的时间设置应该在1秒内完成
        assertTrue(duration < 1000, "Performance test failed: took " + duration + "ms for " + fieldCount + " fields");
        
        // 验证所有字段都被设置了创建时间
        for (IFieldDescribe field : largeFieldList) {
            verify(field).setCreateTime(anyLong());
        }
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的对象定义工具流程")
    void testIntegration_CompleteDefObjUtilFlow() {
        // Arrange - 模拟真实业务场景
        String businessActionCode = "CUSTOMER_CREATE";
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribeList);
        when(mockFieldDescribe1.getCreateTime()).thenReturn(null);
        when(mockFieldDescribe2.getCreateTime()).thenReturn(1234567890L); // 已有时间
        when(mockFieldDescribe3.getCreateTime()).thenReturn(null);

        long beforeTime = System.currentTimeMillis();

        // Act - 执行完整的工具流程
        // 1. 设置字段创建时间
        defObjUtil.addFieldDescribeCreateTime(mockObjectDescribe);
        
        // 2. 生成动作类名
        String actionClassName = DefObjUtil.getActionClassNameByActionCode(businessActionCode);

        long afterTime = System.currentTimeMillis();

        // Assert - 验证完整流程结果
        // 验证字段创建时间设置
        verify(mockObjectDescribe).getFieldDescribes();
        verify(mockFieldDescribe1).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
        verify(mockFieldDescribe2, never()).setCreateTime(anyLong()); // 已有时间，不应该被设置
        verify(mockFieldDescribe3).setCreateTime(longThat(time -> time >= beforeTime && time <= afterTime + 10));
        
        // 验证动作类名生成
        assertEquals("CUSTOMER_CREATECustomAction", actionClassName);

        // 验证业务语义
        assertTrue(actionClassName.contains("CUSTOMER_CREATE"), "动作类名应包含业务动作代码");
        assertTrue(actionClassName.endsWith("CustomAction"), "动作类名应以CustomAction结尾");

        // 验证工具类的实用性
        assertNotNull(actionClassName, "动作类名不应为null");
        assertFalse(actionClassName.trim().isEmpty(), "动作类名不应为空");
        assertTrue(actionClassName.length() > "CustomAction".length(), "动作类名应包含有意义的前缀");
    }
}
