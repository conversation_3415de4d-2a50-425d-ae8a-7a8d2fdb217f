package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DefaultImportObjectProviderTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private User user;

    @Mock
    private IObjectDescribe objectDescribe1;

    @Mock
    private IObjectDescribe objectDescribe2;

    @InjectMocks
    private DefaultImportObjectProvider defaultImportObjectProvider;

    private String testTenantId;
    private ImportObjectProvider.ImportModuleContext importModuleContext;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        when(user.getTenantId()).thenReturn(testTenantId);
        
        importModuleContext = ImportObjectProvider.ImportModuleContext.builder()
                .management(true)
                .build();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectModule方法返回正确的UDOBJ模块名称
     */
    @Test
    @DisplayName("getObjectModule - 返回正确的UDOBJ模块名称")
    void testGetObjectModule_返回正确的UDOBJ模块名称() {
        // 执行被测试方法
        String result = defaultImportObjectProvider.getObjectModule();

        // 验证结果
        assertEquals(UDOBJ, result);
        assertEquals("udobj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportObjectModule方法正常返回导入模块列表
     */
    @Test
    @DisplayName("getImportObjectModule - 正常返回导入模块列表")
    void testGetImportObjectModule_正常返回导入模块列表() {
        // 准备测试数据
        List<IObjectDescribe> mockDescribeList = Arrays.asList(objectDescribe1, objectDescribe2);
        List<IObjectDescribe> filteredDescribeList = Arrays.asList(objectDescribe1);

        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(
                eq(testTenantId), eq(true), eq(true), eq(false), eq(true), eq(ObjectListConfig.IMPORT)))
                .thenReturn(mockDescribeList);
        when(describeLogicService.filterDescribesWithActionCode(
                eq(user), eq(mockDescribeList), eq(ObjectAction.BATCH_IMPORT.getActionCode())))
                .thenReturn(filteredDescribeList);

        // 执行被测试方法
        List<ImportObjectModule.ImportModule> result = 
                defaultImportObjectProvider.getImportObjectModule(user, importModuleContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        // 验证Mock交互
        verify(describeLogicService).findObjectsByTenantId(
                testTenantId, true, true, false, true, ObjectListConfig.IMPORT);
        verify(describeLogicService).filterDescribesWithActionCode(
                user, mockDescribeList, ObjectAction.BATCH_IMPORT.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当没有可导入对象时，getImportObjectModule方法返回空列表
     */
    @Test
    @DisplayName("getImportObjectModule - 没有可导入对象时返回空列表")
    void testGetImportObjectModule_没有可导入对象时返回空列表() {
        // 准备测试数据
        List<IObjectDescribe> emptyDescribeList = Collections.emptyList();

        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(
                eq(testTenantId), eq(true), eq(true), eq(false), eq(true), eq(ObjectListConfig.IMPORT)))
                .thenReturn(emptyDescribeList);
        when(describeLogicService.filterDescribesWithActionCode(
                eq(user), eq(emptyDescribeList), eq(ObjectAction.BATCH_IMPORT.getActionCode())))
                .thenReturn(emptyDescribeList);

        // 执行被测试方法
        List<ImportObjectModule.ImportModule> result = 
                defaultImportObjectProvider.getImportObjectModule(user, importModuleContext);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertEquals(0, result.size());

        // 验证Mock交互
        verify(describeLogicService).findObjectsByTenantId(
                testTenantId, true, true, false, true, ObjectListConfig.IMPORT);
        verify(describeLogicService).filterDescribesWithActionCode(
                user, emptyDescribeList, ObjectAction.BATCH_IMPORT.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当权限过滤后没有可用对象时的处理
     */
    @Test
    @DisplayName("getImportObjectModule - 权限过滤后没有可用对象")
    void testGetImportObjectModule_权限过滤后没有可用对象() {
        // 准备测试数据
        List<IObjectDescribe> mockDescribeList = Arrays.asList(objectDescribe1, objectDescribe2);
        List<IObjectDescribe> emptyFilteredList = Collections.emptyList();

        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(
                eq(testTenantId), eq(true), eq(true), eq(false), eq(true), eq(ObjectListConfig.IMPORT)))
                .thenReturn(mockDescribeList);
        when(describeLogicService.filterDescribesWithActionCode(
                eq(user), eq(mockDescribeList), eq(ObjectAction.BATCH_IMPORT.getActionCode())))
                .thenReturn(emptyFilteredList);

        // 执行被测试方法
        List<ImportObjectModule.ImportModule> result = 
                defaultImportObjectProvider.getImportObjectModule(user, importModuleContext);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertEquals(0, result.size());

        // 验证Mock交互
        verify(describeLogicService).findObjectsByTenantId(
                testTenantId, true, true, false, true, ObjectListConfig.IMPORT);
        verify(describeLogicService).filterDescribesWithActionCode(
                user, mockDescribeList, ObjectAction.BATCH_IMPORT.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当user为null时，getImportObjectModule方法的异常处理
     */
    @Test
    @DisplayName("getImportObjectModule - user为null时的异常处理")
    void testGetImportObjectModuleThrowsException_user为null时的异常处理() {
        // 执行并验证异常
        Exception exception = assertThrows(NullPointerException.class, () -> {
            defaultImportObjectProvider.getImportObjectModule(null, importModuleContext);
        });

        // 验证异常信息
        assertNotNull(exception);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当tenantId为null时的处理
     */
    @Test
    @DisplayName("getImportObjectModule - tenantId为null时的处理")
    void testGetImportObjectModule_tenantId为null时的处理() {
        // 准备测试数据
        when(user.getTenantId()).thenReturn(null);
        List<IObjectDescribe> emptyDescribeList = Collections.emptyList();

        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(
                eq(null), eq(true), eq(true), eq(false), eq(true), eq(ObjectListConfig.IMPORT)))
                .thenReturn(emptyDescribeList);
        when(describeLogicService.filterDescribesWithActionCode(
                eq(user), eq(emptyDescribeList), eq(ObjectAction.BATCH_IMPORT.getActionCode())))
                .thenReturn(emptyDescribeList);

        // 执行被测试方法
        List<ImportObjectModule.ImportModule> result = 
                defaultImportObjectProvider.getImportObjectModule(user, importModuleContext);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证Mock交互
        verify(describeLogicService).findObjectsByTenantId(
                null, true, true, false, true, ObjectListConfig.IMPORT);
        verify(describeLogicService).filterDescribesWithActionCode(
                user, emptyDescribeList, ObjectAction.BATCH_IMPORT.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当ImportModuleContext为null时的处理
     */
    @Test
    @DisplayName("getImportObjectModule - ImportModuleContext为null时的处理")
    void testGetImportObjectModule_ImportModuleContext为null时的处理() {
        // 准备测试数据
        List<IObjectDescribe> mockDescribeList = Arrays.asList(objectDescribe1);

        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(
                eq(testTenantId), eq(true), eq(true), eq(false), eq(true), eq(ObjectListConfig.IMPORT)))
                .thenReturn(mockDescribeList);
        when(describeLogicService.filterDescribesWithActionCode(
                eq(user), eq(mockDescribeList), eq(ObjectAction.BATCH_IMPORT.getActionCode())))
                .thenReturn(mockDescribeList);

        // 执行被测试方法
        List<ImportObjectModule.ImportModule> result = 
                defaultImportObjectProvider.getImportObjectModule(user, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        // 验证Mock交互
        verify(describeLogicService).findObjectsByTenantId(
                testTenantId, true, true, false, true, ObjectListConfig.IMPORT);
        verify(describeLogicService).filterDescribesWithActionCode(
                user, mockDescribeList, ObjectAction.BATCH_IMPORT.getActionCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试DescribeLogicService异常时的处理
     */
    @Test
    @DisplayName("getImportObjectModule - DescribeLogicService异常时的处理")
    void testGetImportObjectModuleThrowsException_DescribeLogicService异常时的处理() {
        // 配置Mock行为
        when(describeLogicService.findObjectsByTenantId(
                eq(testTenantId), eq(true), eq(true), eq(false), eq(true), eq(ObjectListConfig.IMPORT)))
                .thenThrow(new RuntimeException("Service exception"));

        // 执行并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            defaultImportObjectProvider.getImportObjectModule(user, importModuleContext);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("Service exception"));

        // 验证Mock交互
        verify(describeLogicService).findObjectsByTenantId(
                testTenantId, true, true, false, true, ObjectListConfig.IMPORT);
        verify(describeLogicService, never()).filterDescribesWithActionCode(any(), any(), anyString());
    }
}
