package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.FileExtUtil;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.ImageFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ImageImportDataConverter extends AbstractFileImportDataConverter {

    // 图片错误标识的正则表达式模式
    private static final Pattern IMG_ERROR_PATTERN = Pattern.compile("\\[IMG_ERROR:([^\\]]+)\\](.*)");

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.IMAGE);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        ImageFieldDescribe imageFieldDescribe = (ImageFieldDescribe) fieldDescribe;
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        String fieldApiName = fieldDescribe.getApiName();
        //保存图片结构
        List<Map<String, String>> result = Lists.newArrayList();
        String tempData = dataExt.get(fieldApiName, String.class).trim();
        if (Strings.isNullOrEmpty(tempData)) {
            //兼容图片类型：元数据保存图片/附件类型时不能传"", 必须是空列表
            return ConvertResult.buildSuccess(result);
        }
        String[] arr = tempData.split("\\|");
        List<String> images = Arrays.asList(arr);
        //为空时
        if (CollectionUtils.empty(images)) {
            return ConvertResult.buildSuccess(result);
        }

        // 图片错误处理：检测并处理Excel导入服务生成的错误标识
        ConvertResult errorResult = processImageErrors(images, fieldDescribe);
        if (errorResult != null) {
            return errorResult;
        }

        // 如果没有错误，直接使用原始images继续处理
        //如果只允许水印图片，则不能导入
        if (imageFieldDescribe.getIsWaterMark()) {
            return ConvertResult.buildError(I18NExt.text(I18NKey.IMPORT_IMAGE_ERROR1));
        }
        // 校验导入图片数量，不能超过设置的最大数量
        if (images.size() > imageFieldDescribe.getFileAmountLimit()) {
            return ConvertResult.buildError("[" + imageFieldDescribe.getLabel() + "]" +
                    I18NExt.getOrDefault(I18NKey.IMAGE_EXCEED_COUNT, "导入图片数量不能超过{0}张", imageFieldDescribe.getFileAmountLimit()));// ignoreI18n
        }
        if (!validateImageMinSize(imageFieldDescribe, images)) {
            return ConvertResult.buildError("[" + imageFieldDescribe.getLabel() + "]" +
                    I18NExt.getOrDefault(I18NKey.IMAGE_EXCEED_MIN_COUNT, "导入图片数量不能少于{0}张", imageFieldDescribe.getFileAmountMinLimit()));// ignoreI18n
        }

        // 检测是否包含TNpath格式 Excel嵌入图片导入数据时格式
        if (containsTNpathFormat(images)) {
            // TNpath处理分支：直接转换并构造result，不调用checkImportFilesOrConvertFiles
            return handleTNpathConversion(user, images, imageFieldDescribe, result);
        } else {
            // 原有Npath处理分支：继续原有逻辑
            String error = checkImportFilesOrConvertFiles(user, images, imageFieldDescribe.getLabel(), I18NKey.IMAGE, 1024 * 1024 * 20L, result);
            if (!Strings.isNullOrEmpty(error)) {
                return ConvertResult.buildError(error);
            }
            return ConvertResult.buildSuccess(result);
        }
    }

    /**
     * 处理图片错误：检测Excel导入服务生成的错误标识并生成错误报告
     *
     * @param images 图片路径列表，可能包含[IMG_ERROR:xxx]格式的错误标识
     * @param fieldDescribe 字段描述
     * @return 如果有错误返回错误结果，否则返回null继续正常流程
     */
    private ConvertResult processImageErrors(List<String> images, IFieldDescribe fieldDescribe) {
        List<String> errorMessages = new ArrayList<>();

        for (String image : images) {
            if (containsImageError(image)) {
                String errorKey = extractErrorKey(image);
                if (StringUtils.isNotBlank(errorKey)) {
                    // 使用I18NExt.text()获取当前语言环境的错误提示
                    String errorMessage = I18NExt.text(errorKey);
                    errorMessages.add(errorMessage);
                }
            }
        }

        if (errorMessages.isEmpty()) {
            return null; // 没有错误，继续正常流程
        }

        // 生成简化的错误报告：[字段名] 错误消息1,错误消息2,错误消息3
        String finalReport = "[" + fieldDescribe.getLabel() + "] " + String.join(",", errorMessages);
        return ConvertResult.buildError(finalReport);
    }

    /**
     * 检测单元格值是否包含图片错误标识
     *
     * @param cellValue 单元格值，格式：[IMG_ERROR:I18N_KEY]原始值
     * @return true 包含错误标识，false 不包含
     */
    private boolean containsImageError(String cellValue) {
        return StringUtils.isNotBlank(cellValue) &&
                cellValue.matches(".*\\[IMG_ERROR:[^\\]]+\\].*");
    }

    /**
     * 提取错误的多语言key
     *
     * @param cellValue 包含错误标识的单元格值
     * @return 多语言key，提取失败返回null
     */
    private String extractErrorKey(String cellValue) {
        Matcher matcher = IMG_ERROR_PATTERN.matcher(cellValue);
        return matcher.find() ? matcher.group(1).trim() : null;
    }

    /**
     * 校验图片最小个数
     *
     * @return true 符合要求 false 不符合要求
     */
    private boolean validateImageMinSize(ImageFieldDescribe fieldDescribe, List<String> images) {
        Integer fileAmountMinLimit = fieldDescribe.getFileAmountMinLimit();
        if (ObjectUtils.isEmpty(fileAmountMinLimit)) {
            return true;
        }

        //图片为空也不校验长度，这个是业务侧特殊需求
        if (ObjectUtils.isEmpty(images)) {
            return true;
        }
        return images.size() >= fileAmountMinLimit;
    }

    /**
     * 检测是否包含TNpath格式（格式为：文件名.后缀#TN_xxx）
     *
     * @param paths 路径列表
     * @return true 包含TNpath格式，false 不包含
     */
    private boolean containsTNpathFormat(List<String> paths) {
        return paths.stream().anyMatch(path -> {
            if (!path.contains("#")) {
                return false;
            }
            // 检查#后面的部分是否以TN_开头
            String pathPart = FileExtUtil.getPath(path);
            return pathPart != null && pathPart.startsWith("TN_");
        });
    }

    /**
     * 处理TNpath格式的转换
     *
     * @param user 用户信息
     * @param fileNameTnPathInfos TNpath路径列表
     * @param imageFieldDescribe 图片字段描述
     * @param result 结果列表
     * @return 转换结果
     */
    private ConvertResult handleTNpathConversion(User user, List<String> fileNameTnPathInfos, ImageFieldDescribe imageFieldDescribe, List<Map<String, String>> result) {
        try {
            List<Map<String, Object>> fileResult = Lists.newArrayList();
            List<FileStoreService.PathOriginNames> pathOriginNamesList = new ArrayList<>();

            for (String tnPath : fileNameTnPathInfos) {
                String fileName = FileExtUtil.getFileName(tnPath);
                String tempPath = FileExtUtil.getPath(tnPath);
                pathOriginNamesList.add(FileStoreService.PathOriginNames.of(tempPath, fileName));
            }

            // 调用转换服务
            List<FileStoreService.PathPair> pathPairs = fileStoreService.saveImageFromTempFilesAndNames(
                    user.getTenantId(), user.getUserId(), pathOriginNamesList, "");

            // 构建 tempPath -> PathPair 的映射，包含 finalNPath 和 size 信息
            Map<String, FileStoreService.PathPair> tempPathToPathPairMap = pathPairs.stream()
                    .collect(Collectors.toMap(
                            FileStoreService.PathPair::getTempPath,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            for (FileStoreService.PathOriginNames pathOriginNames : pathOriginNamesList) {
                String tempPath = pathOriginNames.getPath();
                String fileName = pathOriginNames.getOriginNames();
                FileStoreService.PathPair pathPair = tempPathToPathPairMap.get(tempPath);
                if (Objects.isNull(pathPair)) {
                    log.warn("lost pathPair, tempPath:{}", tempPath);
                    continue; // 跳过找不到对应关系的文件
                }
                String finalNPath = pathPair.getFinalNPath();
                Long size = pathPair.getSize();
                String fileExt = FileExtUtil.getFileExtension(fileName);
                Map<String, Object> imageInfoMap = Maps.newLinkedHashMap();
                imageInfoMap.put("filename", fileName);
                imageInfoMap.put("ext", fileExt);
                imageInfoMap.put("path", finalNPath);
                imageInfoMap.put("size", size);
                imageInfoMap.put("create_time", System.currentTimeMillis());
                // 注意：create_time信息在TNpath转换中无法获取，暂时不添加
                fileResult.add(imageInfoMap);
            }

            return ConvertResult.buildSuccess(fileResult);

        } catch (Exception e) {
            return ConvertResult.buildError("[" + imageFieldDescribe.getLabel() + "]" +
                    I18NExt.getOrDefault(I18NKey.SAVE_IMAGE_FAILED, "保存图片失败"));// ignoreI18n
        }
    }
}
