package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.util.SfaGrayUtil;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static com.facishare.crm.openapi.Utils.SPU_API_NAME;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SPUObjectImportProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IUniqueRule uniqueRule;

    @Mock
    private RequestContext requestContext;

    @Spy
    private SPUObjectImportProvider spuObjectImportProvider;

    private String testTenantId;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        when(requestContext.getTenantId()).thenReturn(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的SPU API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的SPU API名称")
    void testGetObjectCode_返回正确的SPU_API名称() {
        // 执行被测试方法
        String result = spuObjectImportProvider.getObjectCode();

        // 验证结果
        assertEquals(SPU_API_NAME, result);
        assertEquals("SPUObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectName方法调用父类方法
     */
    @Test
    @DisplayName("getObjectName - 调用父类方法")
    void testGetObjectName_调用父类方法() {
        // 准备测试数据
        String expectedObjectName = "SPU对象";
        doReturn(expectedObjectName).when(spuObjectImportProvider, "getObjectName", objectDescribe);

        // 执行被测试方法
        String result = spuObjectImportProvider.getObjectName(objectDescribe);

        // 验证结果
        assertEquals(expectedObjectName, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当SPU功能开启时，getImportObject方法调用父类方法
     */
    @Test
    @DisplayName("getImportObject - SPU功能开启时调用父类方法")
    void testGetImportObject_SPU功能开启时调用父类方法() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<SfaGrayUtil> mockedSfaGrayUtil = mockStatic(SfaGrayUtil.class)) {

            // 准备测试数据
            ImportObject expectedImportObject = ImportObject.builder()
                    .objectCode(SPU_API_NAME)
                    .objectName("SPU对象")
                    .build();

            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(requestContext);
            mockedSfaGrayUtil.when(() -> SfaGrayUtil.isSpuOpen(testTenantId)).thenReturn(true);
            doReturn(Optional.of(expectedImportObject))
                    .when(spuObjectImportProvider, "getImportObject", objectDescribe, uniqueRule);

            // 执行被测试方法
            Optional<ImportObject> result = spuObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertTrue(result.isPresent());
            assertEquals(expectedImportObject, result.get());

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
            mockedSfaGrayUtil.verify(() -> SfaGrayUtil.isSpuOpen(testTenantId));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当SPU功能关闭时，getImportObject方法返回空Optional
     */
    @Test
    @DisplayName("getImportObject - SPU功能关闭时返回空Optional")
    void testGetImportObject_SPU功能关闭时返回空Optional() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<SfaGrayUtil> mockedSfaGrayUtil = mockStatic(SfaGrayUtil.class)) {

            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(requestContext);
            mockedSfaGrayUtil.when(() -> SfaGrayUtil.isSpuOpen(testTenantId)).thenReturn(false);

            // 执行被测试方法
            Optional<ImportObject> result = spuObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertFalse(result.isPresent());
            assertTrue(result.isEmpty());

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
            mockedSfaGrayUtil.verify(() -> SfaGrayUtil.isSpuOpen(testTenantId));
            // 验证没有调用父类方法
            verify(spuObjectImportProvider, never()).getImportObject(any(IObjectDescribe.class), any(IUniqueRule.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportType方法返回默认导入类型
     */
    @Test
    @DisplayName("getImportType - 返回默认导入类型")
    void testGetImportType_返回默认导入类型() {
        // 执行被测试方法
        ImportType result = spuObjectImportProvider.getImportType(objectDescribe, uniqueRule);

        // 验证结果
        assertEquals(ImportType.DEFAULT, result);
        assertEquals(1, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsNotSupportSaleEvent方法返回true
     */
    @Test
    @DisplayName("getIsNotSupportSaleEvent - 返回true")
    void testGetIsNotSupportSaleEvent_返回true() {
        // 执行被测试方法
        boolean result = spuObjectImportProvider.getIsNotSupportSaleEvent(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试supportImportId方法返回true
     */
    @Test
    @DisplayName("supportImportId - 返回true")
    void testSupportImportId_返回true() {
        // 执行被测试方法
        boolean result = spuObjectImportProvider.supportImportId(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当RequestContext为null时，getImportObject方法的异常处理
     */
    @Test
    @DisplayName("getImportObject - RequestContext为null时的异常处理")
    void testGetImportObjectThrowsException_RequestContext为null时的异常处理() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            // 执行并验证异常
            Exception exception = assertThrows(NullPointerException.class, () -> {
                spuObjectImportProvider.getImportObject(objectDescribe, uniqueRule);
            });

            // 验证异常信息
            assertNotNull(exception);

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当tenantId为null时，SPU功能检查的处理
     */
    @Test
    @DisplayName("getImportObject - tenantId为null时的处理")
    void testGetImportObject_tenantId为null时的处理() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<SfaGrayUtil> mockedSfaGrayUtil = mockStatic(SfaGrayUtil.class)) {

            // 准备测试数据
            when(requestContext.getTenantId()).thenReturn(null);

            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(requestContext);
            mockedSfaGrayUtil.when(() -> SfaGrayUtil.isSpuOpen(null)).thenReturn(false);

            // 执行被测试方法
            Optional<ImportObject> result = spuObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertFalse(result.isPresent());

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
            mockedSfaGrayUtil.verify(() -> SfaGrayUtil.isSpuOpen(null));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - objectDescribe为null时的处理")
    void test各方法_objectDescribe为null时的处理() {
        // 测试getImportType
        ImportType importType = spuObjectImportProvider.getImportType(null, uniqueRule);
        assertEquals(ImportType.DEFAULT, importType);

        // 测试getIsNotSupportSaleEvent
        boolean notSupportSaleEvent = spuObjectImportProvider.getIsNotSupportSaleEvent(null);
        assertTrue(notSupportSaleEvent);

        // 测试supportImportId
        boolean supportImportId = spuObjectImportProvider.supportImportId(null);
        assertTrue(supportImportId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试所有布尔方法的一致性
     */
    @Test
    @DisplayName("布尔方法 - 一致性测试确保都返回true")
    void test布尔方法_一致性测试确保都返回true() {
        // 执行所有布尔方法
        boolean notSupportSaleEvent = spuObjectImportProvider.getIsNotSupportSaleEvent(objectDescribe);
        boolean supportImportId = spuObjectImportProvider.supportImportId(objectDescribe);

        // 验证结果
        assertTrue(notSupportSaleEvent);
        assertTrue(supportImportId);

        // 验证一致性
        assertEquals(notSupportSaleEvent, supportImportId);
    }
}
