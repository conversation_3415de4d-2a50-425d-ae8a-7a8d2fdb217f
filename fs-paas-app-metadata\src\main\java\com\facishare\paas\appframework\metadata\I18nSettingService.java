package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.dto.I18nTrans;
import com.facishare.paas.appframework.metadata.i18nKeyPlatform.Save;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.i18n.client.api.Language;
import com.fxiaoke.i18n.client.api.Localization;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface I18nSettingService {

    Map<String, String> getTransValue(List<String> keys, boolean skipLicenseValidate, boolean isOnTime);

    String getTransValue(String key, String defaultValue, boolean isOnTime);

    Map<String, Localization> getLocalization(List<String> keys, String tenantId, boolean skipLicenseValidate, boolean isOnTime);

    void customObjSave(Save.Arg arg, String tenantId);

    void saveTransValue(Map<String, String> keyToNewName, boolean needLicenseCheck);

    void syncTransValue(Map<String, String> keyToNewName, String lang, String tenantId);

    List<Localization> buildLocalizations(Map<String, String> keyToNewName, String lang, String tenantId);

    String getTransValue(String tenantId, String key, String defaultValue, String lang, boolean isOnTime);

    void syncTransValueIncludePreKey(String tenantId, List<I18nTrans.TransArg> transArg, String lang);

    Map<String, String> getTransValue(Integer tenantId, List<I18nTrans.TransArg> transArgList, String lang, boolean isOnTime);

    /**
     * @description 获取企业License内包含的语言
     */
    List<Language> getTenantLanguage(String ei);

    List<String> getTenantLanguageTags(String ei);

    /*
     * @param describeApiName 对象ApiName
     * @return: 填充i18nInfos中的多语信息, 只会补充这个企业拥有并且已经启用的语言
     */
    List<I18nInfo> queryTranslation(String tenantId, String describeApiName, List<I18nInfo> i18nInfos);

    // 如果描述有变化, 使用这个方法, 防止由于拿不到最新的描述, 导致元数据不同步(需要按照对象描述获取key的tag)
    void synTranslateToMetaQuietly(String tenantId, String describeApiName, List<I18nInfo> i18nInfos, IObjectDescribe describe);

    /**
     * @param tenantId        企业ei
     * @param describeApiName 对象ApiName
     * @param i18nInfos       需要同步的多语消息
     */
    void synTranslateToMetaQuietly(String tenantId, String describeApiName, List<I18nInfo> i18nInfos);

    /*
     * @description: 通过循环比较, 获取当前的name属于的key对应的location, 若无, 返回empty
     */
    Optional<Localization> getPreLocalization(String name, String ei, List<String> preKeys);
}
