package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.facishare.crm.openapi.Utils.PREPAY_DETAIL_API_NAME;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PrepayDetailObjectProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IUniqueRule uniqueRule;

    private PrepayDetailObjectProvider prepayDetailObjectProvider;

    @BeforeEach
    void setUp() {
        prepayDetailObjectProvider = new PrepayDetailObjectProvider();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的预存款API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的预存款API名称")
    void testGetObjectCode_返回正确的预存款API名称() {
        // 执行被测试方法
        String result = prepayDetailObjectProvider.getObjectCode();

        // 验证结果
        assertEquals(PREPAY_DETAIL_API_NAME, result);
        assertEquals("PrepayDetailObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportType方法返回不支持更新导入类型
     */
    @Test
    @DisplayName("getImportType - 返回不支持更新导入类型")
    void testGetImportType_返回不支持更新导入类型() {
        // 执行被测试方法
        ImportType result = prepayDetailObjectProvider.getImportType(objectDescribe, uniqueRule);

        // 验证结果
        assertEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, result);
        assertEquals(0, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDuplicateJudgmentType方法返回不支持更新导入类型
     */
    @Test
    @DisplayName("getDuplicateJudgmentType - 返回不支持更新导入类型")
    void testGetDuplicateJudgmentType_返回不支持更新导入类型() {
        // 执行被测试方法
        int result = prepayDetailObjectProvider.getDuplicateJudgmentType(objectDescribe);

        // 验证结果
        assertEquals(DefaultObjectImportProvider.UNSUPPORT_UPDATE_IMPORT, result);
        assertEquals(0, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportType和getDuplicateJudgmentType方法返回值的一致性
     */
    @Test
    @DisplayName("方法一致性 - getImportType和getDuplicateJudgmentType返回值一致")
    void test方法一致性_getImportType和getDuplicateJudgmentType返回值一致() {
        // 执行被测试方法
        ImportType importType = prepayDetailObjectProvider.getImportType(objectDescribe, uniqueRule);
        int duplicateJudgmentType = prepayDetailObjectProvider.getDuplicateJudgmentType(objectDescribe);

        // 验证结果
        assertEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, importType);
        assertEquals(DefaultObjectImportProvider.UNSUPPORT_UPDATE_IMPORT, duplicateJudgmentType);
        assertEquals(importType.getType(), duplicateJudgmentType);
        assertEquals(0, importType.getType());
        assertEquals(0, duplicateJudgmentType);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - objectDescribe为null时的处理")
    void test各方法_objectDescribe为null时的处理() {
        // 测试getImportType
        ImportType importType = prepayDetailObjectProvider.getImportType(null, uniqueRule);
        assertEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, importType);

        // 测试getDuplicateJudgmentType
        int duplicateJudgmentType = prepayDetailObjectProvider.getDuplicateJudgmentType(null);
        assertEquals(DefaultObjectImportProvider.UNSUPPORT_UPDATE_IMPORT, duplicateJudgmentType);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当uniqueRule为null时，getImportType方法的处理
     */
    @Test
    @DisplayName("getImportType - uniqueRule为null时的处理")
    void testGetImportType_uniqueRule为null时的处理() {
        // 执行被测试方法
        ImportType result = prepayDetailObjectProvider.getImportType(objectDescribe, null);

        // 验证结果
        assertEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, result);
        assertEquals(0, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当所有参数为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - 所有参数为null时的处理")
    void test各方法_所有参数为null时的处理() {
        // 测试getImportType
        ImportType importType = prepayDetailObjectProvider.getImportType(null, null);
        assertEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, importType);

        // 测试getDuplicateJudgmentType
        int duplicateJudgmentType = prepayDetailObjectProvider.getDuplicateJudgmentType(null);
        assertEquals(DefaultObjectImportProvider.UNSUPPORT_UPDATE_IMPORT, duplicateJudgmentType);

        // 验证一致性
        assertEquals(importType.getType(), duplicateJudgmentType);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试预存款对象的业务特性 - 不支持更新导入
     */
    @Test
    @DisplayName("业务特性 - 预存款对象不支持更新导入")
    void test业务特性_预存款对象不支持更新导入() {
        // 执行被测试方法
        String objectCode = prepayDetailObjectProvider.getObjectCode();
        ImportType importType = prepayDetailObjectProvider.getImportType(objectDescribe, uniqueRule);
        int duplicateJudgmentType = prepayDetailObjectProvider.getDuplicateJudgmentType(objectDescribe);

        // 验证业务特性
        assertEquals(PREPAY_DETAIL_API_NAME, objectCode);
        assertEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, importType);
        assertEquals(DefaultObjectImportProvider.UNSUPPORT_UPDATE_IMPORT, duplicateJudgmentType);

        // 验证不支持更新导入的特性
        assertNotEquals(ImportType.DEFAULT, importType);
        assertNotEquals(ImportType.UNSUPPORT_INSERT_IMPORT, importType);
        assertEquals(0, importType.getType());
        assertEquals(0, duplicateJudgmentType);
    }
}
