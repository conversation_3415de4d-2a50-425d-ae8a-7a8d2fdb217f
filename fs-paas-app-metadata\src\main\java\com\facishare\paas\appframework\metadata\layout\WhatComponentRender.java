package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TableColumnExt;
import com.facishare.paas.appframework.metadata.WhatComponentExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Sets;
import lombok.Builder;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;
import static com.facishare.crm.userdefobj.DefObjConstants.invisibleFieldListFormObjectMap;
import static com.facishare.paas.metadata.api.describe.IFieldType.EMBEDDED_OBJECT_LIST;
import static com.facishare.paas.metadata.api.describe.IFieldType.GROUP;

@Builder
public class WhatComponentRender {
    private User user;

    private ObjectDescribeExt describeExt;

    private ObjectDescribeExt whatDescribeExt;

    private WhatComponentExt whatComponentExt;

    private FunctionPrivilegeService functionPrivilegeService;

    public void render() {
        StopWatch stopWatch = StopWatch.create(String.format("whatComponentRender#%s_%s_%s", describeExt.getApiName(), whatDescribeExt.getApiName(), whatComponentExt.getName()));
        removeInactiveFields();
        stopWatch.lap("removeInactiveFields");
        removeInvisibleField();
        stopWatch.lap("removeInvisibleField");
        removeSignOutField();
        stopWatch.lap("removeSignOutField");
        whatComponentExt.adjustFieldRenderType(describeExt.getApiName());
        whatComponentExt.adjustFieldRenderType(whatDescribeExt.getApiName());
        stopWatch.lap("adjustFieldRenderType");
        whatComponentExt.removeFieldByTypes(describeExt, EMBEDDED_OBJECT_LIST, GROUP);
        whatComponentExt.removeFieldByTypes(whatDescribeExt, EMBEDDED_OBJECT_LIST, GROUP);
        stopWatch.lap("removeFieldByTypes");
        //如果布局里没有字段了，则将name字段设置为展示字段
        whatComponentExt.setDefaultFieldListIfEmpty();
        stopWatch.lap("setDefaultFieldListIfEmpty");
        //列表页布局不应该保存label，针对历史数据兼容一下，否则终端有问题，后面终端处理后可以删掉
        whatComponentExt.correctLabel(describeExt, whatDescribeExt);
        stopWatch.lap("correctLabel");
        stopWatch.logSlow(300);
    }

    public void filterUnauthorizedFieldsByRole(String roleCode) {
        Set<String> unauthorizedFieldsByRole = functionPrivilegeService.getUnauthorizedFieldsByRole(user, roleCode, describeExt.getApiName());
        whatComponentExt.removeFields(unauthorizedFieldsByRole);
    }

    private void removeSignOutField() {
        describeExt.getSignInFieldDescribe().ifPresent(x -> {
            whatComponentExt.removeFields(Sets.newHashSet(x.getSignInInfoListFieldApiName()));
            if (Boolean.FALSE.equals(x.getIsEnableSignOut())) {
                whatComponentExt.removeFields(Sets.newHashSet(
                        x.getSignOutStatusFieldApiName(),
                        x.getSignOutLocationFieldApiName(),
                        x.getSignOutTimeFieldApiName(),
                        x.getIntervalFieldApiName()));
            }
        });

        whatDescribeExt.getSignInFieldDescribe().ifPresent(x -> {
            whatComponentExt.removeFields(convertToWhatFieldName(Sets.newHashSet(x.getSignInInfoListFieldApiName())));
            if (Boolean.FALSE.equals(x.getIsEnableSignOut())) {
                whatComponentExt.removeFields(convertToWhatFieldName(Sets.newHashSet(
                        x.getSignOutStatusFieldApiName(),
                        x.getSignOutLocationFieldApiName(),
                        x.getSignOutTimeFieldApiName(),
                        x.getIntervalFieldApiName())));
            }
        });
    }

    private void removeInvisibleField() {
        Set<String> invisibleFields = Sets.newHashSet();
        invisibleFields.addAll(getInvisibleFieldByConfig(describeExt));
        invisibleFields.addAll(convertToWhatFieldName(getInvisibleFieldByConfig(whatDescribeExt)));
        if (functionPrivilegeService != null) {
            invisibleFields.addAll(functionPrivilegeService.getUnauthorizedFields(user, describeExt.getApiName()));
            invisibleFields.addAll(convertToWhatFieldName(
                    functionPrivilegeService.getUnauthorizedFields(user, whatDescribeExt.getApiName())));
        }
        whatComponentExt.removeFields(invisibleFields);
    }

    private Set<String> getInvisibleFieldByConfig(ObjectDescribeExt describeExt) {
        return describeExt.isSFAObject() ?
                invisibleFieldListFormObjectMap.getOrDefault(describeExt.getApiName(), Collections.emptySet()) :
                invisibleFieldListFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet());
    }

    public void removeInactiveFields() {
        Set<String> invisibleFields = Sets.newHashSet();

        Set<String> unActiveFields = describeExt.stream()
                .filter(x -> !x.isActive())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());

        Set<String> set = whatDescribeExt.stream()
                // 老对象的抽象字段也需要被干掉
                .filter(x -> !x.isActive() || (Optional.ofNullable(x.isAbstract()).orElse(Boolean.FALSE)))
                .map(x -> WhatComponentExt.getWhatFieldName(whatDescribeExt.getApiName(), x.getApiName()))
                .collect(Collectors.toSet());
        unActiveFields.addAll(set);
        invisibleFields.addAll(unActiveFields);

        Set<String> deletedFields = whatComponentExt.getIncludeFields().stream()
                .map(x -> TableColumnExt.of(x).getApiName())
                .filter(x -> (!WhatComponentExt.isWhatField(x) && !describeExt.containsField(x)) ||
                        (WhatComponentExt.isWhatField(x) && Objects.nonNull(whatDescribeExt) &&
                                !whatDescribeExt.containsField(WhatComponentExt.getActualFieldNameOfWhatField(x))))
                .collect(Collectors.toSet());

        invisibleFields.addAll(deletedFields);

        whatComponentExt.removeFields(invisibleFields);
    }

    private Set<String> convertToWhatFieldName(Set<String> fieldApiNameSet) {
        return fieldApiNameSet.stream()
                .map(a -> WhatComponentExt.getWhatFieldName(whatDescribeExt.getApiName(), a))
                .collect(Collectors.toSet());
    }

}
