package com.facishare.paas.appframework.metadata.handler;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.pa.impl.Repository;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.QueryResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * HandlerRuntimeConfigServiceImpl的JUnit5测试类
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 测试处理器运行时配置服务的业务逻辑
 * - 测试批量创建、更新、删除、查询等操作
 * - 测试复杂的batchUpsert业务逻辑
 * - 测试异常处理和边界条件
 * 
 * 测试覆盖：
 * - batchCreate() - 批量创建运行时配置
 * - batchUpdate() - 批量更新运行时配置
 * - batchUpsert() - 批量插入或更新运行时配置
 * - batchDelete() - 批量删除运行时配置
 * - findById() - 按ID查询运行时配置
 * - findByUniqueKey() - 按唯一键查询运行时配置
 * - findByObjectApiNameAndInterfaceCode() - 按对象API名称和接口代码查询
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("HandlerRuntimeConfigServiceImpl单元测试")
class HandlerRuntimeConfigServiceImplTest {

    @Mock
    private Repository<HandlerRuntimeConfig> repository;

    @Mock
    private HandlerDefinitionService handlerDefinitionService;

    @InjectMocks
    private HandlerRuntimeConfigServiceImpl handlerRuntimeConfigService;

    // 测试常量
    private static final String TEST_TENANT_ID = "74255";
    private static final String TEST_USER_ID = "test-user-456";
    private static final String TEST_HANDLER_API_NAME = "testHandler";
    private static final String TEST_OBJECT_API_NAME = "TestObject";
    private static final String TEST_INTERFACE_CODE = "before_create";
    private static final String TEST_CONFIG_ID = "config-123";

    // 测试数据
    private User testUser;
    private HandlerRuntimeConfig mockRuntimeConfig;
    private HandlerDefinition mockHandlerDefinition;
    private HandlerRuntimeConfigUniqueKey mockUniqueKey;
    private QueryResult<HandlerRuntimeConfig> mockQueryResult;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = User.systemUser(TEST_TENANT_ID);

        // 创建Mock HandlerRuntimeConfig
        mockRuntimeConfig = mock(HandlerRuntimeConfig.class);
        when(mockRuntimeConfig.getId()).thenReturn(TEST_CONFIG_ID);
        when(mockRuntimeConfig.getHandlerApiName()).thenReturn(TEST_HANDLER_API_NAME);
        when(mockRuntimeConfig.getBindingObjectApiName()).thenReturn(TEST_OBJECT_API_NAME);
        when(mockRuntimeConfig.getInterfaceCode()).thenReturn(TEST_INTERFACE_CODE);
        when(mockRuntimeConfig.getExecuteOrder()).thenReturn(100);
        when(mockRuntimeConfig.isActive()).thenReturn(true);

        // 创建Mock HandlerDefinition
        mockHandlerDefinition = mock(HandlerDefinition.class);
        when(mockHandlerDefinition.getApiName()).thenReturn(TEST_HANDLER_API_NAME);
        when(mockHandlerDefinition.getHandlerType()).thenReturn("before_create");
        when(mockHandlerDefinition.getDefaultOrder()).thenReturn(100);

        // 创建Mock UniqueKey
        mockUniqueKey = mock(HandlerRuntimeConfigUniqueKey.class);
        when(mockUniqueKey.getObjectApiName()).thenReturn(TEST_OBJECT_API_NAME);
        when(mockUniqueKey.getInterfaceCode()).thenReturn(TEST_INTERFACE_CODE);
        when(mockUniqueKey.getHandlerApiName()).thenReturn(TEST_HANDLER_API_NAME);
        when(mockUniqueKey.check()).thenReturn(true);

        // 创建Mock QueryResult
        mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Arrays.asList(mockRuntimeConfig));
    }

    @Test
    @DisplayName("测试batchCreate - 成功批量创建")
    void testBatchCreate_Success() {
        // Arrange
        List<HandlerRuntimeConfig> runtimeConfigs = Arrays.asList(mockRuntimeConfig);

        // Act
        assertDoesNotThrow(() -> {
            handlerRuntimeConfigService.batchCreate(testUser, runtimeConfigs);
        }, "批量创建不应抛出异常");

        // Assert
        verify(repository, times(1)).bulkCreate(testUser, runtimeConfigs);
    }

    @Test
    @DisplayName("测试batchCreate - 空列表")
    void testBatchCreate_EmptyList() {
        // Arrange
        List<HandlerRuntimeConfig> emptyList = new ArrayList<>();

        // Act
        handlerRuntimeConfigService.batchCreate(testUser, emptyList);

        // Assert
        verify(repository, never()).bulkCreate(any(), any());
    }

    @Test
    @DisplayName("测试batchCreate - null列表")
    void testBatchCreate_NullList() {
        // Act
        handlerRuntimeConfigService.batchCreate(testUser, null);

        // Assert
        verify(repository, never()).bulkCreate(any(), any());
    }

    @Test
    @DisplayName("测试batchUpdate - 成功批量更新")
    void testBatchUpdate_Success() {
        // Arrange
        List<HandlerRuntimeConfig> runtimeConfigs = Arrays.asList(mockRuntimeConfig);

        // Act
        assertDoesNotThrow(() -> {
            handlerRuntimeConfigService.batchUpdate(testUser, runtimeConfigs);
        }, "批量更新不应抛出异常");

        // Assert
        verify(repository, times(1)).bulkUpdate(testUser, runtimeConfigs);
    }

    @Test
    @DisplayName("测试batchUpdate - 空列表")
    void testBatchUpdate_EmptyList() {
        // Arrange
        List<HandlerRuntimeConfig> emptyList = new ArrayList<>();

        // Act
        handlerRuntimeConfigService.batchUpdate(testUser, emptyList);

        // Assert
        verify(repository, never()).bulkUpdate(any(), any());
    }

    @Test
    @DisplayName("测试batchDelete - 成功批量删除")
    void testBatchDelete_Success() {
        // Arrange
        List<HandlerRuntimeConfig> runtimeConfigs = Arrays.asList(mockRuntimeConfig);

        // Act
        assertDoesNotThrow(() -> {
            handlerRuntimeConfigService.batchDelete(testUser, runtimeConfigs);
        }, "批量删除不应抛出异常");

        // Assert
        verify(repository, times(1)).bulkInvalid(testUser, runtimeConfigs);
    }

    @Test
    @DisplayName("测试batchDelete - 空列表")
    void testBatchDelete_EmptyList() {
        // Arrange
        List<HandlerRuntimeConfig> emptyList = new ArrayList<>();

        // Act
        handlerRuntimeConfigService.batchDelete(testUser, emptyList);

        // Assert
        verify(repository, never()).bulkInvalid(any(), any());
    }

    @Test
    @DisplayName("测试findById - 成功查询")
    void testFindById_Success() {
        // Arrange
        when(repository.findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class)))
                .thenReturn(mockQueryResult);

        // Act
        HandlerRuntimeConfig result = handlerRuntimeConfigService.findById(TEST_TENANT_ID, TEST_CONFIG_ID);

        // Assert
        assertNotNull(result, "查询结果不应为null");
        assertEquals(mockRuntimeConfig, result, "返回的对象应该一致");
        
        verify(repository, times(1)).findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class));
    }

    @Test
    @DisplayName("测试findById - 空ID")
    void testFindById_EmptyId() {
        // Act
        HandlerRuntimeConfig result = handlerRuntimeConfigService.findById(TEST_TENANT_ID, "");

        // Assert
        assertNull(result, "空ID时结果应该为null");
        
        verify(repository, never()).findByQuery(any(), any(), any());
    }

    @Test
    @DisplayName("测试findById - null ID")
    void testFindById_NullId() {
        // Act
        HandlerRuntimeConfig result = handlerRuntimeConfigService.findById(TEST_TENANT_ID, null);

        // Assert
        assertNull(result, "null ID时结果应该为null");
        
        verify(repository, never()).findByQuery(any(), any(), any());
    }

    @Test
    @DisplayName("测试findByUniqueKey - 成功查询")
    void testFindByUniqueKey_Success() {
        // Arrange
        when(repository.findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class)))
                .thenReturn(mockQueryResult);

        // Act
        HandlerRuntimeConfig result = handlerRuntimeConfigService.findByUniqueKey(TEST_TENANT_ID, mockUniqueKey);

        // Assert
        assertNotNull(result, "查询结果不应为null");
        assertEquals(mockRuntimeConfig, result, "返回的对象应该一致");
        
        verify(repository, times(1)).findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class));
    }

    @Test
    @DisplayName("测试findByUniqueKey - null UniqueKey")
    void testFindByUniqueKey_NullUniqueKey() {
        // Act
        HandlerRuntimeConfig result = handlerRuntimeConfigService.findByUniqueKey(TEST_TENANT_ID, null);

        // Assert
        assertNull(result, "null UniqueKey时结果应该为null");
        
        verify(repository, never()).findByQuery(any(), any(), any());
    }

    @Test
    @DisplayName("测试findByUniqueKey - 无效UniqueKey")
    void testFindByUniqueKey_InvalidUniqueKey() {
        // Arrange
        when(mockUniqueKey.check()).thenReturn(false);

        // Act
        HandlerRuntimeConfig result = handlerRuntimeConfigService.findByUniqueKey(TEST_TENANT_ID, mockUniqueKey);

        // Assert
        assertNull(result, "无效UniqueKey时结果应该为null");
        
        verify(repository, never()).findByQuery(any(), any(), any());
    }

    @Test
    @DisplayName("测试findByObjectApiNameAndInterfaceCode - 成功查询")
    void testFindByObjectApiNameAndInterfaceCode_Success() {
        // Arrange
        when(repository.findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class)))
                .thenReturn(mockQueryResult);

        // Act
        List<HandlerRuntimeConfig> result = handlerRuntimeConfigService.findByObjectApiNameAndInterfaceCode(
                TEST_TENANT_ID, TEST_OBJECT_API_NAME, TEST_INTERFACE_CODE, false, false);

        // Assert
        assertNotNull(result, "查询结果不应为null");
        assertEquals(1, result.size(), "结果数量应该为1");
        assertEquals(mockRuntimeConfig, result.get(0), "返回的对象应该一致");
        
        verify(repository, times(1)).findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class));
    }

    @Test
    @DisplayName("测试findByObjectApiNameAndInterfaceCode - 空参数")
    void testFindByObjectApiNameAndInterfaceCode_EmptyParams() {
        // Act
        List<HandlerRuntimeConfig> result1 = handlerRuntimeConfigService.findByObjectApiNameAndInterfaceCode(
                TEST_TENANT_ID, "", TEST_INTERFACE_CODE, false, false);
        List<HandlerRuntimeConfig> result2 = handlerRuntimeConfigService.findByObjectApiNameAndInterfaceCode(
                TEST_TENANT_ID, TEST_OBJECT_API_NAME, "", false, false);

        // Assert
        assertNotNull(result1, "结果不应为null");
        assertTrue(result1.isEmpty(), "空objectApiName时结果应该是空列表");
        assertNotNull(result2, "结果不应为null");
        assertTrue(result2.isEmpty(), "空interfaceCode时结果应该是空列表");

        verify(repository, never()).findByQuery(any(), any(), any());
    }

    @Test
    @DisplayName("测试batchUpsert - 成功插入或更新")
    void testBatchUpsert_Success() {
        // Arrange
        List<HandlerRuntimeConfig> runtimeConfigs = Arrays.asList(mockRuntimeConfig);

        // Mock HandlerRuntimeConfigUniqueKey.of()
        try (MockedStatic<HandlerRuntimeConfigUniqueKey> mockedUniqueKey = mockStatic(HandlerRuntimeConfigUniqueKey.class)) {
            mockedUniqueKey.when(() -> HandlerRuntimeConfigUniqueKey.of(mockRuntimeConfig)).thenReturn(mockUniqueKey);

            // Mock HandlerDefinitionService
            when(handlerDefinitionService.findByApiNames(eq(TEST_TENANT_ID), anyList(), eq(true), eq(true)))
                    .thenReturn(Arrays.asList(mockHandlerDefinition));

            // Mock HandlerType validation
            try (MockedStatic<HandlerType> mockedHandlerType = mockStatic(HandlerType.class)) {
                HandlerType mockType = mock(HandlerType.class);
                mockedHandlerType.when(() -> HandlerType.getByCode(anyString())).thenReturn(mockType);

                // Mock existing configs query
                when(repository.findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class)))
                        .thenReturn(mockQueryResult);

                // Act
                assertDoesNotThrow(() -> {
                    handlerRuntimeConfigService.batchUpsert(testUser, runtimeConfigs);
                }, "批量插入或更新不应抛出异常");

                // Assert
                verify(handlerDefinitionService, times(1)).findByApiNames(eq(TEST_TENANT_ID), anyList(), eq(true), eq(true));
                verify(repository, atLeastOnce()).findByQuery(any(User.class), any(Query.class), eq(HandlerRuntimeConfig.class));
            }
        }
    }

    @Test
    @DisplayName("测试batchUpsert - 空列表")
    void testBatchUpsert_EmptyList() {
        // Arrange
        List<HandlerRuntimeConfig> emptyList = new ArrayList<>();

        // Act
        handlerRuntimeConfigService.batchUpsert(testUser, emptyList);

        // Assert
        verify(handlerDefinitionService, never()).findByApiNames(any(), any(), anyBoolean(), anyBoolean());
        verify(repository, never()).bulkCreate(any(), any());
        verify(repository, never()).bulkUpdate(any(), any());
    }

    @Test
    @DisplayName("测试batchUpsert - UniqueKey校验失败")
    void testBatchUpsert_ThrowsValidateExceptionForInvalidUniqueKey() {
        // Arrange
        List<HandlerRuntimeConfig> runtimeConfigs = Arrays.asList(mockRuntimeConfig);

        // Mock HandlerRuntimeConfigUniqueKey.of() 返回无效的UniqueKey
        try (MockedStatic<HandlerRuntimeConfigUniqueKey> mockedUniqueKey = mockStatic(HandlerRuntimeConfigUniqueKey.class)) {
            HandlerRuntimeConfigUniqueKey invalidUniqueKey = mock(HandlerRuntimeConfigUniqueKey.class);
            when(invalidUniqueKey.check()).thenReturn(false);
            mockedUniqueKey.when(() -> HandlerRuntimeConfigUniqueKey.of(mockRuntimeConfig)).thenReturn(invalidUniqueKey);

            // Act & Assert
            assertThrows(ValidateException.class, () -> {
                handlerRuntimeConfigService.batchUpsert(testUser, runtimeConfigs);
            }, "无效UniqueKey应该抛出ValidateException");
        }
    }

    @Test
    @DisplayName("测试batchUpsert - 重复UniqueKey")
    void testBatchUpsert_ThrowsValidateExceptionForDuplicateUniqueKey() {
        // Arrange
        HandlerRuntimeConfig duplicateConfig = mock(HandlerRuntimeConfig.class);
        when(duplicateConfig.getHandlerApiName()).thenReturn(TEST_HANDLER_API_NAME);
        when(duplicateConfig.getBindingObjectApiName()).thenReturn(TEST_OBJECT_API_NAME);
        when(duplicateConfig.getInterfaceCode()).thenReturn(TEST_INTERFACE_CODE);

        List<HandlerRuntimeConfig> runtimeConfigs = Arrays.asList(mockRuntimeConfig, duplicateConfig);

        // Mock HandlerRuntimeConfigUniqueKey.of() 返回相同的UniqueKey
        try (MockedStatic<HandlerRuntimeConfigUniqueKey> mockedUniqueKey = mockStatic(HandlerRuntimeConfigUniqueKey.class)) {
            mockedUniqueKey.when(() -> HandlerRuntimeConfigUniqueKey.of(any(HandlerRuntimeConfig.class))).thenReturn(mockUniqueKey);

            // Act & Assert
            assertThrows(ValidateException.class, () -> {
                handlerRuntimeConfigService.batchUpsert(testUser, runtimeConfigs);
            }, "重复UniqueKey应该抛出ValidateException");
        }
    }

    @Test
    @DisplayName("测试batchUpsert - HandlerDefinition不存在")
    void testBatchUpsert_ThrowsValidateExceptionForMissingHandlerDefinition() {
        // Arrange
        List<HandlerRuntimeConfig> runtimeConfigs = Arrays.asList(mockRuntimeConfig);

        // Mock HandlerRuntimeConfigUniqueKey.of()
        try (MockedStatic<HandlerRuntimeConfigUniqueKey> mockedUniqueKey = mockStatic(HandlerRuntimeConfigUniqueKey.class)) {
            mockedUniqueKey.when(() -> HandlerRuntimeConfigUniqueKey.of(mockRuntimeConfig)).thenReturn(mockUniqueKey);

            // Mock HandlerDefinitionService 返回空列表
            when(handlerDefinitionService.findByApiNames(eq(TEST_TENANT_ID), anyList(), eq(true), eq(true)))
                    .thenReturn(new ArrayList<>());

            // Act & Assert
            assertThrows(ValidateException.class, () -> {
                handlerRuntimeConfigService.batchUpsert(testUser, runtimeConfigs);
            }, "HandlerDefinition不存在应该抛出ValidateException");
        }
    }
}
