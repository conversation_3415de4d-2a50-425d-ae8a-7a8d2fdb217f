package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.util.SfaGrayUtil;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BomCoreObjectImportProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IUniqueRule uniqueRule;

    @Mock
    private RequestContext requestContext;

    @Spy
    private BomCoreObjectImportProvider bomCoreObjectImportProvider;

    private String testTenantId;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        when(requestContext.getTenantId()).thenReturn(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的BOM核心API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的BOM核心API名称")
    void testGetObjectCode_返回正确的BOM核心API名称() {
        // 执行被测试方法
        String result = bomCoreObjectImportProvider.getObjectCode();

        // 验证结果
        assertEquals(Utils.BOM_CORE_API_NAME, result);
        assertEquals("BomCoreObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectName方法调用父类方法
     */
    @Test
    @DisplayName("getObjectName - 调用父类方法")
    void testGetObjectName_调用父类方法() {
        // 准备测试数据
        String expectedObjectName = "BOM核心对象";
        doReturn(expectedObjectName).when(bomCoreObjectImportProvider, "getObjectName", objectDescribe);

        // 执行被测试方法
        String result = bomCoreObjectImportProvider.getObjectName(objectDescribe);

        // 验证结果
        assertEquals(expectedObjectName, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当CPQ功能开启时，getImportObject方法调用父类方法
     */
    @Test
    @DisplayName("getImportObject - CPQ功能开启时调用父类方法")
    void testGetImportObject_CPQ功能开启时调用父类方法() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<SfaGrayUtil> mockedSfaGrayUtil = mockStatic(SfaGrayUtil.class)) {

            // 准备测试数据
            ImportObject expectedImportObject = ImportObject.builder()
                    .objectCode(Utils.BOM_CORE_API_NAME)
                    .objectName("BOM核心对象")
                    .build();

            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(requestContext);
            mockedSfaGrayUtil.when(() -> SfaGrayUtil.openCPQ(testTenantId)).thenReturn(true);
            doReturn(Optional.of(expectedImportObject))
                    .when(bomCoreObjectImportProvider, "getImportObject", objectDescribe, uniqueRule);

            // 执行被测试方法
            Optional<ImportObject> result = bomCoreObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertTrue(result.isPresent());
            assertEquals(expectedImportObject, result.get());

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
            mockedSfaGrayUtil.verify(() -> SfaGrayUtil.openCPQ(testTenantId));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当CPQ功能关闭时，getImportObject方法返回空Optional
     */
    @Test
    @DisplayName("getImportObject - CPQ功能关闭时返回空Optional")
    void testGetImportObject_CPQ功能关闭时返回空Optional() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<SfaGrayUtil> mockedSfaGrayUtil = mockStatic(SfaGrayUtil.class)) {

            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(requestContext);
            mockedSfaGrayUtil.when(() -> SfaGrayUtil.openCPQ(testTenantId)).thenReturn(false);

            // 执行被测试方法
            Optional<ImportObject> result = bomCoreObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertFalse(result.isPresent());
            assertTrue(result.isEmpty());

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
            mockedSfaGrayUtil.verify(() -> SfaGrayUtil.openCPQ(testTenantId));
            // 验证没有调用父类方法
            verify(bomCoreObjectImportProvider, never()).getImportObject(any(IObjectDescribe.class), any(IUniqueRule.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportType方法返回不支持新建导入类型
     */
    @Test
    @DisplayName("getImportType - 返回不支持新建导入类型")
    void testGetImportType_返回不支持新建导入类型() {
        // 执行被测试方法
        ImportType result = bomCoreObjectImportProvider.getImportType(objectDescribe, uniqueRule);

        // 验证结果
        assertEquals(ImportType.UNSUPPORT_INSERT_IMPORT, result);
        assertEquals(3, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOpenWorkFlow方法返回true
     */
    @Test
    @DisplayName("getOpenWorkFlow - 返回true")
    void testGetOpenWorkFlow_返回true() {
        // 执行被测试方法
        boolean result = bomCoreObjectImportProvider.getOpenWorkFlow(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsCheckOutOwner方法返回true
     */
    @Test
    @DisplayName("getIsCheckOutOwner - 返回true")
    void testGetIsCheckOutOwner_返回true() {
        // 执行被测试方法
        boolean result = bomCoreObjectImportProvider.getIsCheckOutOwner(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsRemoveOutTeamMember方法返回true
     */
    @Test
    @DisplayName("getIsRemoveOutTeamMember - 返回true")
    void testGetIsRemoveOutTeamMember_返回true() {
        // 执行被测试方法
        boolean result = bomCoreObjectImportProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当RequestContext为null时，getImportObject方法的异常处理
     */
    @Test
    @DisplayName("getImportObject - RequestContext为null时的异常处理")
    void testGetImportObjectThrowsException_RequestContext为null时的异常处理() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            // 执行并验证异常
            Exception exception = assertThrows(NullPointerException.class, () -> {
                bomCoreObjectImportProvider.getImportObject(objectDescribe, uniqueRule);
            });

            // 验证异常信息
            assertNotNull(exception);

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当tenantId为null时，CPQ功能检查的处理
     */
    @Test
    @DisplayName("getImportObject - tenantId为null时的处理")
    void testGetImportObject_tenantId为null时的处理() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<SfaGrayUtil> mockedSfaGrayUtil = mockStatic(SfaGrayUtil.class)) {

            // 准备测试数据
            when(requestContext.getTenantId()).thenReturn(null);

            // 配置Mock行为
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(requestContext);
            mockedSfaGrayUtil.when(() -> SfaGrayUtil.openCPQ(null)).thenReturn(false);

            // 执行被测试方法
            Optional<ImportObject> result = bomCoreObjectImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertFalse(result.isPresent());

            // 验证Mock交互
            mockedRequestContextManager.verify(RequestContextManager::getContext);
            mockedSfaGrayUtil.verify(() -> SfaGrayUtil.openCPQ(null));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - objectDescribe为null时的处理")
    void test各方法_objectDescribe为null时的处理() {
        // 测试getImportType
        ImportType importType = bomCoreObjectImportProvider.getImportType(null, uniqueRule);
        assertEquals(ImportType.UNSUPPORT_INSERT_IMPORT, importType);

        // 测试布尔方法
        boolean openWorkFlow = bomCoreObjectImportProvider.getOpenWorkFlow(null);
        boolean checkOutOwner = bomCoreObjectImportProvider.getIsCheckOutOwner(null);
        boolean removeOutTeamMember = bomCoreObjectImportProvider.getIsRemoveOutTeamMember(null);

        // 验证结果
        assertTrue(openWorkFlow);
        assertTrue(checkOutOwner);
        assertTrue(removeOutTeamMember);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试BOM核心对象的业务特性 - 不支持新建导入但支持工作流
     */
    @Test
    @DisplayName("业务特性 - BOM核心对象不支持新建导入但支持工作流")
    void test业务特性_BOM核心对象不支持新建导入但支持工作流() {
        // 执行被测试方法
        String objectCode = bomCoreObjectImportProvider.getObjectCode();
        ImportType importType = bomCoreObjectImportProvider.getImportType(objectDescribe, uniqueRule);
        boolean openWorkFlow = bomCoreObjectImportProvider.getOpenWorkFlow(objectDescribe);
        boolean checkOutOwner = bomCoreObjectImportProvider.getIsCheckOutOwner(objectDescribe);
        boolean removeOutTeamMember = bomCoreObjectImportProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证业务特性
        assertEquals(Utils.BOM_CORE_API_NAME, objectCode);
        assertEquals(ImportType.UNSUPPORT_INSERT_IMPORT, importType);
        assertTrue(openWorkFlow);
        assertTrue(checkOutOwner);
        assertTrue(removeOutTeamMember);

        // 验证不支持新建导入的特性
        assertNotEquals(ImportType.DEFAULT, importType);
        assertNotEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, importType);
        assertEquals(3, importType.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试所有布尔方法的一致性，确保都返回true
     */
    @Test
    @DisplayName("布尔方法 - 一致性测试确保都返回true")
    void test布尔方法_一致性测试确保都返回true() {
        // 执行所有布尔方法
        boolean openWorkFlow = bomCoreObjectImportProvider.getOpenWorkFlow(objectDescribe);
        boolean checkOutOwner = bomCoreObjectImportProvider.getIsCheckOutOwner(objectDescribe);
        boolean removeOutTeamMember = bomCoreObjectImportProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证结果
        assertTrue(openWorkFlow);
        assertTrue(checkOutOwner);
        assertTrue(removeOutTeamMember);

        // 验证一致性
        assertEquals(openWorkFlow, checkOutOwner);
        assertEquals(checkOutOwner, removeOutTeamMember);
    }
}
