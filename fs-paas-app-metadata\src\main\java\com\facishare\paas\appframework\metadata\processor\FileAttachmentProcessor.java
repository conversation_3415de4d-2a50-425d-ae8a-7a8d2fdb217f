package com.facishare.paas.appframework.metadata.processor;

import com.facishare.paas.appframework.metadata.FileStoreService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by zhouwr on 2017/8/24
 */
@Component
public class FileAttachmentProcessor extends AbstractFileFieldProcessor {

    @Resource
    private FileStoreService fileStoreService;

    @Override
    protected String getFinalFilePath(String tenantId, String userId, String tempFilePath, String fileName,
                                      String fileExt) {
        FileStoreService.PathOriginNames pathOriginNames = FileStoreService.PathOriginNames.of(tempFilePath, fileName);
        String finalFilePath = fileStoreService.saveFileFromTempFile(tenantId, userId, pathOriginNames, fileExt);
        if (StringUtils.isNotBlank(finalFilePath) && StringUtils.isNotBlank(fileExt) && !finalFilePath.endsWith(fileExt)) {
            finalFilePath = finalFilePath + EXT_SEPARATOR + fileExt;
        }
        return finalFilePath;
    }
}
