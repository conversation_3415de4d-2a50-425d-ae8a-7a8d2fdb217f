package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext.BizInfo;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig<PERSON>ey;
import com.facishare.paas.appframework.metadata.dto.DataSnapshotResult;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IDataSnapshot;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.RichText;
import com.facishare.paas.metadata.api.service.IDataSnapshotService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DataSnapshot;
import com.facishare.paas.metadata.impl.search.DataSnapshotSearchPara;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service("dataSnapshotLogicService")
public class DataSnapshotLogicServiceImpl implements DataSnapshotLogicService {

    @Autowired
    private IDataSnapshotService dataSnapshotService;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private FileStoreService fileStoreService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public DataSnapshotResult createSnapshotWithData(User user, IObjectData newData, IObjectData oldData,
                                                     IObjectDescribe describe, BizInfo bizInfo) {
        String biz = bizInfo.getBiz();
        String bizId = bizInfo.getOtherBizId();
        String subBizId = bizInfo.getBizId();

        //临时文件转正式文件
        metaDataService.processData(describe, Lists.newArrayList(newData));
        //拷贝一下，防止外面需要使用原始值
        oldData = ObjectDataExt.of(oldData).copy();
        ObjectDataSnapshot snapshot = findAndMergeSnapshot(user.getTenantId(), describe.getApiName(), oldData.getId(), biz, bizId);
        ObjectDataExt.of(oldData).putAll(snapshot.getMasterSnapshot());
        Map<String, Object> diffMap = ObjectDataExt.of(oldData).diff(newData, describe);

        ObjectDataSnapshot newSnapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(diffMap)
                .biz(biz)
                .bizId(bizId)
                .subBizId(subBizId)
                .build();
        createSnapshot(user, oldData.getDescribeApiName(), oldData.getId(), newSnapshot);

        return DataSnapshotResult.of(snapshot, diffMap);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void createSnapshot(User user, String describeApiName, String dataId, ObjectDataSnapshot snapshot) {
        if (CollectionUtils.empty(snapshot.getMasterSnapshot()) && CollectionUtils.empty(snapshot.getDetailSnapshot())) {
            return;
        }

        IDataSnapshot dataSnapshot = new DataSnapshot();
        dataSnapshot.setTenantId(user.getTenantId());
        dataSnapshot.setDescribeApiName(describeApiName);
        dataSnapshot.setDataId(dataId);

        // Clean signedUrl from masterSnapshot
        fileStoreService.cleanSignedUrl(snapshot.getMasterSnapshot());
        dataSnapshot.setMasterSnapshot(snapshot.getMasterSnapshot());

        // Clean signedUrl from slaveSnapshot (detailSnapshot)
        fileStoreService.cleanNestedSignedUrl(snapshot.getDetailSnapshot());
        dataSnapshot.setSlaveSnapshot((Map) snapshot.getDetailSnapshot());

        dataSnapshot.setBiz(snapshot.getBiz());
        dataSnapshot.setBizId(snapshot.getBizId());
        dataSnapshot.setSubBizId(snapshot.getSubBizId());

        IActionContext context = ActionContextExt.of(user).getContext();
        try {
            dataSnapshotService.create(dataSnapshot, context);
        } catch (MetadataServiceException e) {
            log.warn("createSnapshot failed,tenantId:{},dataSnapshot:{}", user.getTenantId(), JacksonUtils.toJson(dataSnapshot), e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ObjectDataSnapshot findAndMergeSnapshot(String tenantId, String describeApiName, String dataId, String biz, String bizId) {
        if (Strings.isNullOrEmpty(biz) || Strings.isNullOrEmpty(bizId)) {
            return null;
        }
        DataSnapshotSearchPara para = DataSnapshotSearchPara.builder()
                .tenantId(tenantId)
                .describeApiName(describeApiName)
                .dataId(dataId)
                .biz(biz)
                .bizId(bizId)
                .build();
        try {
            ObjectDataSnapshot result = new ObjectDataSnapshot(Maps.newHashMap(), Maps.newHashMap(), biz, bizId, null);
            List<IDataSnapshot> snapshotList = dataSnapshotService.find(para);
            if (CollectionUtils.empty(snapshotList)) {
                return result;
            }
            Collections.sort(snapshotList, Comparator.comparing(IDataSnapshot::getVersion));
            snapshotList.forEach(snapshot -> result.mergeFrom(snapshot));

            // Generate signed URLs for images in snapshots
            IActionContext ctx = ActionContextExt.of(RequestContextManager.getContext().getUser()).getContext();
            fileStoreService.generateSignedUrl(result.getMasterSnapshot(), AuthModel.SIGN, ctx);
            fileStoreService.generateNestedSignedUrl(result.getDetailSnapshot(), AuthModel.SIGN, ctx);
            
            return result;
        } catch (MetadataServiceException e) {
            log.warn("findAndMergeSnapshot failed,tenantId:{},para:{}", tenantId, para, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void deleteSnapshot(User user, String describeApiName, String dataId, String biz, String bizId) {
        DataSnapshotSearchPara para = DataSnapshotSearchPara.builder()
                .tenantId(user.getTenantId())
                .describeApiName(describeApiName)
                .dataId(dataId)
                .biz(biz)
                .bizId(bizId)
                .build();
        try {
            dataSnapshotService.delete(para);
        } catch (MetadataServiceException e) {
            log.warn("deleteSnapshot failed,tenantId:{},para:{}", user.getTenantId(), para, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void updateSnapshot(User user, IDataSnapshot dataSnapshot) {
        if (Strings.isNullOrEmpty(dataSnapshot.getId())
                || Strings.isNullOrEmpty(dataSnapshot.getDataId())
                || Strings.isNullOrEmpty(dataSnapshot.getBiz())
                || Strings.isNullOrEmpty(dataSnapshot.getBizId())
                || Objects.isNull(dataSnapshot.getDescribeApiName())) {
            throw new ValidateException(I18nMessage.of(I18NKey.PARAM_ERROR, I18N.text(I18NKey.PARAM_ERROR)));
        }
        if (CollectionUtils.empty(dataSnapshot.getMasterSnapshot()) && CollectionUtils.empty(dataSnapshot.getSlaveSnapshot())) {
            throw new ValidateException(I18nMessage.of(I18NKey.PARAM_ERROR, I18N.text(I18NKey.PARAM_ERROR)));
        }
        try {
            DataSnapshotSearchPara para = DataSnapshotSearchPara.builder()
                    .tenantId(user.getTenantId())
                    .describeApiName(dataSnapshot.getDescribeApiName())
                    .dataId(dataSnapshot.getDataId())
                    .biz(dataSnapshot.getBiz())
                    .bizId(dataSnapshot.getBizId())
                    .build();
            List<IDataSnapshot> snapshotList = dataSnapshotService.find(para);
            if (CollectionUtils.empty(snapshotList)) {
                return;
            }
            IDataSnapshot dataSnapshotToUpdate = snapshotList.stream().filter(x -> Objects.equals(x.getId(), dataSnapshot.getId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(dataSnapshotToUpdate)) {
                return;
            }
            if (Objects.nonNull(dataSnapshot.getMasterSnapshot())) {
                fileStoreService.cleanSignedUrl(dataSnapshot.getMasterSnapshot());
                dataSnapshotToUpdate.setMasterSnapshot(dataSnapshot.getMasterSnapshot());
            }
            if (Objects.nonNull(dataSnapshot.getSlaveSnapshot())) {
                fileStoreService.cleanNestedSignedUrl((Map) dataSnapshot.getSlaveSnapshot());
                dataSnapshotToUpdate.setSlaveSnapshot(dataSnapshot.getSlaveSnapshot());
            }
            IActionContext context = ActionContextExt.of(user).getContext();
            dataSnapshotService.bulkUpdate(Lists.newArrayList(dataSnapshotToUpdate), context);
        } catch (MetadataServiceException e) {
            log.warn("updateSnapshot failed,tenantId:{},dataSnapshot:{}", user.getTenantId(), JacksonUtils.toJson(dataSnapshot), e);
            throw new MetaDataBusinessException(e);
        }
    }
}
