package com.facishare.paas.appframework.metadata.layout.component;

import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SummaryComponentInfo的JUnit 5测试类
 * 测试汇总组件信息类功能
 */
class SummaryComponentInfoJUnit5Test {

    private SummaryComponentInfo summaryComponentInfo;

    @BeforeEach
    void setUp() {
        summaryComponentInfo = new SummaryComponentInfo();
    }

    /**
     * 测试内容描述：测试默认构造函数
     */
    @Test
    @DisplayName("构造函数 - 默认构造函数")
    void testDefaultConstructor() {
        // Act: 创建SummaryComponentInfo实例
        SummaryComponentInfo result = new SummaryComponentInfo();
        
        // Assert: 验证默认构造函数
        assertNotNull(result);
        assertNull(result.getApiName());
        assertNull(result.getType());
        assertNull(result.getFieldName());
        assertNull(result.getRenderType());
        assertNull(result.getPageType());
    }

    /**
     * 测试内容描述：测试Map构造函数
     */
    @Test
    @DisplayName("构造函数 - Map构造函数")
    void testMapConstructor() {
        // Arrange: 准备测试数据
        Map<String, Object> map = Maps.newHashMap();
        map.put(ISummaryComponentInfo.API_NAME, "total_amount");
        map.put(ISummaryComponentInfo.TYPE, "sum");
        map.put(ISummaryComponentInfo.FIELD_NAME, "amount");
        map.put(IComponentInfo.PAGE_TYPE, "list");
        map.put(IComponentInfo.RENDER_TYPE, "number");
        
        // Act: 创建SummaryComponentInfo实例
        SummaryComponentInfo result = new SummaryComponentInfo(map);
        
        // Assert: 验证Map构造函数
        assertNotNull(result);
        assertEquals("total_amount", result.getApiName());
        assertEquals("sum", result.getType());
        assertEquals("amount", result.getFieldName());
        assertEquals("list", result.getPageType());
        assertEquals("number", result.getRenderType());
    }

    /**
     * 测试内容描述：测试Map构造函数 - null参数
     */
    @Test
    @DisplayName("边界条件 - Map构造函数null参数")
    void testMapConstructor_NullParameter() {
        // Act: 创建SummaryComponentInfo实例，参数为null
        SummaryComponentInfo result = new SummaryComponentInfo(null);

        // Assert: 验证结果 - 对象创建成功但访问方法会抛出异常
        assertNotNull(result);

        // 验证访问方法会抛出NullPointerException，因为内部map为null
        assertThrows(NullPointerException.class, () -> result.getApiName());
        assertThrows(NullPointerException.class, () -> result.getType());
        assertThrows(NullPointerException.class, () -> result.getFieldName());
        assertThrows(NullPointerException.class, () -> result.getPageType());
        assertThrows(NullPointerException.class, () -> result.getRenderType());
    }

    /**
     * 测试内容描述：测试Map构造函数 - 空Map参数
     */
    @Test
    @DisplayName("边界条件 - Map构造函数空Map参数")
    void testMapConstructor_EmptyMap() {
        // Arrange: 准备空Map
        Map<String, Object> emptyMap = Maps.newHashMap();
        
        // Act: 创建SummaryComponentInfo实例
        SummaryComponentInfo result = new SummaryComponentInfo(emptyMap);
        
        // Assert: 验证结果
        assertNotNull(result);
        assertNull(result.getApiName());
        assertNull(result.getType());
        assertNull(result.getFieldName());
        assertNull(result.getPageType());
        assertNull(result.getRenderType());
    }

    /**
     * 测试内容描述：测试getApiName方法
     */
    @Test
    @DisplayName("访问器方法 - getApiName方法")
    void testGetApiName() {
        // Arrange: 设置API名称
        summaryComponentInfo.setApiName("total_sales");
        
        // Act: 执行getApiName方法
        String result = summaryComponentInfo.getApiName();
        
        // Assert: 验证结果
        assertEquals("total_sales", result);
    }

    /**
     * 测试内容描述：测试getApiName方法 - 无值情况
     */
    @Test
    @DisplayName("边界条件 - getApiName方法无值")
    void testGetApiName_NoValue() {
        // Act: 执行getApiName方法
        String result = summaryComponentInfo.getApiName();
        
        // Assert: 验证结果
        assertNull(result);
    }

    /**
     * 测试内容描述：测试setApiName方法
     */
    @Test
    @DisplayName("访问器方法 - setApiName方法")
    void testSetApiName() {
        // Arrange: 准备测试数据
        String apiName = "average_score";
        
        // Act: 执行setApiName方法
        summaryComponentInfo.setApiName(apiName);
        
        // Assert: 验证结果
        String result = summaryComponentInfo.getApiName();
        assertEquals(apiName, result);
    }

    /**
     * 测试内容描述：测试setApiName方法 - null参数
     */
    @Test
    @DisplayName("边界条件 - setApiName方法null参数")
    void testSetApiName_NullParameter() {
        // Act: 执行setApiName方法，参数为null
        summaryComponentInfo.setApiName(null);
        
        // Assert: 验证结果
        String result = summaryComponentInfo.getApiName();
        assertNull(result);
    }

    /**
     * 测试内容描述：测试setApiName方法 - 空字符串参数
     */
    @Test
    @DisplayName("边界条件 - setApiName方法空字符串参数")
    void testSetApiName_EmptyString() {
        // Act: 执行setApiName方法，参数为空字符串
        summaryComponentInfo.setApiName("");
        
        // Assert: 验证结果
        String result = summaryComponentInfo.getApiName();
        assertEquals("", result);
    }

    /**
     * 测试内容描述：测试getType方法
     */
    @Test
    @DisplayName("访问器方法 - getType方法")
    void testGetType() {
        // Arrange: 设置类型
        summaryComponentInfo.setType("count");
        
        // Act: 执行getType方法
        String result = summaryComponentInfo.getType();
        
        // Assert: 验证结果
        assertEquals("count", result);
    }

    /**
     * 测试内容描述：测试getType方法 - 无值情况
     */
    @Test
    @DisplayName("边界条件 - getType方法无值")
    void testGetType_NoValue() {
        // Act: 执行getType方法
        String result = summaryComponentInfo.getType();
        
        // Assert: 验证结果
        assertNull(result);
    }

    /**
     * 测试内容描述：测试setType方法
     */
    @Test
    @DisplayName("访问器方法 - setType方法")
    void testSetType() {
        // Arrange: 准备测试数据
        String type = "max";
        
        // Act: 执行setType方法
        summaryComponentInfo.setType(type);
        
        // Assert: 验证结果
        String result = summaryComponentInfo.getType();
        assertEquals(type, result);
    }

    /**
     * 测试内容描述：测试setType方法 - null参数
     */
    @Test
    @DisplayName("边界条件 - setType方法null参数")
    void testSetType_NullParameter() {
        // Act: 执行setType方法，参数为null
        summaryComponentInfo.setType(null);
        
        // Assert: 验证结果
        String result = summaryComponentInfo.getType();
        assertNull(result);
    }

    /**
     * 测试内容描述：测试getFieldName方法
     */
    @Test
    @DisplayName("访问器方法 - getFieldName方法")
    void testGetFieldName() {
        // Arrange: 设置字段名称
        summaryComponentInfo.setFieldName("price");
        
        // Act: 执行getFieldName方法
        String result = summaryComponentInfo.getFieldName();
        
        // Assert: 验证结果
        assertEquals("price", result);
    }

    /**
     * 测试内容描述：测试getFieldName方法 - 无值情况
     */
    @Test
    @DisplayName("边界条件 - getFieldName方法无值")
    void testGetFieldName_NoValue() {
        // Act: 执行getFieldName方法
        String result = summaryComponentInfo.getFieldName();
        
        // Assert: 验证结果
        assertNull(result);
    }

    /**
     * 测试内容描述：测试setFieldName方法
     */
    @Test
    @DisplayName("访问器方法 - setFieldName方法")
    void testSetFieldName() {
        // Arrange: 准备测试数据
        String fieldName = "quantity";
        
        // Act: 执行setFieldName方法
        summaryComponentInfo.setFieldName(fieldName);
        
        // Assert: 验证结果
        String result = summaryComponentInfo.getFieldName();
        assertEquals(fieldName, result);
    }

    /**
     * 测试内容描述：测试setFieldName方法 - null参数
     */
    @Test
    @DisplayName("边界条件 - setFieldName方法null参数")
    void testSetFieldName_NullParameter() {
        // Act: 执行setFieldName方法，参数为null
        summaryComponentInfo.setFieldName(null);
        
        // Assert: 验证结果
        String result = summaryComponentInfo.getFieldName();
        assertNull(result);
    }

    /**
     * 测试内容描述：测试toMap方法
     */
    @Test
    @DisplayName("访问器方法 - toMap方法")
    void testToMap() {
        // Arrange: 设置数据
        summaryComponentInfo.setApiName("total_revenue");
        summaryComponentInfo.setType("sum");
        summaryComponentInfo.setFieldName("revenue");
        
        // Act: 执行toMap方法
        Map<String, Object> result = summaryComponentInfo.toMap();
        
        // Assert: 验证结果
        assertNotNull(result);
        assertEquals("total_revenue", result.get(ISummaryComponentInfo.API_NAME));
        assertEquals("sum", result.get(ISummaryComponentInfo.TYPE));
        assertEquals("revenue", result.get(ISummaryComponentInfo.FIELD_NAME));
    }

    /**
     * 测试内容描述：测试toMap方法 - 空对象
     */
    @Test
    @DisplayName("边界条件 - toMap方法空对象")
    void testToMap_EmptyObject() {
        // Act: 执行toMap方法
        Map<String, Object> result = summaryComponentInfo.toMap();
        
        // Assert: 验证结果
        assertNotNull(result);
        // 空对象的toMap应该返回空的Map或包含null值的Map
    }

    /**
     * 测试内容描述：测试getRenderType方法
     */
    @Test
    @DisplayName("访问器方法 - getRenderType方法")
    void testGetRenderType() {
        // Arrange: 设置渲染类型
        Map<String, Object> map = Maps.newHashMap();
        map.put(IComponentInfo.RENDER_TYPE, "chart");
        summaryComponentInfo = new SummaryComponentInfo(map);
        
        // Act: 执行getRenderType方法
        String result = summaryComponentInfo.getRenderType();
        
        // Assert: 验证结果
        assertEquals("chart", result);
    }

    /**
     * 测试内容描述：测试getRenderType方法 - 无值情况
     */
    @Test
    @DisplayName("边界条件 - getRenderType方法无值")
    void testGetRenderType_NoValue() {
        // Act: 执行getRenderType方法
        String result = summaryComponentInfo.getRenderType();
        
        // Assert: 验证结果
        assertNull(result);
    }

    /**
     * 测试内容描述：测试getPageType方法
     */
    @Test
    @DisplayName("访问器方法 - getPageType方法")
    void testGetPageType() {
        // Arrange: 设置页面类型
        Map<String, Object> map = Maps.newHashMap();
        map.put(IComponentInfo.PAGE_TYPE, "dashboard");
        summaryComponentInfo = new SummaryComponentInfo(map);
        
        // Act: 执行getPageType方法
        String result = summaryComponentInfo.getPageType();
        
        // Assert: 验证结果
        assertEquals("dashboard", result);
    }

    /**
     * 测试内容描述：测试getPageType方法 - 无值情况
     */
    @Test
    @DisplayName("边界条件 - getPageType方法无值")
    void testGetPageType_NoValue() {
        // Act: 执行getPageType方法
        String result = summaryComponentInfo.getPageType();
        
        // Assert: 验证结果
        assertNull(result);
    }

    /**
     * 测试内容描述：测试copy方法
     */
    @Test
    @DisplayName("访问器方法 - copy方法")
    void testCopy() {
        // Arrange: 设置原始数据
        String apiName = "total_orders";
        String type = "count";
        String fieldName = "order_id";
        String pageType = "list";
        String renderType = "number";
        
        Map<String, Object> map = Maps.newHashMap();
        map.put(ISummaryComponentInfo.API_NAME, apiName);
        map.put(ISummaryComponentInfo.TYPE, type);
        map.put(ISummaryComponentInfo.FIELD_NAME, fieldName);
        map.put(IComponentInfo.PAGE_TYPE, pageType);
        map.put(IComponentInfo.RENDER_TYPE, renderType);
        summaryComponentInfo = new SummaryComponentInfo(map);
        
        // Act: 执行copy方法
        ISummaryComponentInfo result = summaryComponentInfo.copy();
        
        // Assert: 验证copy方法
        assertNotNull(result);
        assertNotSame(summaryComponentInfo, result);
        assertEquals(summaryComponentInfo.getApiName(), result.getApiName());
        assertEquals(summaryComponentInfo.getType(), result.getType());
        assertEquals(summaryComponentInfo.getFieldName(), result.getFieldName());
        assertEquals(summaryComponentInfo.getPageType(), result.getPageType());
        assertEquals(summaryComponentInfo.getRenderType(), result.getRenderType());
    }

    /**
     * 测试内容描述：测试copy方法 - 空对象
     */
    @Test
    @DisplayName("边界条件 - copy方法空对象")
    void testCopy_EmptyObject() {
        // Act: 执行copy方法
        ISummaryComponentInfo result = summaryComponentInfo.copy();
        
        // Assert: 验证copy方法
        assertNotNull(result);
        assertNotSame(summaryComponentInfo, result);
        assertEquals(summaryComponentInfo.getApiName(), result.getApiName());
        assertEquals(summaryComponentInfo.getType(), result.getType());
        assertEquals(summaryComponentInfo.getFieldName(), result.getFieldName());
        assertEquals(summaryComponentInfo.getPageType(), result.getPageType());
        assertEquals(summaryComponentInfo.getRenderType(), result.getRenderType());
    }

    /**
     * 测试内容描述：测试接口实现
     */
    @Test
    @DisplayName("接口验证 - 接口实现验证")
    void testInterfaceImplementation() {
        // Assert: 验证接口实现
        assertTrue(summaryComponentInfo instanceof ISummaryComponentInfo);
        assertTrue(summaryComponentInfo instanceof IComponentInfo);
        assertTrue(summaryComponentInfo instanceof com.facishare.paas.metadata.api.JsonCompatible);
    }

    /**
     * 测试内容描述：测试继承关系
     */
    @Test
    @DisplayName("继承验证 - 继承关系验证")
    void testInheritance() {
        // Assert: 验证继承关系
        assertTrue(summaryComponentInfo instanceof com.facishare.paas.metadata.impl.DocumentBasedBean);
    }

    /**
     * 测试内容描述：测试序列化版本号
     */
    @Test
    @DisplayName("序列化 - 序列化版本号验证")
    void testSerialVersionUID() {
        // Act & Assert: 验证序列化版本号存在
        try {
            java.lang.reflect.Field field = SummaryComponentInfo.class.getDeclaredField("serialVersionUID");
            field.setAccessible(true);
            long serialVersionUID = field.getLong(null);
            assertEquals(-7583742308956510342L, serialVersionUID);
        } catch (Exception e) {
            fail("序列化版本号验证失败: " + e.getMessage());
        }
    }

    /**
     * 测试内容描述：测试业务场景 - 汇总统计配置
     */
    @Test
    @DisplayName("业务场景 - 汇总统计配置")
    void testSummaryStatisticsConfiguration() {
        // Arrange: 模拟不同汇总统计配置
        String[][] summaryConfigs = {
            {"total_amount", "sum", "amount"},
            {"order_count", "count", "order_id"},
            {"max_price", "max", "price"},
            {"min_price", "min", "price"},
            {"avg_score", "average", "score"}
        };
        
        for (String[] config : summaryConfigs) {
            // Act: 配置汇总统计
            SummaryComponentInfo component = new SummaryComponentInfo();
            component.setApiName(config[0]);
            component.setType(config[1]);
            component.setFieldName(config[2]);
            
            // Assert: 验证汇总统计配置
            assertEquals(config[0], component.getApiName());
            assertEquals(config[1], component.getType());
            assertEquals(config[2], component.getFieldName());
        }
    }

    /**
     * 测试内容描述：测试复杂配置场景
     */
    @Test
    @DisplayName("复杂场景 - 复杂配置场景")
    void testComplexConfiguration() {
        // Arrange: 创建复杂配置
        Map<String, Object> complexMap = Maps.newHashMap();
        complexMap.put(ISummaryComponentInfo.API_NAME, "complex_summary");
        complexMap.put(ISummaryComponentInfo.TYPE, "custom_aggregation");
        complexMap.put(ISummaryComponentInfo.FIELD_NAME, "complex_field");
        complexMap.put(IComponentInfo.PAGE_TYPE, "custom_page");
        complexMap.put(IComponentInfo.RENDER_TYPE, "custom_render");
        complexMap.put("custom_field", "custom_value");
        
        // Act: 创建复杂配置的组件
        SummaryComponentInfo complexComponent = new SummaryComponentInfo(complexMap);
        
        // Assert: 验证复杂配置
        assertEquals("complex_summary", complexComponent.getApiName());
        assertEquals("custom_aggregation", complexComponent.getType());
        assertEquals("complex_field", complexComponent.getFieldName());
        assertEquals("custom_page", complexComponent.getPageType());
        assertEquals("custom_render", complexComponent.getRenderType());
    }

    /**
     * 测试内容描述：测试常量定义
     */
    @Test
    @DisplayName("常量验证 - 常量定义验证")
    void testConstants() {
        // Assert: 验证接口常量
        assertEquals("type", ISummaryComponentInfo.TYPE);
        assertEquals("field_name", ISummaryComponentInfo.FIELD_NAME);
        assertEquals("api_name", ISummaryComponentInfo.API_NAME);
        assertEquals("render_type", IComponentInfo.RENDER_TYPE);
        assertEquals("page_type", IComponentInfo.PAGE_TYPE);
    }

    /**
     * 测试内容描述：测试数据一致性
     */
    @Test
    @DisplayName("一致性验证 - 数据一致性验证")
    void testDataConsistency() {
        // Arrange: 设置初始数据
        String originalApiName = "test_api";
        String originalType = "sum";
        String originalFieldName = "test_field";
        
        summaryComponentInfo.setApiName(originalApiName);
        summaryComponentInfo.setType(originalType);
        summaryComponentInfo.setFieldName(originalFieldName);
        
        // Act: 获取数据
        String retrievedApiName = summaryComponentInfo.getApiName();
        String retrievedType = summaryComponentInfo.getType();
        String retrievedFieldName = summaryComponentInfo.getFieldName();
        
        // Assert: 验证数据一致性
        assertEquals(originalApiName, retrievedApiName);
        assertEquals(originalType, retrievedType);
        assertEquals(originalFieldName, retrievedFieldName);
        
        // 验证toMap的一致性
        Map<String, Object> map = summaryComponentInfo.toMap();
        assertEquals(originalApiName, map.get(ISummaryComponentInfo.API_NAME));
        assertEquals(originalType, map.get(ISummaryComponentInfo.TYPE));
        assertEquals(originalFieldName, map.get(ISummaryComponentInfo.FIELD_NAME));
        
        // 验证copy后的数据一致性
        ISummaryComponentInfo copiedComponent = summaryComponentInfo.copy();
        assertEquals(summaryComponentInfo.getApiName(), copiedComponent.getApiName());
        assertEquals(summaryComponentInfo.getType(), copiedComponent.getType());
        assertEquals(summaryComponentInfo.getFieldName(), copiedComponent.getFieldName());
    }

    /**
     * 测试内容描述：测试特殊字符处理
     */
    @Test
    @DisplayName("边界条件 - 特殊字符处理")
    void testSpecialCharacters() {
        // Arrange: 创建包含特殊字符的配置
        String specialApiName = "api@#$%^&*()";
        String specialType = "type@#$%^&*()";
        String specialFieldName = "field@#$%^&*()";
        
        // Act: 设置包含特殊字符的配置
        summaryComponentInfo.setApiName(specialApiName);
        summaryComponentInfo.setType(specialType);
        summaryComponentInfo.setFieldName(specialFieldName);
        
        // Assert: 验证特殊字符处理
        assertEquals(specialApiName, summaryComponentInfo.getApiName());
        assertEquals(specialType, summaryComponentInfo.getType());
        assertEquals(specialFieldName, summaryComponentInfo.getFieldName());
    }

    /**
     * 测试内容描述：测试null安全性
     */
    @Test
    @DisplayName("安全性验证 - null安全性验证")
    void testNullSafety() {
        // Act & Assert: 验证null安全性
        assertDoesNotThrow(() -> {
            summaryComponentInfo.setApiName(null);
            summaryComponentInfo.setType(null);
            summaryComponentInfo.setFieldName(null);
            summaryComponentInfo.getApiName();
            summaryComponentInfo.getType();
            summaryComponentInfo.getFieldName();
            summaryComponentInfo.getRenderType();
            summaryComponentInfo.getPageType();
            summaryComponentInfo.toMap();
            summaryComponentInfo.copy();
        });
    }

    /**
     * 测试内容描述：测试深拷贝验证
     */
    @Test
    @DisplayName("拷贝验证 - 深拷贝验证")
    void testDeepCopy() {
        // Arrange: 创建原始组件
        String originalApiName = "original_api";
        String originalType = "sum";
        String originalFieldName = "original_field";
        
        summaryComponentInfo.setApiName(originalApiName);
        summaryComponentInfo.setType(originalType);
        summaryComponentInfo.setFieldName(originalFieldName);
        
        // Act: 执行拷贝
        ISummaryComponentInfo copiedComponent = summaryComponentInfo.copy();
        
        // Assert: 验证深拷贝
        assertNotSame(summaryComponentInfo, copiedComponent);
        assertEquals(summaryComponentInfo.getApiName(), copiedComponent.getApiName());
        assertEquals(summaryComponentInfo.getType(), copiedComponent.getType());
        assertEquals(summaryComponentInfo.getFieldName(), copiedComponent.getFieldName());
        
        // 验证修改拷贝不影响原始对象（如果是深拷贝的话）
        assertNotNull(copiedComponent);
    }
}
