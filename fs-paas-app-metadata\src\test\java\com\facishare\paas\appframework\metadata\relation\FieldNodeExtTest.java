package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.relation.FieldNode;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FieldNodeExtTest {

    @Mock(lenient = true)
    private FieldNode mockFieldNode;

    @Mock
    private IFieldDescribe mockFieldDescribe;

    @Mock
    private FieldDescribeExt mockFieldDescribeExt;

    private IFieldDescribe fieldDescribe;
    private String objectApiName = "TestObject";
    private String fieldApiName = "testField";

    @BeforeEach
    void setUp() {
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("api_name", fieldApiName);
        fieldMap.put("type", IFieldType.TEXT);
        fieldMap.put("label", "Test Field");
        fieldDescribe = FieldDescribeFactory.newInstance(fieldMap);

        when(mockFieldNode.getObjectApiName()).thenReturn(objectApiName);
        when(mockFieldNode.getFieldApiName()).thenReturn(fieldApiName);
        when(mockFieldNode.getOrder()).thenReturn(1);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用FieldNode和IFieldDescribe创建FieldNodeExt对象的正常场景
     */
    @Test
    @DisplayName("创建对象 - 使用FieldNode和IFieldDescribe")
    void testOf_使用FieldNode和IFieldDescribe() {
        // 执行被测试方法
        FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);

        // 验证结果
        assertNotNull(fieldNodeExt);
        assertEquals(mockFieldNode, fieldNodeExt.getFieldNode());
        assertEquals(mockFieldDescribe, fieldNodeExt.getFieldDescribe());
        assertTrue(fieldNodeExt.isCalculateAllData());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用FieldNode和IFieldDescribe创建FieldNodeExt对象，默认calculateAllData为true
     */
    @Test
    @DisplayName("创建对象 - 默认calculateAllData为true")
    void testOf_默认calculateAllData为true() {
        // 执行被测试方法
        FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe);

        // 验证结果
        assertNotNull(fieldNodeExt);
        assertEquals(mockFieldNode, fieldNodeExt.getFieldNode());
        assertEquals(mockFieldDescribe, fieldNodeExt.getFieldDescribe());
        assertTrue(fieldNodeExt.isCalculateAllData());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用objectApiName和IFieldDescribe创建FieldNodeExt对象的正常场景
     */
    @Test
    @DisplayName("创建对象 - 使用objectApiName和IFieldDescribe")
    void testOf_使用objectApiName和IFieldDescribe() {
        try (MockedStatic<FieldNode> mockedFieldNode = mockStatic(FieldNode.class)) {
            // 配置Mock行为
            mockedFieldNode.when(() -> FieldNode.of(objectApiName, fieldDescribe, 1)).thenReturn(mockFieldNode);

            // 执行被测试方法
            FieldNodeExt fieldNodeExt = FieldNodeExt.of(objectApiName, fieldDescribe, 1);

            // 验证结果
            assertNotNull(fieldNodeExt);
            assertEquals(mockFieldNode, fieldNodeExt.getFieldNode());
            assertEquals(fieldDescribe, fieldNodeExt.getFieldDescribe());
            assertTrue(fieldNodeExt.isCalculateAllData());

            // 验证Mock交互
            mockedFieldNode.verify(() -> FieldNode.of(objectApiName, fieldDescribe, 1));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用objectApiName、IFieldDescribe和calculateAllData创建FieldNodeExt对象
     */
    @Test
    @DisplayName("创建对象 - 指定calculateAllData")
    void testOf_指定calculateAllData() {
        try (MockedStatic<FieldNode> mockedFieldNode = mockStatic(FieldNode.class)) {
            // 配置Mock行为
            mockedFieldNode.when(() -> FieldNode.of(objectApiName, fieldDescribe, 1)).thenReturn(mockFieldNode);

            // 执行被测试方法
            FieldNodeExt fieldNodeExt = FieldNodeExt.of(objectApiName, fieldDescribe, 1, false);

            // 验证结果
            assertNotNull(fieldNodeExt);
            assertEquals(mockFieldNode, fieldNodeExt.getFieldNode());
            assertEquals(fieldDescribe, fieldNodeExt.getFieldDescribe());
            assertFalse(fieldNodeExt.isCalculateAllData());

            // 验证Mock交互
            mockedFieldNode.verify(() -> FieldNode.of(objectApiName, fieldDescribe, 1));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sameGroup方法在相同对象和相同calculateAllData时返回true
     */
    @Test
    @DisplayName("相同组判断 - 相同对象相同calculateAllData")
    void testSameGroup_相同对象相同calculateAllData() {
        // 准备测试数据
        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode2.getObjectApiName()).thenReturn(objectApiName);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, true);

        // 执行被测试方法
        boolean result = fieldNodeExt1.sameGroup(fieldNodeExt2);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sameGroup方法在不同对象时返回false
     */
    @Test
    @DisplayName("相同组判断 - 不同对象")
    void testSameGroup_不同对象() {
        // 准备测试数据
        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode2.getObjectApiName()).thenReturn("DifferentObject");
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, true);

        // 执行被测试方法
        boolean result = fieldNodeExt1.sameGroup(fieldNodeExt2);

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sameGroup方法在相同对象但不同calculateAllData时返回false
     */
    @Test
    @DisplayName("相同组判断 - 相同对象不同calculateAllData")
    void testSameGroup_相同对象不同calculateAllData() {
        // 准备测试数据
        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode2.getObjectApiName()).thenReturn(objectApiName);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, false);

        // 执行被测试方法
        boolean result = fieldNodeExt1.sameGroup(fieldNodeExt2);

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isLookupField方法的正常场景
     */
    @Test
    @DisplayName("查找字段判断 - 正常场景")
    void testIsLookupField_正常场景() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockFieldDescribe)).thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isLookupField()).thenReturn(true);

            FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);

            // 执行被测试方法
            boolean result = fieldNodeExt.isLookupField();

            // 验证结果
            assertTrue(result);

            // 验证Mock交互
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.of(mockFieldDescribe));
            verify(mockFieldDescribeExt).isLookupField();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isLookupField方法返回false的场景
     */
    @Test
    @DisplayName("查找字段判断 - 返回false")
    void testIsLookupField_返回false() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockFieldDescribe)).thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isLookupField()).thenReturn(false);

            FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);

            // 执行被测试方法
            boolean result = fieldNodeExt.isLookupField();

            // 验证结果
            assertFalse(result);

            // 验证Mock交互
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.of(mockFieldDescribe));
            verify(mockFieldDescribeExt).isLookupField();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试compareTo方法在FieldNode比较结果不为0时的场景
     */
    @Test
    @DisplayName("比较 - FieldNode比较结果不为0")
    void testCompareTo_FieldNode比较结果不为0() {
        // 准备测试数据
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode.compareTo(mockFieldNode2)).thenReturn(1);

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, true);

        // 执行被测试方法
        int result = fieldNodeExt1.compareTo(fieldNodeExt2);

        // 验证结果
        assertEquals(1, result);
        verify(mockFieldNode).compareTo(mockFieldNode2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试compareTo方法在FieldNode相等但calculateAllData不同时的场景
     */
    @Test
    @DisplayName("比较 - FieldNode相等calculateAllData不同")
    void testCompareTo_FieldNode相等calculateAllData不同() {
        // 准备测试数据
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode.compareTo(mockFieldNode2)).thenReturn(0);

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, false);

        // 执行被测试方法
        int result = fieldNodeExt1.compareTo(fieldNodeExt2);

        // 验证结果
        assertEquals(-1, result); // calculateAllData为true的排在前面
        verify(mockFieldNode).compareTo(mockFieldNode2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试compareTo方法在所有属性都相等时的场景
     */
    @Test
    @DisplayName("比较 - 所有属性都相等")
    void testCompareTo_所有属性都相等() {
        // 准备测试数据
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode.compareTo(mockFieldNode2)).thenReturn(0);

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, true);

        // 执行被测试方法
        int result = fieldNodeExt1.compareTo(fieldNodeExt2);

        // 验证结果
        assertEquals(0, result);
        verify(mockFieldNode).compareTo(mockFieldNode2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建FieldNodeExt对象时传入null FieldNode抛出异常
     */
    @Test
    @DisplayName("创建对象 - 传入null FieldNode抛出异常")
    void testOfThrowsNullPointerException_传入null_FieldNode() {
        // 执行并验证异常
        assertThrows(NullPointerException.class, () -> {
            FieldNodeExt.of(null, mockFieldDescribe, true);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortAndClassifyNodeExtList方法的正常场景
     */
    @Test
    @DisplayName("排序和分类 - 正常场景")
    void testSortAndClassifyNodeExtList_正常场景() {
        try (MockedStatic<FieldNode> mockedFieldNode = mockStatic(FieldNode.class)) {
            // 准备测试数据
            FieldNode fieldNode1 = mock(FieldNode.class);
            FieldNode fieldNode2 = mock(FieldNode.class);
            FieldNode fieldNode3 = mock(FieldNode.class);

            when(fieldNode1.getObjectApiName()).thenReturn("ObjectA");
            when(fieldNode2.getObjectApiName()).thenReturn("ObjectA");
            when(fieldNode3.getObjectApiName()).thenReturn("ObjectB");

            when(fieldNode1.compareTo(any())).thenReturn(-1);
            when(fieldNode2.compareTo(any())).thenReturn(1);
            when(fieldNode3.compareTo(any())).thenReturn(0);

            FieldNodeExt nodeExt1 = FieldNodeExt.of(fieldNode1, mockFieldDescribe, true);
            FieldNodeExt nodeExt2 = FieldNodeExt.of(fieldNode2, mockFieldDescribe, false);
            FieldNodeExt nodeExt3 = FieldNodeExt.of(fieldNode3, mockFieldDescribe, true);

            List<FieldNodeExt> nodeExtList = Arrays.asList(nodeExt2, nodeExt1, nodeExt3);

            // 执行被测试方法
            List<List<FieldNodeExt>> result = FieldNodeExt.sortAndClassifyNodeExtList(nodeExtList);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortAndClassifyNodeExtList方法传入空列表的场景
     */
    @Test
    @DisplayName("排序和分类 - 空列表")
    void testSortAndClassifyNodeExtList_空列表() {
        // 执行被测试方法
        List<List<FieldNodeExt>> result = FieldNodeExt.sortAndClassifyNodeExtList(new ArrayList<>());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortAndClassifyNodeExtList方法传入null的场景
     */
    @Test
    @DisplayName("排序和分类 - 传入null")
    void testSortAndClassifyNodeExtList_传入null() {
        // 执行被测试方法
        List<List<FieldNodeExt>> result = FieldNodeExt.sortAndClassifyNodeExtList(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sameGroup方法的正常场景
     */
    @Test
    @DisplayName("同组判断 - 正常场景")
    void testSameGroup_正常场景() {
        // 准备测试数据
        when(mockFieldNode.getObjectApiName()).thenReturn("TestObject");

        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode2.getObjectApiName()).thenReturn("TestObject");

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, true);

        // 执行被测试方法
        boolean result = fieldNodeExt1.sameGroup(fieldNodeExt2);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sameGroup方法在对象API名称不同时的场景
     */
    @Test
    @DisplayName("同组判断 - 对象API名称不同")
    void testSameGroup_对象API名称不同() {
        // 准备测试数据
        when(mockFieldNode.getObjectApiName()).thenReturn("TestObject1");

        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode2.getObjectApiName()).thenReturn("TestObject2");

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, true);

        // 执行被测试方法
        boolean result = fieldNodeExt1.sameGroup(fieldNodeExt2);

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isLookupField方法的扩展场景
     */
    @Test
    @DisplayName("查找字段判断 - 扩展场景")
    void testIsLookupField_扩展场景() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 配置Mock行为
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.isLookupField()).thenReturn(false);

            FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);

            // 执行被测试方法
            boolean result = fieldNodeExt.isLookupField();

            // 验证结果
            assertFalse(result);
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.of(mockFieldDescribe));
            verify(mockFieldDescribeExt).isLookupField();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试compareTo方法在calculateAllData不同时的场景
     */
    @Test
    @DisplayName("比较 - calculateAllData不同")
    void testCompareTo_calculateAllData不同() {
        // 准备测试数据
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode.compareTo(mockFieldNode2)).thenReturn(0);

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, false);

        // 执行被测试方法
        int result = fieldNodeExt1.compareTo(fieldNodeExt2);

        // 验证结果
        assertEquals(-1, result); // calculateAllData为true的排在前面
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试compareTo方法在FieldNode比较结果为负数时的场景
     */
    @Test
    @DisplayName("比较 - FieldNode比较结果为负数")
    void testCompareTo_FieldNode比较结果为负数() {
        // 准备测试数据
        FieldNode mockFieldNode2 = mock(FieldNode.class);
        when(mockFieldNode.compareTo(mockFieldNode2)).thenReturn(-1);

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode2, mockFieldDescribe, false);

        // 执行被测试方法
        int result = fieldNodeExt1.compareTo(fieldNodeExt2);

        // 验证结果
        assertEquals(-1, result); // 直接返回FieldNode的比较结果
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EqualsAndHashCode注解的排除字段功能
     */
    @Test
    @DisplayName("对象比较 - equals和hashCode排除fieldDescribe")
    void testEqualsAndHashCode_排除fieldDescribe() {
        // 创建两个具有相同FieldNode和calculateAllData但不同fieldDescribe的对象
        IFieldDescribe differentFieldDescribe = mock(IFieldDescribe.class);

        FieldNodeExt fieldNodeExt1 = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);
        FieldNodeExt fieldNodeExt2 = FieldNodeExt.of(mockFieldNode, differentFieldDescribe, true);

        // 验证equals方法（应该相等，因为fieldDescribe被排除）
        assertEquals(fieldNodeExt1, fieldNodeExt2);
        assertEquals(fieldNodeExt1.hashCode(), fieldNodeExt2.hashCode());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldNodeExt的toString方法
     */
    @Test
    @DisplayName("字符串表示 - toString方法")
    void testToString() {
        // 创建测试对象
        FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);

        // 执行被测试方法
        String result = fieldNodeExt.toString();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("FieldNodeExt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldNodeExt的getter方法
     */
    @Test
    @DisplayName("属性访问 - 所有getter方法")
    void testGetters_所有属性() {
        // 创建测试对象
        FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, false);

        // 验证所有getter方法
        assertEquals(mockFieldNode, fieldNodeExt.getFieldNode());
        assertEquals(mockFieldDescribe, fieldNodeExt.getFieldDescribe());
        assertFalse(fieldNodeExt.isCalculateAllData());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortAndClassifyNodeExtList方法的复杂分组场景
     */
    @Test
    @DisplayName("排序和分类 - 复杂分组场景")
    void testSortAndClassifyNodeExtList_复杂分组场景() {
        // 准备测试数据 - 多个对象，多个calculateAllData值
        FieldNode fieldNode1 = mock(FieldNode.class);
        FieldNode fieldNode2 = mock(FieldNode.class);
        FieldNode fieldNode3 = mock(FieldNode.class);
        FieldNode fieldNode4 = mock(FieldNode.class);

        when(fieldNode1.getObjectApiName()).thenReturn("ObjectA");
        when(fieldNode2.getObjectApiName()).thenReturn("ObjectA");
        when(fieldNode3.getObjectApiName()).thenReturn("ObjectB");
        when(fieldNode4.getObjectApiName()).thenReturn("ObjectB");

        // 设置比较行为
        when(fieldNode1.compareTo(any())).thenAnswer(invocation -> {
            FieldNode other = invocation.getArgument(0);
            return fieldNode1.equals(other) ? 0 : -1;
        });
        when(fieldNode2.compareTo(any())).thenAnswer(invocation -> {
            FieldNode other = invocation.getArgument(0);
            return fieldNode2.equals(other) ? 0 : 1;
        });
        when(fieldNode3.compareTo(any())).thenAnswer(invocation -> {
            FieldNode other = invocation.getArgument(0);
            return fieldNode3.equals(other) ? 0 : -1;
        });
        when(fieldNode4.compareTo(any())).thenAnswer(invocation -> {
            FieldNode other = invocation.getArgument(0);
            return fieldNode4.equals(other) ? 0 : 1;
        });

        FieldNodeExt nodeExt1 = FieldNodeExt.of(fieldNode1, mockFieldDescribe, true);
        FieldNodeExt nodeExt2 = FieldNodeExt.of(fieldNode2, mockFieldDescribe, false);
        FieldNodeExt nodeExt3 = FieldNodeExt.of(fieldNode3, mockFieldDescribe, true);
        FieldNodeExt nodeExt4 = FieldNodeExt.of(fieldNode4, mockFieldDescribe, false);

        List<FieldNodeExt> nodeExtList = Arrays.asList(nodeExt4, nodeExt2, nodeExt3, nodeExt1);

        // 执行被测试方法
        List<List<FieldNodeExt>> result = FieldNodeExt.sortAndClassifyNodeExtList(nodeExtList);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 验证分组逻辑 - 相同对象和calculateAllData的应该在同一组
        for (List<FieldNodeExt> group : result) {
            if (group.size() > 1) {
                FieldNodeExt first = group.get(0);
                for (int i = 1; i < group.size(); i++) {
                    assertTrue(first.sameGroup(group.get(i)));
                }
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FieldNodeExt的委托方法
     */
    @Test
    @DisplayName("委托方法 - FieldNode方法委托")
    void testFieldNodeDelegation() {
        // 配置Mock行为
        when(mockFieldNode.getObjectApiName()).thenReturn("DelegateObject");
        when(mockFieldNode.getFieldApiName()).thenReturn("delegateField");
        when(mockFieldNode.getOrder()).thenReturn(5);

        // 创建测试对象
        FieldNodeExt fieldNodeExt = FieldNodeExt.of(mockFieldNode, mockFieldDescribe, true);

        // 验证委托方法
        assertEquals("DelegateObject", fieldNodeExt.getObjectApiName());
        assertEquals("delegateField", fieldNodeExt.getFieldApiName());
        assertEquals(5, fieldNodeExt.getOrder());

        // 验证Mock交互
        verify(mockFieldNode, atLeastOnce()).getObjectApiName();
        verify(mockFieldNode, atLeastOnce()).getFieldApiName();
        verify(mockFieldNode, atLeastOnce()).getOrder();
    }
}
