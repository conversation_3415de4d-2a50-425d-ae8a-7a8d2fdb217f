package com.facishare.paas.appframework.metadata.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for SearchTemplateUtil
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SearchTemplateUtil 单元测试")
class SearchTemplateUtilJUnit5Test {

    // ==================== 常量测试 ====================

    @Test
    @DisplayName("验证需要添加默认搜索模板的API名称常量")
    void testNeedAddDefaultSearchTemplateApiNames() {
        // Assert
        assertNotNull(SearchTemplateUtil.NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES);
        assertFalse(SearchTemplateUtil.NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES.isEmpty());
        assertTrue(SearchTemplateUtil.NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES.contains(Utils.CASES_API_NAME));
        
        // 验证集合是不可修改的
        assertThrows(UnsupportedOperationException.class, () -> {
            SearchTemplateUtil.NEED_ADD_DEFAULT_SEARCH_TEMPLATE_API_NAMES.add("test");
        });
    }

    // ==================== getFilterByPara 方法测试 ====================

    @Test
    @DisplayName("根据参数获取过滤器 - 正常情况")
    void testGetFilterByPara_Success() {
        // Arrange
        Operator operator = Operator.EQ;
        String fieldName = "status";
        List<String> values = Lists.newArrayList("active", "pending");
        boolean objectReference = false;

        // Act
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // Assert
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals("AND", result.getConnector());
        assertEquals(fieldName, result.getFieldName());
        assertEquals(values, result.getFieldValues());
        // 注意：IFilter接口可能没有isObjectReference方法，需要检查实际接口
    }

    @Test
    @DisplayName("根据参数获取过滤器 - 对象引用为true")
    void testGetFilterByPara_ObjectReferenceTrue() {
        // Arrange
        Operator operator = Operator.IN;
        String fieldName = "assignedTo";
        List<String> values = Lists.newArrayList("user1", "user2");
        boolean objectReference = true;

        // Act
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // Assert
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals("AND", result.getConnector());
        assertEquals(fieldName, result.getFieldName());
        assertEquals(values, result.getFieldValues());
    }

    @Test
    @DisplayName("根据参数获取过滤器 - 空值列表")
    void testGetFilterByPara_EmptyValueList() {
        // Arrange
        Operator operator = Operator.NEQ;
        String fieldName = "category";
        List<String> values = Lists.newArrayList();
        boolean objectReference = false;

        // Act
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // Assert
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals("AND", result.getConnector());
        assertEquals(fieldName, result.getFieldName());
        assertEquals(values, result.getFieldValues());
        assertTrue(result.getFieldValues().isEmpty());
    }

    @Test
    @DisplayName("根据参数获取过滤器 - null值列表")
    void testGetFilterByPara_NullValueList() {
        // Arrange
        Operator operator = Operator.LIKE;
        String fieldName = "description";
        List<String> values = null;
        boolean objectReference = false;

        // Act
        IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);

        // Assert
        assertNotNull(result);
        assertEquals(operator, result.getOperator());
        assertEquals("AND", result.getConnector());
        assertEquals(fieldName, result.getFieldName());
        assertNull(result.getFieldValues());
    }

    @Test
    @DisplayName("根据参数获取过滤器 - 不同操作符")
    void testGetFilterByPara_DifferentOperators() {
        // Arrange & Act & Assert
        String fieldName = "testField";
        List<String> values = Lists.newArrayList("value1");
        boolean objectReference = false;

        // 测试不同的操作符
        Operator[] operators = {
            Operator.EQ, Operator.NEQ, Operator.LIKE,
            Operator.IN, Operator.GT, Operator.LT,
            Operator.GTE, Operator.LTE
        };

        for (Operator operator : operators) {
            IFilter result = SearchTemplateUtil.getFilterByPara(operator, fieldName, values, objectReference);
            assertNotNull(result, "Filter should not be null for operator: " + operator);
            assertEquals(operator, result.getOperator(), "Operator should match for: " + operator);
            assertEquals(fieldName, result.getFieldName());
            assertEquals(values, result.getFieldValues());
        }
    }

    // ==================== getSearchTemplate 方法测试（简化版本） ====================

    @Test
    @DisplayName("获取搜索模板 - 简化版本")
    void testGetSearchTemplate_SimpleVersion() {
        // Arrange
        String tenantId = "tenant123";
        String apiName = "TestObject";
        String label = "Test Search Template";
        boolean isDefault = true;
        List<IFilter> filters = Lists.newArrayList();
        
        IFilter filter = SearchTemplateUtil.getFilterByPara(Operator.EQ, "status", Lists.newArrayList("active"), false);
        filters.add(filter);

        // Act
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters);

        // Assert
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(apiName, result.getObjectDescribeApiName());
        assertEquals(label, result.getLabel());
        assertEquals(isDefault, result.getIsDefault());
        assertEquals(filters, result.getFilters());
        assertFalse(result.getIsHidden());
        assertEquals("CRM", result.getPackage());
        assertEquals("default", result.getType());
        assertEquals("system", result.getCreatedBy());
    }

    @Test
    @DisplayName("获取搜索模板 - 简化版本，非默认模板")
    void testGetSearchTemplate_SimpleVersion_NotDefault() {
        // Arrange
        String tenantId = "tenant456";
        String apiName = "CustomObject";
        String label = "Custom Search Template";
        boolean isDefault = false;
        List<IFilter> filters = Lists.newArrayList();

        // Act
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters);

        // Assert
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(apiName, result.getObjectDescribeApiName());
        assertEquals(label, result.getLabel());
        assertFalse(result.getIsDefault());
        assertEquals(filters, result.getFilters());
        assertFalse(result.getIsHidden());
        assertEquals("CRM", result.getPackage());
        assertEquals("default", result.getType());
        assertEquals("system", result.getCreatedBy());
    }

    // ==================== getSearchTemplate 方法测试（完整版本） ====================

    @Test
    @DisplayName("获取搜索模板 - 完整版本")
    void testGetSearchTemplate_FullVersion() {
        // Arrange
        String tenantId = "tenant789";
        String apiName = "FullObject";
        String label = "Full Search Template";
        boolean isDefault = true;
        List<IFilter> filters = Lists.newArrayList();
        List<String> fieldList = Lists.newArrayList("field1", "field2", "field3");
        Integer fieldListType = 1;

        IFilter filter = SearchTemplateUtil.getFilterByPara(Operator.IN, "category", Lists.newArrayList("cat1", "cat2"), false);
        filters.add(filter);

        // Act
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters, fieldList, fieldListType);

        // Assert
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(apiName, result.getObjectDescribeApiName());
        assertEquals(label, result.getLabel());
        assertEquals(isDefault, result.getIsDefault());
        assertEquals(filters, result.getFilters());
        assertEquals(fieldListType, result.getFieldListType());
        assertNotNull(result.getFieldList());
        assertFalse(result.getIsHidden());
        assertEquals("CRM", result.getPackage());
        assertEquals("default", result.getType());
        assertEquals("system", result.getCreatedBy());
    }

    @Test
    @DisplayName("获取搜索模板 - 完整版本，null字段列表")
    void testGetSearchTemplate_FullVersion_NullFieldList() {
        // Arrange
        String tenantId = "tenant999";
        String apiName = "NullFieldObject";
        String label = "Null Field Search Template";
        boolean isDefault = false;
        List<IFilter> filters = Lists.newArrayList();
        List<String> fieldList = null;
        Integer fieldListType = 2;

        // Act
        ISearchTemplate result = SearchTemplateUtil.getSearchTemplate(tenantId, apiName, label, isDefault, filters, fieldList, fieldListType);

        // Assert
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(apiName, result.getObjectDescribeApiName());
        assertEquals(label, result.getLabel());
        assertEquals(isDefault, result.getIsDefault());
        assertEquals(filters, result.getFilters());
        assertEquals(fieldListType, result.getFieldListType());
        assertNull(result.getFieldList()); // 应该为null，因为fieldList为null
    }

}
