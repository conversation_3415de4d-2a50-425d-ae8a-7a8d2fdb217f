package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.ref.RefMessage;
import com.facishare.paas.appframework.core.model.ref.RefMessage.ActionType;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Ref;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Refs;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.appframework.metadata.reference.OptionDependenceRef;
import com.facishare.paas.appframework.metadata.reference.RefFieldService;
import com.facishare.paas.appframework.metadata.repository.model.MtFieldDependence;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.service.IDistributedLockService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.MetadataTransactionService;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/7/29
 */
@Slf4j
@Service
public class SelectFieldDependenceLogicServiceImpl implements SelectFieldDependenceLogicService {
    @Autowired
    private FieldDependenceLogicService fieldDependenceLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private RefFieldService refFieldService;
    @Autowired
    private IDistributedLockService distributedLockService;
    @Autowired
    private MetadataTransactionService metadataTransactionService;

    @Override
    public List<SelectFieldDependence> findFieldDependenceWitchObjectApiName(User user, String objectDescribeApiName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectDescribeApiName);

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<SelectOne> childField = describeExt.getSelectOneFields();

        List<SelectFieldDependence> result = Lists.newArrayList();
        for (SelectOne selectOne : childField) {
            String parentApiName = selectOne.getCascadeParentApiName();
            if (Strings.isNullOrEmpty(parentApiName)) {
                continue;
            }
            describeExt.getFieldDescribeSilently(parentApiName)
                    .map(FieldDescribeExt::of)
                    .filter(FieldDescribeExt::isCascadeParentField)
                    .ifPresent(parentField -> {
                        SelectFieldDependence fieldDependence = SelectFieldDependence.createWithOutDependence(parentField.getFieldDescribe(), selectOne);
                        result.add(fieldDependence);
                    });
        }
        return result;
    }

    @Override
    public Optional<SelectFieldDependence> find(User user, String objectDescribeApiName, String fieldApiName, String childFieldName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectDescribeApiName);

        SelectFieldDependence result = SelectFieldDependence.verifyAndFindOne(describe, fieldApiName, childFieldName, () -> {
            Optional<MtFieldDependence> fieldDependence = findFieldDependence(user, describe,
                    upstreamUser -> fieldDependenceLogicService.find(upstreamUser, describe.getApiName(), fieldApiName, childFieldName));
            return fieldDependence.map(SelectFieldDependence::from).orElse(null);
        });
        return Optional.ofNullable(result);
    }

    private <T> T findFieldDependence(User user, IObjectDescribe describe, Function<User, T> function) {
        if (ObjectDescribeExt.of(describe).isDownstreamTenantWithPublicObject()) {
            User upstreamUser = User.systemUser(describe.getUpstreamTenantId());
            try {
                return metadataTransactionService.executeWithOutMetadataTransaction(() -> function.apply(upstreamUser));
            } catch (MetadataServiceException e) {
                log.warn("findFieldDependence fail! tenantId:{}, upstreamTenantId:{}, objectApiName:{}", user.getTenantId(), upstreamUser.getTenantId(), describe.getApiName());
                throw new MetaDataBusinessException(e);
            }
        }
        return function.apply(user);
    }

    @Override
    @Transactional
    public void create(User user, SelectFieldDependence selectFieldDependence) {
        String describeApiName = selectFieldDependence.getDescribeApiName();
        String childFieldName = selectFieldDependence.getChildFieldName();
        tryLock(user, describeApiName, childFieldName);
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        validateByPublicObject(describe);

        SelectFieldDependence.ConvertResultInfo convertInfo = selectFieldDependence.convert2Create(describe);
        create(user, describe, convertInfo);
        executeReference(ActionType.CREATE, describe, Tuple.of(selectFieldDependence.getFieldApiName(), childFieldName));
    }

    private void validateByPublicObject(IObjectDescribe describe) {
        if (ObjectDescribeExt.of(describe).isDownstreamTenantWithPublicObject()) {
            throw new ValidateException(I18nMessage.of(I18NKey.NOT_SUPPORT_MODIFY_FIELD_DEPENDENCIES, I18N.text(I18NKey.NOT_SUPPORT_MODIFY_FIELD_DEPENDENCIES)));
        }
    }

    private void create(User user, IObjectDescribe describe, SelectFieldDependence.ConvertResultInfo convertInfo) {
        describeLogicService.updateFieldDescribe(user, describe, convertInfo.getFieldDescribes());
        convertInfo.getFieldDependence().ifPresent(fieldDependence -> fieldDependenceLogicService.create(user, fieldDependence));
    }

    @Override
    public void copy(User user, IObjectDescribe objectDescribe, SelectFieldDependence selectFieldDependence) {
        SelectFieldDependence.ConvertResultInfo convertInfo = selectFieldDependence.convert2Copy(objectDescribe);
        create(user, objectDescribe, convertInfo);
    }

    @Override
    @Transactional
    public void update(User user, SelectFieldDependence selectFieldDependence) {
        String describeApiName = selectFieldDependence.getDescribeApiName();
        String childFieldName = selectFieldDependence.getChildFieldName();
        tryLock(user, describeApiName, childFieldName);
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        validateByPublicObject(describe);
        SelectFieldDependence.ConvertResultInfo convertInfo = selectFieldDependence.convert2Update(describe);

        describeLogicService.updateFieldDescribe(user, describe, convertInfo.getFieldDescribes());
        convertInfo.getFieldDependence().ifPresent(fieldDependence -> fieldDependenceLogicService.update(user, fieldDependence));

        executeReference(ActionType.DELETE_AND_CREATE, describe, Tuple.of(selectFieldDependence.getFieldApiName(), selectFieldDependence.getChildFieldName()));
    }

    @Override
    @Transactional
    public void deleted(User user, String describeApiName, String fieldApiName, String childFieldName) {
        tryLock(user, describeApiName, childFieldName);
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        validateByPublicObject(describe);
        SelectFieldDependence.ConvertResultInfo convertInfo = SelectFieldDependence.convert2Deleted(describe, fieldApiName, childFieldName);

        describeLogicService.updateFieldDescribe(user, describe, convertInfo.getFieldDescribes());
        convertInfo.getFieldDependence().ifPresent(fieldDependence -> fieldDependenceLogicService.deleted(user, describeApiName, fieldApiName, childFieldName));
        executeReference(ActionType.DELETE, describe, Tuple.of(fieldApiName, childFieldName));
    }

    @Override
    public List<SelectOne> findChildFields(User user, String describeApiName, String fieldApiName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        IFieldDescribe fieldDescribe = describeExt
                .getFieldDescribeSilently(fieldApiName)
                .filter(it -> FieldDescribeExt.of(it).isCascadeParentField())
                .orElseThrow(() -> new ValidateException(I18nMessage.of(I18NKey.PARAM_ERROR, I18N.text(I18NKey.PARAM_ERROR))));

        return describeExt.stream()
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isCascadeChildField)
                .map(FieldDescribeExt::<SelectOne>getFieldDescribe)
                .filter(it -> Objects.equals(fieldDescribe.getApiName(), it.getCascadeParentApiName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SelectFieldDependence> findAll(User user, String objectDescribeApiName) {
        return findByCache(user, objectDescribeApiName, () -> findAllInDB(user, objectDescribeApiName));
    }

    public List<SelectFieldDependence> findAllInDB(User user, String objectDescribeApiName) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectDescribeApiName);

        List<MtFieldDependence> dependences = findFieldDependence(user, describe,
                upstreamUser -> fieldDependenceLogicService.findAll(upstreamUser, objectDescribeApiName));
        if (CollectionUtils.empty(dependences)) {
            return Collections.emptyList();
        }
        Map<String, MtFieldDependence> dependenceMap = dependences.stream()
                .collect(Collectors.toMap(it -> getKey(it.getFieldApiName(), it.getChildFieldName()), Function.identity(), (x, y) -> x));
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<SelectOne> childField = describeExt.getSelectOneFields();

        List<SelectFieldDependence> result = Lists.newArrayList();
        for (SelectOne selectOne : childField) {
            String parentApiName = selectOne.getCascadeParentApiName();
            if (Strings.isNullOrEmpty(parentApiName)) {
                continue;
            }
            describeExt.getFieldDescribeSilently(parentApiName)
                    .map(FieldDescribeExt::of)
                    .filter(FieldDescribeExt::isCascadeParentField)
                    .filter(it -> it.isGeneralOptions() || FieldDescribeExt.of(selectOne).isGeneralOptions())
                    .map(it -> getKey(it.getApiName(), selectOne.getApiName()))
                    .map(dependenceMap::get)
                    .map(SelectFieldDependence::from)
                    .ifPresent(result::add);
        }
        return ImmutableList.copyOf(result);
    }

    private <T> T findByCache(User user, String objectDescribeApiName, Supplier<T> supplier) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SELECT_FIELD_DEPENDENCE_LOCAL_CACHE_GRAY, user.getTenantId())) {
            return supplier.get();
        }
        String cacheKey = getCacheKey(user, objectDescribeApiName);
        CacheContext cacheContext = CacheContext.getContext();
        Object cache = cacheContext.getCache(cacheKey);
        if (Objects.nonNull(cache)) {
            return (T) cache;
        }
        T result = supplier.get();
        cacheContext.setCache(cacheKey, result);
        return result;
    }

    private String getCacheKey(User user, String objectDescribeApiName) {
        return String.format("SELECT_FIELD_DEPENDENCE_%s_%s", user.getTenantId(), objectDescribeApiName);
    }

    private void executeReference(RefMessage.ActionType actionType, IObjectDescribe objectDescribe, Tuple<String, String> parentChild) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIND_WHERE_A_FIELD_IS_USED_GRAY_KEY, objectDescribe.getTenantId())) {
            return;
        }
        IFieldDescribe parentField = objectDescribe.getFieldDescribe(parentChild.getKey());
        IFieldDescribe childField = objectDescribe.getFieldDescribe(parentChild.getValue());
        Ref ref = OptionDependenceRef.buildRefByOptionDependence(actionType, Tuple.of(parentField, childField), objectDescribe, childField);
        Refs refs = Refs.builder().refs(Lists.newArrayList(ref)).build();
        refFieldService.sendRefs(refs);
    }

    private String getKey(String parentApiName, String childApiName) {
        return String.format("%s_%s", parentApiName, childApiName);
    }

    private void tryLock(User user, String describeApiName, String childFieldName) {
        distributedLockService.advisoryTransactionalLock(user.getTenantId(), getKey(describeApiName, childFieldName));
    }
}
