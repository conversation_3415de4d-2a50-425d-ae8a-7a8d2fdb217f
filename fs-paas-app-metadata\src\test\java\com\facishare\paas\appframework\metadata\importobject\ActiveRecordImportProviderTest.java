package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.metadata.util.ActiveRecordConfig;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static com.facishare.crm.openapi.Utils.ACTIVE_RECORD_API_NAME;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ActiveRecordImportProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    @Mock
    private IUniqueRule uniqueRule;

    @Spy
    private ActiveRecordImportProvider activeRecordImportProvider;

    private String testTenantId;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        when(objectDescribe.getTenantId()).thenReturn(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的销售记录API名称")
    void testGetObjectCode_返回正确的销售记录API名称() {
        // 执行被测试方法
        String result = activeRecordImportProvider.getObjectCode();

        // 验证结果
        assertEquals(ACTIVE_RECORD_API_NAME, result);
        assertEquals("ActiveRecordObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当灰度配置开启时，getImportObject方法调用父类方法
     */
    @Test
    @DisplayName("getImportObject - 灰度开启时调用父类方法")
    void testGetImportObject_灰度开启时调用父类方法() {
        try (MockedStatic<ActiveRecordConfig> mockedActiveRecordConfig = mockStatic(ActiveRecordConfig.class)) {
            // 准备测试数据
            ImportObject expectedImportObject = ImportObject.builder()
                    .objectCode(ACTIVE_RECORD_API_NAME)
                    .objectName("销售记录")
                    .build();

            // 配置Mock行为
            mockedActiveRecordConfig.when(() -> ActiveRecordConfig.isInActiveRecordImportGray(testTenantId))
                    .thenReturn(true);
            doReturn(Optional.of(expectedImportObject))
                    .when(activeRecordImportProvider, "getImportObject", objectDescribe, uniqueRule);

            // 执行被测试方法
            Optional<ImportObject> result = activeRecordImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertTrue(result.isPresent());
            assertEquals(expectedImportObject, result.get());

            // 验证Mock交互
            mockedActiveRecordConfig.verify(() -> ActiveRecordConfig.isInActiveRecordImportGray(testTenantId));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当灰度配置关闭时，getImportObject方法返回空Optional
     */
    @Test
    @DisplayName("getImportObject - 灰度关闭时返回空Optional")
    void testGetImportObject_灰度关闭时返回空Optional() {
        try (MockedStatic<ActiveRecordConfig> mockedActiveRecordConfig = mockStatic(ActiveRecordConfig.class)) {
            // 配置Mock行为
            mockedActiveRecordConfig.when(() -> ActiveRecordConfig.isInActiveRecordImportGray(testTenantId))
                    .thenReturn(false);

            // 执行被测试方法
            Optional<ImportObject> result = activeRecordImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertFalse(result.isPresent());
            assertTrue(result.isEmpty());

            // 验证Mock交互
            mockedActiveRecordConfig.verify(() -> ActiveRecordConfig.isInActiveRecordImportGray(testTenantId));
            // 验证没有调用父类方法
            verify(activeRecordImportProvider, never()).getImportObject(any(IObjectDescribe.class), any(IUniqueRule.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getImportType方法返回不支持更新导入类型
     */
    @Test
    @DisplayName("getImportType - 返回不支持更新导入类型")
    void testGetImportType_返回不支持更新导入类型() {
        // 执行被测试方法
        ImportType result = activeRecordImportProvider.getImportType(objectDescribe, uniqueRule);

        // 验证结果
        assertEquals(ImportType.UNSUPPORT_UPDATE_IMPORT, result);
        assertEquals(0, result.getType());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，getImportObject方法的异常处理
     */
    @Test
    @DisplayName("getImportObject - objectDescribe为null时的异常处理")
    void testGetImportObjectThrowsException_objectDescribe为null时的异常处理() {
        try (MockedStatic<ActiveRecordConfig> mockedActiveRecordConfig = mockStatic(ActiveRecordConfig.class)) {
            // 执行并验证异常
            Exception exception = assertThrows(NullPointerException.class, () -> {
                activeRecordImportProvider.getImportObject(null, uniqueRule);
            });

            // 验证异常信息
            assertNotNull(exception);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当tenantId为null时，灰度配置的处理
     */
    @Test
    @DisplayName("getImportObject - tenantId为null时的处理")
    void testGetImportObject_tenantId为null时的处理() {
        try (MockedStatic<ActiveRecordConfig> mockedActiveRecordConfig = mockStatic(ActiveRecordConfig.class)) {
            // 准备测试数据
            when(objectDescribe.getTenantId()).thenReturn(null);

            // 配置Mock行为
            mockedActiveRecordConfig.when(() -> ActiveRecordConfig.isInActiveRecordImportGray(null))
                    .thenReturn(false);

            // 执行被测试方法
            Optional<ImportObject> result = activeRecordImportProvider.getImportObject(objectDescribe, uniqueRule);

            // 验证结果
            assertFalse(result.isPresent());

            // 验证Mock交互
            mockedActiveRecordConfig.verify(() -> ActiveRecordConfig.isInActiveRecordImportGray(null));
        }
    }
}
