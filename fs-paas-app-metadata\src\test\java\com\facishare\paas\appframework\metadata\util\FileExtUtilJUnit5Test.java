package com.facishare.paas.appframework.metadata.util;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JUnit5测试类 for FileExtUtil (兼容性类)
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("FileExtUtil 单元测试 (兼容性类)")
class FileExtUtilJUnit5Test {

    @Mock
    private IFieldDescribe mockFieldDescribe;

    // ==================== 常量测试 ====================

    @Test
    @DisplayName("验证IMAGE_FLAG常量")
    void testImageFlagConstant() {
        // Assert
        assertNotNull(FileExtUtil.IMAGE_FLAG);
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.IMAGE_FLAG, FileExtUtil.IMAGE_FLAG);
    }

    // ==================== parseFileExt 方法测试 ====================

    @Test
    @DisplayName("解析文件扩展名 - 正常情况")
    void testParseFileExt_Success() {
        // Arrange
        String fileName = "document.pdf";
        String expectedExt = "pdf";

        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedExt, result);
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    @Test
    @DisplayName("解析文件扩展名 - 多个点的文件名")
    void testParseFileExt_MultipleDotsFileName() {
        // Arrange
        String fileName = "my.document.backup.txt";
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        assertNotNull(result);
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    @Test
    @DisplayName("解析文件扩展名 - 无扩展名文件")
    void testParseFileExt_NoExtension() {
        // Arrange
        String fileName = "document";
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    @Test
    @DisplayName("解析文件扩展名 - 空字符串")
    void testParseFileExt_EmptyString() {
        // Arrange
        String fileName = "";
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    @Test
    @DisplayName("解析文件扩展名 - null文件名")
    void testParseFileExt_NullFileName() {
        // Arrange
        String fileName = null;
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    @Test
    @DisplayName("解析文件扩展名 - 隐藏文件")
    void testParseFileExt_HiddenFile() {
        // Arrange
        String fileName = ".gitignore";
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    @Test
    @DisplayName("解析文件扩展名 - 大写扩展名")
    void testParseFileExt_UpperCaseExtension() {
        // Arrange
        String fileName = "document.PDF";
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        assertNotNull(result);
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    // ==================== isEmbeddedImageField 方法测试 ====================

    @Test
    @DisplayName("判断是否为嵌入式图片字段 - 委托调用验证")
    void testIsEmbeddedImageField_DelegateCall() {
        // Act
        boolean result = FileExtUtil.isEmbeddedImageField(mockFieldDescribe);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.isEmbeddedImageField(mockFieldDescribe), result);
    }

    @Test
    @DisplayName("判断是否为嵌入式图片字段 - null字段描述")
    void testIsEmbeddedImageField_NullFieldDescribe() {
        // Act & Assert - 应该抛出相同的异常
        assertThrows(NullPointerException.class, () -> {
            FileExtUtil.isEmbeddedImageField(null);
        });

        assertThrows(NullPointerException.class, () -> {
            com.facishare.paas.appframework.common.util.FileExtUtil.isEmbeddedImageField(null);
        });
    }

    // ==================== extractImageOriginalFieldName 方法测试 ====================

    @Test
    @DisplayName("提取图片原始字段名 - 正常情况")
    void testExtractImageOriginalFieldName_Success() {
        // Arrange
        String apiName = "image_field_name";
        
        // Act
        String result = FileExtUtil.extractImageOriginalFieldName(apiName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.extractImageOriginalFieldName(apiName), result);
    }

    @Test
    @DisplayName("提取图片原始字段名 - 空字符串")
    void testExtractImageOriginalFieldName_EmptyString() {
        // Arrange
        String apiName = "";
        
        // Act
        String result = FileExtUtil.extractImageOriginalFieldName(apiName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.extractImageOriginalFieldName(apiName), result);
    }

    @Test
    @DisplayName("提取图片原始字段名 - null API名称")
    void testExtractImageOriginalFieldName_NullApiName() {
        // Arrange
        String apiName = null;

        // Act & Assert - 应该抛出相同的异常
        assertThrows(NullPointerException.class, () -> {
            FileExtUtil.extractImageOriginalFieldName(apiName);
        });

        assertThrows(NullPointerException.class, () -> {
            com.facishare.paas.appframework.common.util.FileExtUtil.extractImageOriginalFieldName(apiName);
        });
    }

    @Test
    @DisplayName("提取图片原始字段名 - 复杂API名称")
    void testExtractImageOriginalFieldName_ComplexApiName() {
        // Arrange
        String apiName = "complex_image_field_with_underscores";
        
        // Act
        String result = FileExtUtil.extractImageOriginalFieldName(apiName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.extractImageOriginalFieldName(apiName), result);
    }

    // ==================== 边界和异常测试 ====================

    @Test
    @DisplayName("边界测试 - 极长文件名")
    void testEdgeCase_VeryLongFileName() {
        // Arrange
        StringBuilder longFileName = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longFileName.append("very_long_file_name_segment_").append(i).append("_");
        }
        longFileName.append(".txt");
        String fileName = longFileName.toString();

        // Act & Assert - 不应该抛出异常
        assertDoesNotThrow(() -> {
            String result = FileExtUtil.parseFileExt(fileName);
            // 验证委托调用结果一致
            assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
        });
    }

    @Test
    @DisplayName("边界测试 - 特殊字符文件名")
    void testEdgeCase_SpecialCharacterFileName() {
        // Arrange
        String fileName = "file@#$%^&*()_+{}|:<>?[]\\.txt";
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    @Test
    @DisplayName("边界测试 - 国际化字符文件名")
    void testEdgeCase_InternationalCharacterFileName() {
        // Arrange
        String fileName = "文档_ドキュメント_документ.pdf";
        
        // Act
        String result = FileExtUtil.parseFileExt(fileName);

        // Assert
        // 验证委托调用结果一致
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName), result);
    }

    // ==================== 集成测试 ====================

    @Test
    @DisplayName("集成测试 - 完整的文件扩展工具流程")
    void testIntegration_CompleteFileExtUtilFlow() {
        // Arrange - 模拟真实业务场景
        String[] testFileNames = {
            "document.pdf",
            "image.jpg",
            "spreadsheet.xlsx",
            "presentation.pptx",
            "archive.zip",
            "video.mp4",
            "audio.mp3",
            "text.txt"
        };

        // Act & Assert - 执行完整的文件扩展工具流程
        for (String fileName : testFileNames) {
            // 1. 解析文件扩展名
            String ext = FileExtUtil.parseFileExt(fileName);
            String expectedExt = com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName);
            
            // 验证委托调用结果一致
            assertEquals(expectedExt, ext, "文件扩展名解析结果应与委托调用一致: " + fileName);
            
            // 2. 提取图片原始字段名（如果适用）
            String originalFieldName = FileExtUtil.extractImageOriginalFieldName(fileName);
            String expectedOriginalFieldName = com.facishare.paas.appframework.common.util.FileExtUtil.extractImageOriginalFieldName(fileName);
            
            // 验证委托调用结果一致
            assertEquals(expectedOriginalFieldName, originalFieldName, "图片原始字段名提取结果应与委托调用一致: " + fileName);
        }
        
        // 验证常量一致性
        assertEquals(com.facishare.paas.appframework.common.util.FileExtUtil.IMAGE_FLAG, FileExtUtil.IMAGE_FLAG, "IMAGE_FLAG常量应与委托类一致");
        
        // 验证嵌入式图片字段判断
        boolean isEmbeddedImage = FileExtUtil.isEmbeddedImageField(mockFieldDescribe);
        boolean expectedIsEmbeddedImage = com.facishare.paas.appframework.common.util.FileExtUtil.isEmbeddedImageField(mockFieldDescribe);
        assertEquals(expectedIsEmbeddedImage, isEmbeddedImage, "嵌入式图片字段判断结果应与委托调用一致");
    }

    @Test
    @DisplayName("集成测试 - 兼容性验证")
    void testIntegration_CompatibilityVerification() {
        // Arrange - 测试各种边界情况的兼容性
        String[] edgeCaseFileNames = {
            null,
            "",
            ".",
            "..",
            ".hidden",
            "file.",
            "file..",
            "file.ext.backup",
            "UPPERCASE.EXT",
            "mixed.Case.File.TXT"
        };

        // Act & Assert - 验证所有边界情况的兼容性
        for (String fileName : edgeCaseFileNames) {
            // 解析文件扩展名的兼容性
            String ext = FileExtUtil.parseFileExt(fileName);
            String expectedExt = com.facishare.paas.appframework.common.util.FileExtUtil.parseFileExt(fileName);
            assertEquals(expectedExt, ext, "边界情况文件扩展名解析应保持兼容性: " + fileName);

            // 提取图片原始字段名的兼容性 - 处理null情况
            if (fileName == null) {
                // null情况应该抛出相同的异常
                assertThrows(NullPointerException.class, () -> {
                    FileExtUtil.extractImageOriginalFieldName(fileName);
                });
                assertThrows(NullPointerException.class, () -> {
                    com.facishare.paas.appframework.common.util.FileExtUtil.extractImageOriginalFieldName(fileName);
                });
            } else {
                String originalFieldName = FileExtUtil.extractImageOriginalFieldName(fileName);
                String expectedOriginalFieldName = com.facishare.paas.appframework.common.util.FileExtUtil.extractImageOriginalFieldName(fileName);
                assertEquals(expectedOriginalFieldName, originalFieldName, "边界情况图片原始字段名提取应保持兼容性: " + fileName);
            }
        }
        
        // 验证null字段描述的兼容性 - 应该抛出相同的异常
        assertThrows(NullPointerException.class, () -> {
            FileExtUtil.isEmbeddedImageField(null);
        });
        assertThrows(NullPointerException.class, () -> {
            com.facishare.paas.appframework.common.util.FileExtUtil.isEmbeddedImageField(null);
        });
    }
}
