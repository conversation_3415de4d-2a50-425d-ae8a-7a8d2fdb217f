package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.facishare.crm.openapi.Utils.QUOTE_API_NAME;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class QuoteObjectProviderTest {

    @Mock
    private IObjectDescribe objectDescribe;

    private QuoteObjectProvider quoteObjectProvider;

    @BeforeEach
    void setUp() {
        quoteObjectProvider = new QuoteObjectProvider();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getObjectCode方法返回正确的报价单API名称
     */
    @Test
    @DisplayName("getObjectCode - 返回正确的报价单API名称")
    void testGetObjectCode_返回正确的报价单API名称() {
        // 执行被测试方法
        String result = quoteObjectProvider.getObjectCode();

        // 验证结果
        assertEquals(QUOTE_API_NAME, result);
        assertEquals("QuoteObj", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOpenWorkFlow方法始终返回true
     */
    @Test
    @DisplayName("getOpenWorkFlow - 始终返回true")
    void testGetOpenWorkFlow_始终返回true() {
        // 执行被测试方法
        boolean result = quoteObjectProvider.getOpenWorkFlow(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsCheckOutOwner方法始终返回true
     */
    @Test
    @DisplayName("getIsCheckOutOwner - 始终返回true")
    void testGetIsCheckOutOwner_始终返回true() {
        // 执行被测试方法
        boolean result = quoteObjectProvider.getIsCheckOutOwner(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsUpdateOwner方法始终返回true
     */
    @Test
    @DisplayName("getIsUpdateOwner - 始终返回true")
    void testGetIsUpdateOwner_始终返回true() {
        // 执行被测试方法
        boolean result = quoteObjectProvider.getIsUpdateOwner(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIsRemoveOutTeamMember方法始终返回true
     */
    @Test
    @DisplayName("getIsRemoveOutTeamMember - 始终返回true")
    void testGetIsRemoveOutTeamMember_始终返回true() {
        // 执行被测试方法
        boolean result = quoteObjectProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证结果
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectDescribe为null时，各方法的处理
     */
    @Test
    @DisplayName("各方法 - objectDescribe为null时的处理")
    void test各方法_objectDescribe为null时的处理() {
        // 测试getOpenWorkFlow
        boolean openWorkFlowResult = quoteObjectProvider.getOpenWorkFlow(null);
        assertTrue(openWorkFlowResult);

        // 测试getIsCheckOutOwner
        boolean checkOutOwnerResult = quoteObjectProvider.getIsCheckOutOwner(null);
        assertTrue(checkOutOwnerResult);

        // 测试getIsUpdateOwner
        boolean updateOwnerResult = quoteObjectProvider.getIsUpdateOwner(null);
        assertTrue(updateOwnerResult);

        // 测试getIsRemoveOutTeamMember
        boolean removeOutTeamMemberResult = quoteObjectProvider.getIsRemoveOutTeamMember(null);
        assertTrue(removeOutTeamMemberResult);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试所有方法的一致性，确保都返回true
     */
    @Test
    @DisplayName("所有方法 - 一致性测试确保都返回true")
    void test所有方法_一致性测试确保都返回true() {
        // 执行所有被测试方法
        String objectCode = quoteObjectProvider.getObjectCode();
        boolean openWorkFlow = quoteObjectProvider.getOpenWorkFlow(objectDescribe);
        boolean checkOutOwner = quoteObjectProvider.getIsCheckOutOwner(objectDescribe);
        boolean updateOwner = quoteObjectProvider.getIsUpdateOwner(objectDescribe);
        boolean removeOutTeamMember = quoteObjectProvider.getIsRemoveOutTeamMember(objectDescribe);

        // 验证结果
        assertEquals(QUOTE_API_NAME, objectCode);
        assertTrue(openWorkFlow);
        assertTrue(checkOutOwner);
        assertTrue(updateOwner);
        assertTrue(removeOutTeamMember);

        // 验证所有布尔方法都返回相同的值
        assertEquals(openWorkFlow, checkOutOwner);
        assertEquals(checkOutOwner, updateOwner);
        assertEquals(updateOwner, removeOutTeamMember);
    }
}
