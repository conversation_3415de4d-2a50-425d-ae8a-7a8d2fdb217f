package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.userdefobj.DefObjConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ImportObjectModuleManagerTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private ImportObjectProvider defaultProvider;

    @Mock
    private ImportObjectProviderProxy proxy;

    @Mock
    private ImportObjectProvider testProvider1;

    @Mock
    private ImportObjectProvider testProvider2;

    @Mock
    private ImportObjectProvider emptyModuleProvider;

    private ImportObjectModuleManager importObjectModuleManager;

    @BeforeEach
    void setUp() {
        importObjectModuleManager = new ImportObjectModuleManager();
        ReflectionTestUtils.setField(importObjectModuleManager, "defaultProvider", defaultProvider);
        ReflectionTestUtils.setField(importObjectModuleManager, "proxy", proxy);

        // 配置Mock行为
        when(testProvider1.getObjectModule()).thenReturn("TEST_MODULE_1");
        when(testProvider2.getObjectModule()).thenReturn("TEST_MODULE_2");
        when(emptyModuleProvider.getObjectModule()).thenReturn(null);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext方法正确初始化providerMap
     */
    @Test
    @DisplayName("setApplicationContext - 正确初始化providerMap")
    void testSetApplicationContext_正确初始化providerMap() {
        // 准备测试数据
        Map<String, ImportObjectProvider> beanMap = new HashMap<>();
        beanMap.put("testProvider1", testProvider1);
        beanMap.put("testProvider2", testProvider2);
        beanMap.put("emptyModuleProvider", emptyModuleProvider);

        // 配置Mock行为
        when(applicationContext.getBeansOfType(ImportObjectProvider.class)).thenReturn(beanMap);

        // 执行被测试方法
        importObjectModuleManager.setApplicationContext(applicationContext);

        // 验证结果 - 通过getLocalProvider验证providerMap是否正确初始化
        Optional<ImportObjectProvider> provider1 = importObjectModuleManager.getLocalProvider("TEST_MODULE_1");
        Optional<ImportObjectProvider> provider2 = importObjectModuleManager.getLocalProvider("TEST_MODULE_2");
        Optional<ImportObjectProvider> emptyProvider = importObjectModuleManager.getLocalProvider("EMPTY_MODULE");

        assertTrue(provider1.isPresent());
        assertEquals(testProvider1, provider1.get());
        assertTrue(provider2.isPresent());
        assertEquals(testProvider2, provider2.get());
        assertFalse(emptyProvider.isPresent());

        // 验证Mock交互
        verify(applicationContext).getBeansOfType(ImportObjectProvider.class);
        verify(testProvider1).getObjectModule();
        verify(testProvider2).getObjectModule();
        verify(emptyModuleProvider).getObjectModule();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当objectModule为UDOBJ时返回defaultProvider
     */
    @Test
    @DisplayName("getProvider - objectModule为UDOBJ时返回defaultProvider")
    void testGetProvider_objectModule为UDOBJ时返回defaultProvider() {
        // 执行被测试方法
        ImportObjectProvider result = importObjectModuleManager.getProvider(DefObjConstants.UDOBJ);

        // 验证结果
        assertEquals(defaultProvider, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当objectModule为udobj（小写）时返回defaultProvider
     */
    @Test
    @DisplayName("getProvider - objectModule为udobj小写时返回defaultProvider")
    void testGetProvider_objectModule为udobj小写时返回defaultProvider() {
        // 执行被测试方法
        ImportObjectProvider result = importObjectModuleManager.getProvider("udobj");

        // 验证结果
        assertEquals(defaultProvider, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当找到本地provider时返回本地provider
     */
    @Test
    @DisplayName("getProvider - 找到本地provider时返回本地provider")
    void testGetProvider_找到本地provider时返回本地provider() {
        // 准备测试数据 - 先初始化providerMap
        Map<String, ImportObjectProvider> beanMap = new HashMap<>();
        beanMap.put("testProvider1", testProvider1);
        when(applicationContext.getBeansOfType(ImportObjectProvider.class)).thenReturn(beanMap);
        importObjectModuleManager.setApplicationContext(applicationContext);

        // 执行被测试方法
        ImportObjectProvider result = importObjectModuleManager.getProvider("TEST_MODULE_1");

        // 验证结果
        assertEquals(testProvider1, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getProvider方法当找不到本地provider时返回远程provider
     */
    @Test
    @DisplayName("getProvider - 找不到本地provider时返回远程provider")
    void testGetProvider_找不到本地provider时返回远程provider() {
        // 执行被测试方法
        ImportObjectProvider result = importObjectModuleManager.getProvider("UNKNOWN_MODULE");

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof RemoteImportObjectProvider);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalProvider方法当存在本地provider时返回Optional包装的provider
     */
    @Test
    @DisplayName("getLocalProvider - 存在本地provider时返回Optional包装的provider")
    void testGetLocalProvider_存在本地provider时返回Optional包装的provider() {
        // 准备测试数据 - 先初始化providerMap
        Map<String, ImportObjectProvider> beanMap = new HashMap<>();
        beanMap.put("testProvider1", testProvider1);
        when(applicationContext.getBeansOfType(ImportObjectProvider.class)).thenReturn(beanMap);
        importObjectModuleManager.setApplicationContext(applicationContext);

        // 执行被测试方法
        Optional<ImportObjectProvider> result = importObjectModuleManager.getLocalProvider("TEST_MODULE_1");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(testProvider1, result.get());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLocalProvider方法当不存在本地provider时返回空Optional
     */
    @Test
    @DisplayName("getLocalProvider - 不存在本地provider时返回空Optional")
    void testGetLocalProvider_不存在本地provider时返回空Optional() {
        // 执行被测试方法
        Optional<ImportObjectProvider> result = importObjectModuleManager.getLocalProvider("UNKNOWN_MODULE");

        // 验证结果
        assertFalse(result.isPresent());
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectModule为null时，getProvider和getLocalProvider方法的处理
     */
    @Test
    @DisplayName("各方法 - objectModule为null时的处理")
    void test各方法_objectModule为null时的处理() {
        // 测试getProvider
        ImportObjectProvider providerResult = importObjectModuleManager.getProvider(null);
        assertNotNull(providerResult);
        assertTrue(providerResult instanceof RemoteImportObjectProvider);

        // 测试getLocalProvider
        Optional<ImportObjectProvider> localProviderResult = importObjectModuleManager.getLocalProvider(null);
        assertFalse(localProviderResult.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当objectModule为空字符串时的处理
     */
    @Test
    @DisplayName("各方法 - objectModule为空字符串时的处理")
    void test各方法_objectModule为空字符串时的处理() {
        // 测试getProvider
        ImportObjectProvider providerResult = importObjectModuleManager.getProvider("");
        assertNotNull(providerResult);
        assertTrue(providerResult instanceof RemoteImportObjectProvider);

        // 测试getLocalProvider
        Optional<ImportObjectProvider> localProviderResult = importObjectModuleManager.getLocalProvider("");
        assertFalse(localProviderResult.isPresent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext方法过滤掉objectModule为空的provider
     */
    @Test
    @DisplayName("setApplicationContext - 过滤掉objectModule为空的provider")
    void testSetApplicationContext_过滤掉objectModule为空的provider() {
        // 准备测试数据
        Map<String, ImportObjectProvider> beanMap = new HashMap<>();
        beanMap.put("testProvider1", testProvider1);
        beanMap.put("emptyModuleProvider", emptyModuleProvider);

        // 配置Mock行为
        when(applicationContext.getBeansOfType(ImportObjectProvider.class)).thenReturn(beanMap);

        // 执行被测试方法
        importObjectModuleManager.setApplicationContext(applicationContext);

        // 验证结果 - emptyModuleProvider不应该被添加到providerMap中
        Optional<ImportObjectProvider> provider1 = importObjectModuleManager.getLocalProvider("TEST_MODULE_1");
        assertTrue(provider1.isPresent());
        assertEquals(testProvider1, provider1.get());

        // 验证空模块的provider没有被添加
        Optional<ImportObjectProvider> emptyProvider = importObjectModuleManager.getLocalProvider(null);
        assertFalse(emptyProvider.isPresent());
    }
}
