package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.appframework.metadata.dto.CurrencyConfig;
import com.facishare.paas.appframework.metadata.dto.CurrencyInfo;
import com.facishare.paas.appframework.metadata.quote.FillQuoteFieldValueArg;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component("quoteValueService")
public class QuoteValueServiceImpl implements QuoteValueService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private FieldDataConverterManager fieldDataConverterManager;
    @Autowired
    private OrgService orgService;
    @Autowired
    private MultiCurrencyLogicService multiCurrencyLogicService;
    @Autowired
    private MetaDataGlobalService metaDataGlobalService;

    public static final String deletedOption = "#%$";
    private static final Set<String> CALCULATE_QUOTE_TYPE = ImmutableSet.of(IFieldType.SELECT_ONE, IFieldType.TIME, IFieldType.DATE,
            IFieldType.DATE_TIME, IFieldType.SELECT_MANY, IFieldType.TRUE_OR_FALSE, IFieldType.COUNTRY, IFieldType.PROVINCE,
            IFieldType.CITY, IFieldType.DISTRICT, IFieldType.PERCENTILE, IFieldType.NUMBER, IFieldType.CURRENCY,
            IFieldType.RECORD_TYPE);

    private static final Set<String> NEED_ORIG_VALUE_TYPE = ImmutableSet.of(IFieldType.SELECT_ONE, IFieldType.SELECT_MANY,
            IFieldType.TRUE_OR_FALSE, IFieldType.RECORD_TYPE, IFieldType.COUNTRY, IFieldType.PROVINCE, IFieldType.CITY,
            IFieldType.DISTRICT, IFieldType.PERCENTILE);

    @Override
    public void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe, boolean isConvertLocation) {
        fillQuoteFieldValue(user, objectDataList, describe, null, isConvertLocation);
    }

    @Override
    public void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                                    Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation) {
        List<Quote> quoteList = ObjectDescribeExt.of(describe).getQuoteFieldDescribes();
        fillQuoteFieldValue(user, objectDataList, describe, refObjectDataMap, isConvertLocation, quoteList);
    }

    @Override
    public void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                                    Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation, List<Quote> quoteList) {
        fillQuoteFieldValue(user, objectDataList, describe, refObjectDataMap, isConvertLocation, quoteList, null);
    }

    @Override
    public void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                                    Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation,
                                    List<Quote> quoteList, IObjectData masterData) {
        fillQuoteFieldValue(user, objectDataList, describe, refObjectDataMap, isConvertLocation, quoteList, masterData, false);
    }

    @Override
    public void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe, Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation,
                                    List<Quote> quoteList, IObjectData masterData, boolean isForCalculate) {

        fillQuoteFieldValue(user, objectDataList, describe, refObjectDataMap, isConvertLocation, quoteList, masterData,
                isForCalculate, FillQuoteFieldValueArg.builder().isFillMultiRegion(true).build());
    }

    @Override
    public void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe, Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation,
                                    List<Quote> quoteList, IObjectData masterData, boolean isForCalculate, FillQuoteFieldValueArg fillQuoteFieldValueArg) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        if (CollectionUtils.empty(quoteList)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("QuoteValueServiceImpl.fillQuoteFieldValue");
        try {
            List<QuoteInfo> quoteInfoList = generateQuoteInfoList(quoteList, describe, user);
            stopWatch.lap("generateQuoteInfoList");
            if (CollectionUtils.empty(quoteInfoList)) {
                return;
            }

            //给data补充id，防止后面出现空指针
            Tuple<List<IObjectData>, List<IObjectData>> noIdDataTuple = ObjectDataExt.fillDataId(objectDataList, masterData, describe);
            stopWatch.lap("fillDataId");

            fillQuoteDescribeInfo(user.getTenantId(), quoteInfoList);
            stopWatch.lap("fillQuoteDescribeInfo");

            objectDataList.forEach(data -> quoteList.stream().filter(quote -> ObjectDataExt.of(data).toMap().containsKey(quote.getApiName()))
                    .forEach(quote -> {
                        data.set(quote.getApiName(), null);
                        ObjectDataExt.of(data).remove(FieldDescribeExt.getLookupNameByFieldName(quote.getApiName()));
                        ObjectDataExt.of(data).remove(FieldDescribeExt.getQuotedValueNameByFieldName(quote.getApiName()));
                    }));

            //补充普通引用字段的值
            fillCommonQuoteFieldValue(user, objectDataList, describe, refObjectDataMap, isConvertLocation, masterData, quoteInfoList, isForCalculate, fillQuoteFieldValueArg);
            stopWatch.lap("fillCommonQuoteFieldValue");

            //补充人员的名称
            fillEmployeeInfo(user, objectDataList, quoteInfoList, isForCalculate);
            stopWatch.lap("fillEmployeeInfo");

            ///补充部门的名称
            fillDeptInfo(user, objectDataList, quoteInfoList, isForCalculate);
            stopWatch.lap("fillDeptInfo");

            //补充查找关联
            fillQuotedLookupName(user, objectDataList, describe, quoteInfoList, isForCalculate);
            stopWatch.lap("fillQuotedLookupName");
            //补充乡镇和村的名称
            fillAreaInfo(user, objectDataList, quoteInfoList, isForCalculate);
            stopWatch.lap("fillAreaInfo");
//        fillTownAndVillageInfo(user, objectDataList, describe, quoteInfoList, isForCalculate);

            //打印计算日志，方便排查问题
            if (RequestUtil.isCalculateLogEnable()) {
                List<String> quoteFieldNames = quoteList.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
                objectDataList.forEach(data -> log.info("calcQuote on:{},di:{},r:{}", describe.getApiName(),
                        ObjectDataExt.of(data).getIdForCalculate(), ObjectDataExt.of(data).toMap(quoteFieldNames)));
            }

            //data去掉补充的id，防止影响到后续的业务逻辑
            ObjectDataExt.removeDataId(noIdDataTuple, describe);
            stopWatch.lap("removeDataId");
        } finally {
            stopWatch.logSlow(3000);
        }
    }

//    private void fillTownAndVillageInfo(User user, List<IObjectData> objectDataList, IObjectDescribe describe, List<QuoteInfo> quoteInfoList, boolean isForCalculate) {
//        fillTownAndVillageInfo(user, objectDataList,  describe, quoteInfoList, isForCalculate, IFieldType.TOWN);
//        fillTownAndVillageInfo(user, objectDataList, describe, quoteInfoList, isForCalculate, IFieldType.VILLAGE);
//    }

    private void fillAreaInfo(User user, List<IObjectData> objectDataList, List<QuoteInfo> quoteInfoList, boolean isForCalculate) {
        // 补充国家省市区
        List<String> fieldTypes = Lists.newArrayList(IFieldType.TOWN, IFieldType.VILLAGE);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BACK_FILL_COUNTRY_PROVINCE_CITY_DISTRICT, user.getTenantId())) {
            fieldTypes.addAll(Arrays.asList(
                    IFieldType.COUNTRY,
                    IFieldType.PROVINCE,
                    IFieldType.CITY,
                    IFieldType.DISTRICT
            ));
        }
        fillAreaInfo(user, objectDataList, quoteInfoList, isForCalculate, fieldTypes);
    }

    private void fillAreaInfo(User user, List<IObjectData> objectDataList, List<QuoteInfo> quoteInfoList, boolean isForCalculate, List<String> fieldTypes) {
        fillReferenceFieldInfo(user, objectDataList, quoteInfoList, isForCalculate, fieldTypes,
                codes -> metaDataGlobalService.batchQueryAreaLabelsByCodes(user, codes)
                        .stream()
                        .collect(Collectors.toMap(MetaDataGlobalService.CountryInfo::getValue, Function.identity(), (x, y) -> x)),
                this::formatAreaNames);
    }

    // 格式化地区名称
    private String formatAreaNames(List<String> areaCodes, Map<String, MetaDataGlobalService.CountryInfo> areaInfoMap) {
        return areaCodes.stream()
                .map(code -> {
                    MetaDataGlobalService.CountryInfo countryInfo = areaInfoMap.get(code);
                    return Optional.ofNullable(countryInfo).map(MetaDataGlobalService.CountryInfo::getLabel).orElse(code);
                })
                .collect(Collectors.joining(","));
    }

    private void fillDeptInfo(User user, List<IObjectData> objectDataList, List<QuoteInfo> quoteInfoList, boolean isForCalculate) {
        fillReferenceFieldInfo(user, objectDataList, quoteInfoList, isForCalculate, ObjectDescribeExt.DEPARTMENT_FIELD_TYPES,
                deptIds -> fetchDeptInfoMap(user, deptIds),
                this::formatDeptNames
        );
    }

    // 获取部门信息列表
    private Map<String, QueryDeptInfoByDeptIds.DeptInfo> fetchDeptInfoMap(User user, Set<String> deptIds) {
        try {
            List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoList = orgService.getAllDeptInfoNameByIds(
                    user.getTenantId(), user.getUserId(), Lists.newArrayList(deptIds));
            return CollectionUtils.nullToEmpty(deptInfoList)
                    .stream()
                    .collect(Collectors.toMap(QueryDeptInfoByDeptIds.DeptInfo::getDeptId, Function.identity(), (x, y) -> x));
        } catch (Exception e) {
            log.error("fetchDeptInfoList fail! ei:{}, message:{}", user.getTenantId(), e.getMessage(), e);
            return Maps.newHashMap();
        }
    }

    // 格式化部门名称
    private String formatDeptNames(List<String> deptIds, Map<String, QueryDeptInfoByDeptIds.DeptInfo> deptInfoMap) {
        return deptIds.stream()
                .map(deptId -> {
                    QueryDeptInfoByDeptIds.DeptInfo deptInfo = deptInfoMap.get(deptId);
                    if (Objects.isNull(deptInfo) || StringUtils.isBlank(deptInfo.getDeptName())) {
                        return deptId;
                    }
                    if (Objects.equals(QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE.getCode(), deptInfo.getStatus())) {
                        return deptInfo.getDeptName();
                    }
                    return deptInfo.getDeptName() + "(" + I18NExt.text(I18NKey.HAS_BEEN_DISCONTINU) + ")";
                })
                .collect(Collectors.joining(","));
    }

    private void fillCommonQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                                           Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation,
                                           IObjectData masterData, List<QuoteInfo> quoteInfoList, boolean isForCalculate,
                                           FillQuoteFieldValueArg fillQuoteFieldValueArg) {
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(objectDataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        for (QuoteInfo info : quoteInfoList) {
            if (Objects.isNull(info.getQuotedDescribe())) {
                log.warn("quoted describe is null,tenantId:{},objectApiName:{},quotedDescribeApiName:{}",
                        user.getTenantId(), describe.getApiName(), info.getQuotedDescribeApiName());
                continue;
            }

            Set<String> idList = getIdList(objectDataList, info.getFieldInfoList());
            if (CollectionUtils.empty(idList)) {
                continue;
            }
            info.setIdList(idList);

            boolean batchFillCountryProvinceCityDistrict = UdobjGrayConfig.isAllow(
                    UdobjGrayConfigKey.BACK_FILL_COUNTRY_PROVINCE_CITY_DISTRICT,
                    user.getTenantId()
            );
            List<QuoteInfo.QuoteFieldInfo> fieldInfoList = filterQuoteFieldInfoList(info, batchFillCountryProvinceCityDistrict);

            //计算服务触发的计算使用多线程，防止超时出错
            if (isForCalculate) {
                calculateCommonQuoteWithOneDescribe(user, describe, refObjectDataMap, isConvertLocation,
                        masterData, isForCalculate, synchronizedDataList, info, fieldInfoList, fillQuoteFieldValueArg);
            } else {
                parallelTask.submit(() -> calculateCommonQuoteWithOneDescribe(user, describe, refObjectDataMap, isConvertLocation,
                        masterData, isForCalculate, synchronizedDataList, info, fieldInfoList, fillQuoteFieldValueArg));
            }
        }
        if (!isForCalculate) {
            try {
                parallelTask.await(getWaitTime(), TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("calcQuote error,user:{},objectApiName:{}", user, describe.getApiName(), e);
            }
        }
    }

    private int getWaitTime() {
        if (RequestUtil.isCepRequest()) {
            return 5;
        }
        return AppFrameworkConfig.fillQuoteFieldInfoWaitTime();
    }

    private void calculateCommonQuoteWithOneDescribe(User user, IObjectDescribe describe, Map<String, List<IObjectData>> refObjectDataMap,
                                                     boolean isConvertLocation, IObjectData masterData, boolean isForCalculate,
                                                     List<IObjectData> synchronizedDataList, QuoteInfo info, List<QuoteInfo.QuoteFieldInfo> fieldInfoList,
                                                     FillQuoteFieldValueArg fillQuoteFieldValueArg) {
        try {
            fillUdObjData(user, info, refObjectDataMap, masterData);
            if (CollectionUtils.empty(fieldInfoList)) {
                return;
            }
            fillQuoteImageInfoForLowVersion(info.getQuotedDescribe(), fieldInfoList, synchronizedDataList);

            for (IObjectData data : synchronizedDataList) {
                for (QuoteInfo.QuoteFieldInfo quoteFieldInfo : fieldInfoList) {
                    String sourceValue = getFieldStringValue(data, quoteFieldInfo.getLookupFieldApiName());
                    if (Strings.isNullOrEmpty(sourceValue)) {
                        continue;
                    }

                    Tuple<Object, Object> tuple = getQuoteValue(sourceValue, quoteFieldInfo, info, isConvertLocation, isForCalculate, fillQuoteFieldValueArg);

                    if (isForCalculate && CALCULATE_QUOTE_TYPE.contains(quoteFieldInfo.getQuotedFieldType())) {
                        data.set(quoteFieldInfo.getFieldApiName(), tuple.getKey());
                        if (RequestUtil.isDebugMode()) {
                            log.info("calcQuote on:{},fn:{},di:{},r:{}", describe.getApiName(), quoteFieldInfo.getFieldApiName(),
                                    ObjectDataExt.of(data).getIdForCalculate(), tuple.getKey());
                        } else {
                            log.debug("calcQuote on:{},fn:{},di:{},r:{}", describe.getApiName(), quoteFieldInfo.getFieldApiName(),
                                    ObjectDataExt.of(data).getIdForCalculate(), tuple.getKey());
                        }
                    } else {
                        data.set(quoteFieldInfo.getFieldApiName(), tuple.getValue());
                        if (RequestUtil.isDebugMode()) {
                            log.info("calcQuote on:{},fn:{},di:{},r:{}", describe.getApiName(), quoteFieldInfo.getFieldApiName(),
                                    ObjectDataExt.of(data).getIdForCalculate(), tuple.getValue());
                        } else {
                            log.debug("calcQuote on:{},fn:{},di:{},r:{}", describe.getApiName(), quoteFieldInfo.getFieldApiName(),
                                    ObjectDataExt.of(data).getIdForCalculate(), tuple.getValue());
                        }
                    }

                    if (!isForCalculate) {
                        //引用单选和布尔字段的时候，将option的value存在字段apiName__v中，深研那边国际化使用
                        if (NEED_ORIG_VALUE_TYPE.contains(quoteFieldInfo.getQuotedFieldType())) {
                            data.set(FieldDescribeExt.getQuotedValueNameByFieldName(quoteFieldInfo.getFieldApiName()), tuple.getKey());
                            fillSelectOptionsOther(sourceValue, info, quoteFieldInfo, data);
                        }
                        fillSelectOptions(info.getQuotedDescribe(), quoteFieldInfo, tuple, data);
                    }
                }
            }
            if (!isForCalculate) {
                // 引用金额字段处理
                fillQuoteCurrencyFieldInfo(user, info, describe, fieldInfoList, synchronizedDataList, fillQuoteFieldValueArg.isFillMultiRegion());
            }
        } catch (Exception e) {
            log.error("calcQuote failed,tenantId:{},objectApiName:{},quotedDescribeApiName:{},fieldInfoList:{}",
                    user.getTenantId(), describe.getApiName(), info.getQuotedDescribeApiName(), info.getFieldInfoList(), e);
            //如果是计算服务，抛出异常，触发重试
            if (RequestUtil.isCalculateContext()) {
                throw e;
            }
        }
    }

    // TODO L: 引用字段补充金额显示
    private void fillQuoteCurrencyFieldInfo(User user, QuoteInfo info, IObjectDescribe describe, List<QuoteInfo.QuoteFieldInfo> fieldInfoList,
                                            List<IObjectData> synchronizedDataList, boolean isFillMultiRegion) {
        if (CollectionUtils.empty(synchronizedDataList)) {
            return;
        }
        List<QuoteInfo.QuoteFieldInfo> quoteFieldInfos = fieldInfoList.stream()
                .filter(x -> IFieldType.CURRENCY.equals(x.getQuotedFieldType()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(quoteFieldInfos)) {
            return;
        }

        Map<String, String> dataIdAndCurrencyCodes = info.getDataList().stream()
                .filter(x -> Objects.nonNull(ObjectDataExt.of(x).getCurrency()))
                .collect(Collectors.toMap(DBRecord::getId, y -> ObjectDataExt.of(y).getCurrency(), (x, y) -> x));
        if (CollectionUtils.empty(dataIdAndCurrencyCodes)) {
            // 开通多货币并不一定有币种，不开通多货币一定没有币种，所以没有币种值时格式化引用金额字段值
            // __r 设置 千分撇格式
            for (QuoteInfo.QuoteFieldInfo quoteFieldInfo : quoteFieldInfos) {
                for (IObjectData data : synchronizedDataList) {
                    ObjectDataExt dataExt = ObjectDataExt.of(data);
                    String numberValue = dataExt.get(quoteFieldInfo.getFieldApiName(), String.class);
                    if (StringUtils.isBlank(numberValue)) {
                        continue;
                    }
                    String formatedNumber = ObjectDataExt.formatNumberThousand(numberValue, isFillMultiRegion);
                    dataExt.set(FieldDescribeExt.getLookupNameByFieldName(quoteFieldInfo.getFieldApiName()), formatedNumber);
                }
            }
            return;
        }
        CurrencyConfig currencyConfig = multiCurrencyLogicService.findCurrencyTransStatus(user);
        if (!currencyConfig.isMultiCurrencyEnable()) {   // 未开启多币种
            return;
        }
        List<MtCurrency> currencyList = multiCurrencyLogicService.findCurrencyByCodes(user, Lists.newArrayList(dataIdAndCurrencyCodes.values()));
        if (CollectionUtils.empty(currencyList)) {
            return;
        }
        MtCurrency functionalCurrency = CollectionUtils.findOrAdd(currencyList, // 获取本位币
                (MtCurrency currency) -> BooleanUtils.isTrue(currency.getIsFunctional()),
                () -> multiCurrencyLogicService.findFunctionalCurrency(user));

        // 查询其他币种到到user个人币种的汇率
        String userCurrencyCode = multiCurrencyLogicService.findPersonCurrencyCode(user);
        MtCurrency userCurrency;
        Map<String, String> rateToUserCurrenyCollect;
        if (currencyConfig.isUserCurrencyEnable() && StringUtils.isNotBlank(userCurrencyCode)) {
            userCurrency = CollectionUtils.findOrAdd(currencyList,
                    (MtCurrency currency) -> Objects.equals(userCurrencyCode, currency.getCurrencyCode()),
                    () -> multiCurrencyLogicService.findCurrencyByCode(userCurrencyCode, user));
            rateToUserCurrenyCollect = CollectionUtils.nullToEmpty(multiCurrencyLogicService.findCurrencyExchangeList(user, userCurrencyCode))
                    .stream().collect(Collectors.toMap(x -> x.getFromCurrencyCode(), x -> x.getExchangeRate(), (x1, x2) -> x1));
        }
        else {
            userCurrency = null;
            rateToUserCurrenyCollect = Maps.newHashMap();
        }

        for (QuoteInfo.QuoteFieldInfo quoteFieldInfo : quoteFieldInfos) {
            for (IObjectData data : synchronizedDataList) {
                ObjectDataExt dataExt = ObjectDataExt.of(data);
                String numberValue = dataExt.get(quoteFieldInfo.getFieldApiName(), String.class);
                if (StringUtils.isBlank(numberValue)) {
                    continue;
                }
                String dataId = dataExt.get(quoteFieldInfo.getLookupFieldApiName(), String.class);
                IFieldDescribe quoteFieldDescribe = ObjectDescribeExt.of(info.getQuotedDescribe()).getFieldDescribe(quoteFieldInfo.getQuotedFieldName());
                String currencyType = quoteFieldDescribe.getCurrencyType();
                String currencyCode;
                if (Objects.equals(currencyType, MtCurrency.FUNCTIONAL_CURRENCY)) {
                    currencyCode = functionalCurrency.getCurrencyCode();
                } else {
                    currencyCode = dataIdAndCurrencyCodes.get(dataId);
                }
                MtCurrency currencyData = currencyList.stream().filter(x -> Objects.equals(currencyCode, x.getCurrencyCode())).findFirst().orElse(null);

                String formattedNumber = MtCurrency.formatCurrencyNumber(numberValue, currencyData, quoteFieldDescribe, rateToUserCurrenyCollect.get(currencyCode), userCurrency);
                dataExt.set(FieldDescribeExt.getLookupNameByFieldName(quoteFieldInfo.getFieldApiName()), formattedNumber);
                String currencyFieldApiName = FieldDescribeExt.getCurrencyFieldName(quoteFieldInfo.getFieldApiName());
                if (Objects.nonNull(currencyData)) {
                    CurrencyInfo currencyInfo = CurrencyInfo.builder()
                            .fieldApiName(currencyFieldApiName)
                            .objectApiName(describe.getApiName())
                            .prefix(currencyData.getRealCurrencyPrefix())
                            .suffix(currencyData.getRealCurrencySuffix())
                            .build();
                    dataExt.set(currencyFieldApiName, currencyInfo);
                }
            }
        }
    }

    private void fillSelectOptionsOther(String sourceValue, QuoteInfo quoteInfo, QuoteInfo.QuoteFieldInfo quoteFieldInfo, IObjectData data) {
        quoteInfo.getDataList().stream()
                .filter(objectData -> Objects.equals(objectData.getId(), sourceValue))
                .map(objectData -> getSelectOtherValue(quoteFieldInfo, objectData))
                .filter(Objects::nonNull)
                .findFirst()
                .ifPresent(otherValue -> data.set(FieldDescribeExt.getSelectOther(quoteFieldInfo.getFieldApiName()), otherValue));
    }

    private String getSelectOtherValue(QuoteInfo.QuoteFieldInfo quoteFieldInfo, IObjectData objectData) {
        if (IFieldType.SELECT_ONE.equals(quoteFieldInfo.getQuotedFieldType())) {
            String otherOption = objectData.get(quoteFieldInfo.getQuotedFieldName(), String.class);
            if ("other".equals(otherOption)) {
                return objectData.get(FieldDescribeExt.getSelectOther(quoteFieldInfo.getQuotedFieldName()), String.class);
            }
        } else if (IFieldType.SELECT_MANY.equals(quoteFieldInfo.getQuotedFieldType())) {
            Object o = objectData.get(quoteFieldInfo.getQuotedFieldName());
            if (o instanceof List && ((List) o).contains("other")) {
                return objectData.get(FieldDescribeExt.getSelectOther(quoteFieldInfo.getQuotedFieldName()), String.class);
            }
        }
        return null;
    }

    private void fillSelectOptions(IObjectDescribe objectDescribe, QuoteInfo.QuoteFieldInfo quoteFieldInfo, Tuple<Object, Object> tuple, IObjectData data) {
        String quotedFieldType = quoteFieldInfo.getQuotedFieldType();
        if (!IFieldType.SELECT_ONE.equals(quotedFieldType) && !IFieldType.SELECT_MANY.equals(quotedFieldType)
                && !IFieldType.RECORD_TYPE.equals(quotedFieldType)) {
            return;
        }
        String displayKey = quoteFieldInfo.getFieldApiName() + "__r";
        data.set(displayKey, tuple.getValue());
        if (ObjectDataExt.isValueEmpty(tuple.getKey())) {
            return;
        }
        if (IFieldType.SELECT_ONE.equals(quotedFieldType)) {
            SelectOne fieldDescribe = (SelectOne) objectDescribe.getFieldDescribe(quoteFieldInfo.getQuotedFieldName());
            boolean present = fieldDescribe.getSelectOptions().stream()
                    .anyMatch(x -> StringUtils.equals(x.getValue(), String.valueOf(tuple.getKey())));
            if (!present) {
                data.set(displayKey, deletedOption);
            }
        } else if (IFieldType.SELECT_MANY.equals(quotedFieldType)) {
            List<String> keyList = (List<String>) tuple.getKey();
            List<String> valueList = Splitter.on(",").omitEmptyStrings().splitToList(Strings.nullToEmpty((String) tuple.getValue()));
            if (keyList.size() != valueList.size()) {
                SelectMany fieldDescribe = (SelectMany) objectDescribe.getFieldDescribe(quoteFieldInfo.getQuotedFieldName());
                Map<String, String> optionMap = fieldDescribe.getSelectOptions().stream()
                        .collect(Collectors.toMap(x -> x.getValue(), x -> x.getLabel()));
                List<String> result = keyList.stream().map(x -> optionMap.getOrDefault(x, deletedOption)).collect(Collectors.toList());
                data.set(displayKey, StringUtils.join(result, ","));
            }
        }
    }

    private void fillQuotedLookupName(User user, List<IObjectData> objectDataList, IObjectDescribe describe, List<QuoteInfo> quoteInfoList, boolean isForCalculate) {
        try {
            Map<String, Set<String>> quotedLookupIdMap = Maps.newHashMap();
            Map<String, List<QuoteInfo.LookupQuoteFieldInfo>> quoteInfoMap = Maps.newConcurrentMap();
            quoteInfoList.stream().filter(a -> CollectionUtils.notEmpty(a.getDataList())).forEach(a -> {
                Map<String, IObjectData> lookupDataMap = a.getDataList().stream().collect(Collectors.toMap(d -> d.getId(), d -> d));
                a.getFieldInfoList().stream().filter(b -> ObjectDescribeExt.of(a.getQuotedDescribe()).isReference(b.getQuotedFieldName())).forEach(b -> {
                    String lookupFieldApiName = b.getLookupFieldApiName();
                    objectDataList.stream().filter(d -> !ObjectDataExt.isValueEmpty(d.get(lookupFieldApiName)))
                            .filter(d -> lookupDataMap.containsKey(d.get(lookupFieldApiName))).forEach(d -> {
                                String sourceValue = getFieldStringValue(lookupDataMap.get(d.get(lookupFieldApiName)), b.getQuotedFieldName());
                                if (!Strings.isNullOrEmpty(sourceValue)) {
                                    //isForCalculate=true时表示计算服务落地的计算，使用原值(lookup的数据id)存库，否则查询lookup数据的name用于展示
                                    if (isForCalculate) {
                                        d.set(b.getFieldApiName(), sourceValue);
                                    } else {
                                        IFieldDescribe fieldDescribe = a.getQuotedDescribe().getFieldDescribe(b.getQuotedFieldName());
                                        String targetApiName = ((ObjectReferenceFieldDescribe) fieldDescribe).getTargetApiName();
                                        quoteInfoMap.putIfAbsent(d.getId(), Lists.newArrayList());
                                        quoteInfoMap.get(d.getId()).add(QuoteInfo.LookupQuoteFieldInfo.of(b, targetApiName, sourceValue));
                                        quotedLookupIdMap.putIfAbsent(targetApiName, Sets.newHashSet());
                                        quotedLookupIdMap.get(targetApiName).add(sourceValue);
                                    }
                                }
                            });
                });
            });

            //只有非计算服务落地的调用才需要继续查询lookup数据的name，否则直接返回
            if (isForCalculate || CollectionUtils.empty(quotedLookupIdMap)) {
                return;
            }

            Map<String, Map<String, String>> idNameMap = Maps.newConcurrentMap();
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            quotedLookupIdMap.forEach((lookupApiName, lookupIds) -> {
                idNameMap.putIfAbsent(lookupApiName, Maps.newHashMap());
                parallelTask.submit(() -> calculateLookupNameWithOneDescribe(user, idNameMap, lookupApiName, lookupIds));
            });
            try {
                parallelTask.await(getWaitTime(), TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.error("fill quoted lookup name time out,user:{},apiName:{},quotedLookupIdMap:{}", user, describe.getApiName(), quotedLookupIdMap, e);
            }
            objectDataList.stream().forEach(a -> {
                if (Objects.isNull(a)) {
                    log.warn("objectData is null,tenantId:{},objectApiName:{}", user.getTenantId(), describe.getApiName());
                    return;
                }
                if (Objects.isNull(a.getId())) {
                    log.warn("objectData has no id,tenantId:{},objectApiName:{},data:{}", user.getTenantId(), describe.getApiName(), a.getName());
                    return;
                }
                if (!quoteInfoMap.containsKey(a.getId())) {
                    return;
                }
                quoteInfoMap.get(a.getId()).forEach(b -> {
                    QuoteInfo.QuoteFieldInfo quoteFieldInfo = b.getQuoteFieldInfo();
                    String targetApiName = b.getTargetApiName();
                    String sourceValue = b.getSourceValue();
                    a.set(quoteFieldInfo.getFieldApiName(), idNameMap.get(targetApiName).get(sourceValue));
                    a.set(FieldDescribeExt.getQuotedValueNameByFieldName(quoteFieldInfo.getFieldApiName()), sourceValue);
                    if (RequestUtil.isDebugMode()) {
                        log.info("calcQuote on:{},fn:{},di:{},r:{}", describe.getApiName(), quoteFieldInfo.getFieldApiName(),
                                ObjectDataExt.of(a).getIdForCalculate(), idNameMap.get(targetApiName).get(sourceValue));
                    } else {
                        log.debug("calcQuote on:{},fn:{},di:{},r:{}", describe.getApiName(), quoteFieldInfo.getFieldApiName(),
                                ObjectDataExt.of(a).getIdForCalculate(), idNameMap.get(targetApiName).get(sourceValue));
                    }
                });
            });
        } catch (Exception e) {
            log.error("fillQuotedLookupName in quote field error, ei:{}, object:{}", user.getTenantId(), describe.getApiName(), e);
        }
    }

    private void calculateLookupNameWithOneDescribe(User user, Map<String, Map<String, String>> idNameMap,
                                                    String lookupApiName, Set<String> lookupIds) {
        IActionContext context = ActionContextExt.of(user).getContext();
        if (AppFrameworkConfig.isSupportDisplayNameFieldEnterprise(user.getTenantId())
                && AppFrameworkConfig.isSupportDisplayNameField(lookupApiName)) {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), lookupApiName);
            if (ObjectDescribeExt.of(objectDescribe).isSupportDisplayName()) {
                List<IObjectData> dataList = findObjectData(user, Lists.newArrayList(lookupIds), lookupApiName, Lists.newArrayList(FieldDescribeExt.DISPLAY_NAME));
                dataList.forEach(obj -> idNameMap.get(lookupApiName).put(obj.getId(), obj.get(FieldDescribeExt.DISPLAY_NAME, String.class)));
                return;
            }
        }
        List<INameCache> recordNames = metaDataService.findRecordName(context, lookupApiName, Lists.newArrayList(lookupIds));
        recordNames.stream()
                .filter(x -> StringUtils.isNotBlank(x.getId()) && StringUtils.isNotBlank(x.getName()))
                .forEach(x -> idNameMap.get(lookupApiName).put(x.getId(), x.getName()));
    }

    private void fillEmployeeInfo(User user, List<IObjectData> objectDataList, List<QuoteInfo> quoteInfoList, boolean isForCalculate) {
        fillReferenceFieldInfo(user, objectDataList, quoteInfoList, isForCalculate, ObjectDescribeExt.EMPLOYEE_FIELD_TYPES,
                userIds -> fetchUserInfoMap(user, userIds),
                this::formatEmployeeNames);
    }


    // 格式化员工名称
    private String formatEmployeeNames(List<String> employeeIds, Map<String, UserInfo> userInfoMap) {
        return employeeIds.stream()
                .map(employeeId -> {
                    UserInfo userInfo = userInfoMap.get(employeeId);
                    if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getName())) {
                        return employeeId;
                    }
                    if (UserInfo.NORMAL_STATUS == userInfo.getStatus()) {
                        return userInfo.getName();
                    }
                    return userInfo.getName() + "(" + I18NExt.text(I18NKey.HAS_BEEN_DISCONTINU) + ")";
                })
                .collect(Collectors.joining(","));
    }

    private Map<String, UserInfo> fetchUserInfoMap(User user, Set<String> userIdList) {
        if (CollectionUtils.empty(userIdList)) {
            return Maps.newHashMap();
        }
        try {
            return orgService.getUserInfoMapByIds(user.getTenantId(), user.getUserId(), Lists.newArrayList(userIdList)
            );
        } catch (Exception e) {
            log.warn("fetchUserInfoMap fail! ei:{}, message:{}", user.getTenantId(), e.getMessage());
        }
        return Maps.newHashMap();
    }


    /**
     * 通用方法：填充引用字段值（适用于部门和员工字段）
     *
     * @param user           用户信息
     * @param objectDataList 对象数据列表
     * @param quoteInfoList  引用信息列表
     * @param isForCalculate 是否用于计算
     * @param fieldTypes     字段类型列表
     * @param infoFetcher    信息获取器
     * @param formatter      格式化器
     * @param <T>            信息类型
     */
    private <T> void fillReferenceFieldInfo(User user, List<IObjectData> objectDataList, List<QuoteInfo> quoteInfoList,
                                            boolean isForCalculate, List<String> fieldTypes,
                                            Function<Set<String>, Map<String, T>> infoFetcher,
                                            BiFunction<List<String>, Map<String, T>, String> formatter) {

        // 提前返回，避免不必要的处理
        if (CollectionUtils.empty(objectDataList) || CollectionUtils.empty(quoteInfoList)) {
            return;
        }

        // 如果是计算服务，直接处理并返回
        if (isForCalculate) {
            processReferenceIdsForCalculate(objectDataList, quoteInfoList, fieldTypes);
            return;
        }

        // 收集ID
        Map<String, List<String>> fieldNameAndDataId2IdMap = collectReferenceIds(objectDataList, quoteInfoList, fieldTypes, false);

        // 提取所有需要查询的ID
        Set<String> idList = extractIds(fieldNameAndDataId2IdMap);
        if (CollectionUtils.empty(idList)) {
            return;
        }

        // 获取信息
        Map<String, T> infoMap = infoFetcher.apply(idList);

        // 填充显示名称
        try {
            fillReferenceDisplayNames(objectDataList, quoteInfoList, fieldNameAndDataId2IdMap, infoMap, fieldTypes, formatter);
        } catch (Exception e) {
            log.warn("fillReferenceDisplayNames fail! ei:{}, idList:{}", user.getTenantId(), idList, e);
        }
    }

    // 填充引用显示名称
    private <T> void fillReferenceDisplayNames(List<IObjectData> objectDataList, List<QuoteInfo> quoteInfoList,
                                               Map<String, List<String>> fieldNameAndDataId2IdMap, Map<String, T> infoMap,
                                               List<String> fieldTypes, BiFunction<List<String>, Map<String, T>, String> formatter) {

        // 过滤有效的 quoteInfo
        List<QuoteInfo> validQuoteInfos = quoteInfoList.stream()
                .filter(a -> CollectionUtils.notEmpty(a.getDataList()))
                .collect(Collectors.toList());

        for (QuoteInfo quoteInfo : validQuoteInfos) {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(quoteInfo.getQuotedDescribe());

            // 过滤指定类型字段
            List<QuoteInfo.QuoteFieldInfo> filteredFields = quoteInfo.getFieldInfoList().stream()
                    .filter(b -> describeExt.isSpecifiedFieldType(b.getQuotedFieldName(), fieldTypes))
                    .collect(Collectors.toList());

            for (QuoteInfo.QuoteFieldInfo fieldInfo : filteredFields) {
                fillReferenceDisplayName(
                        objectDataList,
                        fieldInfo,
                        fieldNameAndDataId2IdMap,
                        infoMap,
                        formatter
                );
            }
        }
    }

    // 处理引用字段
    private void processReferenceField(List<IObjectData> objectDataList, QuoteInfo.QuoteFieldInfo quoteFieldInfo,
                                       Map<String, IObjectData> lookupDataMap, ObjectDescribeExt quotedDescribeExt,
                                       Map<String, List<String>> fieldNameAndDataId2IdMap, boolean isForCalculate) {

        String lookupFieldApiName = quoteFieldInfo.getLookupFieldApiName();
        IFieldDescribe fieldDescribe = quotedDescribeExt.getFieldDescribe(quoteFieldInfo.getQuotedFieldName());

        for (IObjectData data : objectDataList) {
            Object lookupValue = data.get(lookupFieldApiName);
            if (ObjectDataExt.isValueEmpty(lookupValue)) {
                continue;
            }

            IObjectData lookupData = lookupDataMap.get(lookupValue);
            if (lookupData == null) {
                continue;
            }

            // 根据字段类型获取值
            List<String> values;
            if (FieldDescribeExt.of(fieldDescribe).isEmployeeField() ||
                    FieldDescribeExt.of(fieldDescribe).isEmployeeManyField()) {
                values = ObjectDataExt.of(lookupData).getEmployeeFieldValueByFieldDescribe(fieldDescribe);
            } else if (FieldDescribeExt.of(fieldDescribe).isDepartmentField() ||
                    FieldDescribeExt.of(fieldDescribe).isDepartmentManyField()) {
                values = ObjectDataExt.of(lookupData).getDepartmentFieldValueByFieldDescribe(fieldDescribe);
            } else if (FieldDescribeExt.of(fieldDescribe).isRegionField()) {
                values = ObjectDataExt.of(lookupData).getRegionFieldValueByFieldDescribe(fieldDescribe);
            } else {
                continue;
            }

            if (CollectionUtils.empty(values)) {
                continue;
            }

            // 计算服务落地的调用使用原值存库，否则查询名称用于展示
            if (isForCalculate) {
                if (FieldDescribeExt.of(fieldDescribe).isRegionField()) {
                    data.set(quoteFieldInfo.getFieldApiName(), values.get(0));
                } else {
                    data.set(quoteFieldInfo.getFieldApiName(), values);
                }
            } else {
                Object value = FieldDescribeExt.of(fieldDescribe).isEmployeeField() ||
                        FieldDescribeExt.of(fieldDescribe).isDepartmentField() ||
                        FieldDescribeExt.of(fieldDescribe).isRegionField() ?
                        values.get(0) : values;
                // 设置__v
                data.set(FieldDescribeExt.getQuotedValueNameByFieldName(quoteFieldInfo.getFieldApiName()), value);
                String key = quoteFieldInfo.getQuotedFieldName() + "|" + lookupData.getId();
                fieldNameAndDataId2IdMap.put(key, values);
            }
        }
    }


    // 处理计算服务的引用ID
    private void processReferenceIdsForCalculate(List<IObjectData> objectDataList, List<QuoteInfo> quoteInfoList,
                                                 List<String> fieldTypes) {
        collectReferenceIds(objectDataList, quoteInfoList, fieldTypes, true);
    }

    // 提取ID集合
    private Set<String> extractIds(Map<String, List<String>> fieldNameAndDataId2IdMap) {
        return fieldNameAndDataId2IdMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }

    // 收集引用ID信息
    private Map<String, List<String>> collectReferenceIds(List<IObjectData> objectDataList,
                                                          List<QuoteInfo> quoteInfoList,
                                                          List<String> fieldTypes,
                                                          boolean isForCalculate) {
        Map<String, List<String>> fieldNameAndDataId2IdMap = Maps.newHashMap();

        try {
            // 预先过滤有效的 quoteInfo
            List<QuoteInfo> validQuoteInfos = quoteInfoList.stream()
                    .filter(quoteInfo -> CollectionUtils.notEmpty(quoteInfo.getDataList()))
                    .collect(Collectors.toList());

            for (QuoteInfo quoteInfo : validQuoteInfos) {
                // 创建一次 ObjectDescribeExt 对象并复用
                ObjectDescribeExt quotedDescribeExt = ObjectDescribeExt.of(quoteInfo.getQuotedDescribe());

                // 构建查找数据映射
                Map<String, IObjectData> lookupDataMap = quoteInfo.getDataList().stream()
                        .collect(Collectors.toMap(IObjectData::getId, Function.identity()));

                // 过滤出指定类型字段
                List<QuoteInfo.QuoteFieldInfo> filteredFields = quoteInfo.getFieldInfoList().stream()
                        .filter(quoteFieldInfo -> quotedDescribeExt.isSpecifiedFieldType(quoteFieldInfo.getQuotedFieldName(), fieldTypes))
                        .collect(Collectors.toList());

                // 处理每个字段
                for (QuoteInfo.QuoteFieldInfo quoteFieldInfo : filteredFields) {
                    processReferenceField(
                            objectDataList,
                            quoteFieldInfo,
                            lookupDataMap,
                            quotedDescribeExt,
                            fieldNameAndDataId2IdMap,
                            isForCalculate
                    );
                }
            }
        } catch (Exception e) {
            log.error("Collecting reference IDs failed: {}", e.getMessage(), e);
            //如果是计算服务，抛出异常，触发重试
            if (RequestUtil.isCalculateContext()) {
                throw e;
            }
        }

        return fieldNameAndDataId2IdMap;
    }


    // 填充引用显示名称
    private <T> void fillReferenceDisplayName(
            List<IObjectData> objectDataList,
            QuoteInfo.QuoteFieldInfo fieldInfo,
            Map<String, List<String>> fieldNameAndDataId2IdMap,
            Map<String, T> infoMap,
            BiFunction<List<String>, Map<String, T>, String> formatter) {

        String lookupFieldApiName = fieldInfo.getLookupFieldApiName();
        String fieldApiName = fieldInfo.getFieldApiName();

        for (IObjectData data : objectDataList) {
            String quoteDataId = data.get(lookupFieldApiName, String.class);
            if (Strings.isNullOrEmpty(quoteDataId)) {
                continue;
            }

            String mapKey = fieldInfo.getQuotedFieldName() + "|" + quoteDataId;
            List<String> quoteIds = fieldNameAndDataId2IdMap.get(mapKey);
            if (CollectionUtils.empty(quoteIds)) {
                continue;
            }

            String displayValue = formatter.apply(quoteIds, infoMap);
            data.set(fieldApiName, displayValue);

            // 日志记录
            logQuoteCalculation(data, fieldApiName, displayValue);
        }
    }

    // 日志记录
    private void logQuoteCalculation(IObjectData data, String fieldApiName, String value) {
        if (RequestUtil.isDebugMode()) {
            log.info("calcQuote on:{},fn:{},di:{},r:{}", data.getDescribeApiName(), fieldApiName, ObjectDataExt.of(data).getIdForCalculate(), value);
        } else {
            log.debug("calcQuote on:{},fn:{},di:{},r:{}", data.getDescribeApiName(), fieldApiName, ObjectDataExt.of(data).getIdForCalculate(), value);
        }
    }

    private List<QuoteInfo> generateQuoteInfoList(List<Quote> quoteList, IObjectDescribe describe, User user) {
        Map<String, QuoteInfo> map = Maps.newHashMap();
        for (Quote quote : quoteList) {
            QuoteInfo info = getQuotedLookupDescribeApiName(quote, describe);
            if (info == null) {
                continue;
            }
            if (map.containsKey(info.getQuotedDescribeApiName())) {
                map.get(info.getQuotedDescribeApiName()).getFieldInfoList().addAll(info.getFieldInfoList());
            } else {
                info.setUser(user);
                map.put(info.getQuotedDescribeApiName(), info);
            }
        }
        return Lists.newArrayList(map.values());
    }

    private void fillQuoteDescribeInfo(String tenantId, List<QuoteInfo> quoteInfoList) {
        List<String> apiNames = quoteInfoList.stream().map(x -> x.getQuotedDescribeApiName()).distinct().collect(Collectors.toList());
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopyIfGray(tenantId, apiNames);
        quoteInfoList.forEach(x -> x.setQuotedDescribe(describeMap.get(x.getQuotedDescribeApiName())));
    }

    public QuoteInfo getQuotedLookupDescribeApiName(Quote quote, IObjectDescribe describe) {
        String quoteField = quote.getQuoteField();
        if (Strings.isNullOrEmpty(quoteField)) {
            return null;
        }
        String[] split = quoteField.split("\\.");
        if (split.length == 0) {
            return null;
        }
        String fieldName = split[0];
        //查找关联字段
        String fieldApiName = fieldName.replace("__r", "");

        IFieldDescribe field = ObjectDescribeExt.of(describe).getFieldDescribeSilently(fieldApiName).orElse(null);
        if (null == field) {
            return null;
        }

        String refObjApiName = null;
        if (IFieldType.OBJECT_REFERENCE.equalsIgnoreCase(field.getType())
                || IFieldType.OBJECT_REFERENCE_MANY.equalsIgnoreCase(field.getType())) {
            refObjApiName = ((IObjectReferenceField) field).getTargetApiName();
        } else if (IFieldType.MASTER_DETAIL.equalsIgnoreCase(field.getType())) {
            refObjApiName = ((MasterDetail) field).getTargetApiName();
        }

        List<QuoteInfo.QuoteFieldInfo> list = Lists.newArrayList(QuoteInfo.QuoteFieldInfo.builder()
                .fieldApiName(quote.getApiName())
                .lookupFieldApiName(fieldApiName)
                .quotedFieldName(split[1])
                .quotedFieldType(quote.getQuoteFieldType())
                .index(quote.isIndex())
                .build());
        return QuoteInfo.builder().quotedDescribeApiName(refObjApiName)
                .fieldInfoList(list)
                .build();
    }

    private Set<String> getIdList(List<IObjectData> objectDataList, List<QuoteInfo.QuoteFieldInfo> fieldApiNameSet) {
        Set<String> list = Sets.newHashSet();
        for (IObjectData data : objectDataList) {
            for (QuoteInfo.QuoteFieldInfo fieldApiName : fieldApiNameSet) {
                String value = getFieldStringValue(data, fieldApiName.getLookupFieldApiName());
                if (!Strings.isNullOrEmpty(value)) {
                    list.add(value);
                }
            }
        }
        return list;
    }

    private Tuple<Object, Object> getQuoteValue(String sourceValue, QuoteInfo.QuoteFieldInfo quoteFieldInfo, QuoteInfo quoteInfo,
                                                boolean isConvertLocation, boolean isForCalculate, FillQuoteFieldValueArg fillQuoteFieldValueArg) {
        if (Strings.isNullOrEmpty(sourceValue) || CollectionUtils.empty(quoteInfo.getDataList())) {
            return Tuple.of(null, null);
        }

        IFieldDescribe fieldDescribe = quoteInfo.getQuotedDescribe().getFieldDescribe(quoteFieldInfo.getQuotedFieldName());
        if (null == fieldDescribe) {
            return Tuple.of(null, null);
        }
        quoteFieldInfo.setQuotedFieldType(fieldDescribe.getType());

        List<IObjectData> dataList = quoteInfo.getDataList();
        for (IObjectData data : dataList) {
            String id = data.getId();
            if (!Objects.equals(id, sourceValue)) {
                continue;
            }

            Object value = data.get(fieldDescribe.getApiName());
            if (isForCalculate) {
                return Tuple.of(value, value);
            }
            Object displayValue = getDisplayValue(fieldDescribe, data, quoteInfo.getUser(), isConvertLocation, quoteFieldInfo.isIndex(), fillQuoteFieldValueArg);
            return Tuple.of(value, displayValue);
        }

        return Tuple.of(null, null);
    }

    private Object getDisplayValue(IFieldDescribe fieldDescribe, IObjectData data, User user, boolean isConvertLocation, boolean isIndex,
                                   FillQuoteFieldValueArg fillQuoteFieldValueArg) {
        //图片，附件，大附件不转换，这里增加类型的话需要通知流程侧，他们那边有判断逻辑
        if (Objects.equals(fieldDescribe.getType(), IFieldType.IMAGE) ||
                Objects.equals(fieldDescribe.getType(), IFieldType.FILE_ATTACHMENT) ||
                Objects.equals(fieldDescribe.getType(), IFieldType.BIG_FILE_ATTACHMENT)) {
            return data.get(fieldDescribe.getApiName());
        }

        if (!isConvertLocation && IFieldType.LOCATION.equals(fieldDescribe.getType())) {
            return data.get(fieldDescribe.getApiName());
        }

        FieldDataConverter fieldDataConverter = fieldDataConverterManager.getFieldDataConverter(fieldDescribe.getType());
        DataConvertContext dataConvertContext = DataConvertContext.of(user);

        if (fillQuoteFieldValueArg.isFillMultiRegion()
                && AppFrameworkConfig.isUseMultiRegionByFieldType(FieldDescribeExt.of(fieldDescribe).getTypeOrReturnType())
                && Objects.nonNull(data.get(fieldDescribe.getApiName()))) {

            //数字类型的字段，fillQuoteFieldValueArg.isFillMultiRegionByNumber()为true的情况为打印和导出的调用，需要处理多区域，其余情况不处理多区域
            if (!IFieldType.NUMBER.equals(FieldDescribeExt.of(fieldDescribe).getTypeOrReturnType()) || fillQuoteFieldValueArg.isFillMultiRegionByNumber()) {
                dataConvertContext.setRegion(MultiRegionContextHolder.getUserRegion());
            }
        }

        dataConvertContext.setFromQuoteField(true);
        Object value = fieldDataConverter.convertFieldData(data, fieldDescribe, dataConvertContext);
        String stringValue = String.valueOf(value);
        if (IFieldType.PERCENTILE.equals(fieldDescribe.getType()) && null != value) {
            value = stringValue + "%";
        }
        //文本类型的字段且不落地，才支持多语
        if (FieldDescribeExt.of(fieldDescribe).isTextType() && null != value && !isIndex) {
            Object langValue = FieldDescribeExt.of(fieldDescribe).handlerMultiLang(user.getTenantId(), data);
            if (Objects.nonNull(langValue)) {
                value = langValue;
            }
        }

        return value;
    }

    private String getFieldStringValue(IObjectData data, String apiName) {
        Object o = data.get(apiName);
        return null == o ? "" : String.valueOf(o);
    }

    private void fillUdObjData(User user, QuoteInfo quoteValue, Map<String, List<IObjectData>> refObjectDataMap, IObjectData masterData) {
        if (Objects.isNull(quoteValue) || CollectionUtils.empty(quoteValue.getIdList())) {
            return;
        }

        List<IObjectData> dataList;
        if (Objects.nonNull(masterData) && quoteValue.getQuotedDescribeApiName().equals(masterData.getDescribeApiName())) {
            dataList = Lists.newArrayList(masterData);
        } else if (Objects.nonNull(refObjectDataMap)) {
            dataList = refObjectDataMap.get(quoteValue.getQuotedDescribeApiName());
            if (CollectionUtils.notEmpty(dataList)) {
                dataList = dataList.stream().filter(x -> quoteValue.getIdList().contains(x.getId())).collect(Collectors.toList());
            }
        } else {
            List<String> quotedFieldApiNames = quoteValue.getFieldInfoList().stream().map(x -> x.getQuotedFieldName()).distinct().collect(Collectors.toList());
            quotedFieldApiNames.add(FieldDescribeExt.CURRENCY_FIELD);
            dataList = findObjectData(user, Lists.newArrayList(quoteValue.getIdList()), quoteValue.getQuotedDescribeApiName(), quotedFieldApiNames);
        }
        quoteValue.setDataList(dataList);
    }

    public void fillQuoteImageInfoForLowVersion(IObjectDescribe quotedDescribe, List<QuoteInfo.QuoteFieldInfo> fieldInfoList, List<IObjectData> dataList) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_675)) {
            List<QuoteInfo.QuoteFieldInfo> quoteFieldInfoList = fieldInfoList.stream().filter(x ->
                    quotedDescribe.containsField(x.getQuotedFieldName())
                            && (StringUtils.equals(quotedDescribe.getFieldDescribe(x.getQuotedFieldName()).getType(), IFieldType.IMAGE)
                            || StringUtils.equals(quotedDescribe.getFieldDescribe(x.getQuotedFieldName()).getType(), IFieldType.FILE_ATTACHMENT)
                            || StringUtils.equals(quotedDescribe.getFieldDescribe(x.getQuotedFieldName()).getType(), IFieldType.BIG_FILE_ATTACHMENT)
                    )
            ).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(quoteFieldInfoList)) {
                quoteFieldInfoList.forEach(quoteFieldInfo -> {
                    dataList.forEach(data -> data.set(quoteFieldInfo.getFieldApiName(), I18N.text(I18NKey.QUOTE_IMAGE_NOT_SUPPORT_LOW_VERSION)));
                    fieldInfoList.remove(quoteFieldInfo);
                });
            }
        }
    }

    private List<IObjectData> findObjectData(User user, List<String> ids, String objectApiName, List<String> fieldApiNames) {
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(user)
                .projectionFields(fieldApiNames)
                .includeInvalid(true)
                .skipRelevantTeam(true)
                .isSimple(true)
                .build();
        return metaDataFindService.findObjectDataByIdsWithQueryContext(queryContext, ids, objectApiName);
    }

    @Override
    public void fillQuoteValueVirtualField(User user, IObjectData objectData, Map<String, List<IObjectData>> details) {
        if (Objects.nonNull(objectData) && CollectionUtils.notEmpty(ObjectDataExt.of(objectData).toMap())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FUNCTION_VALUE_CONSISTENT_GRAY_EI, user.getTenantId())) {
            Set<String> apiNames = Sets.newHashSet();
            String describeApiName = objectData.getDescribeApiName();
            apiNames.add(describeApiName);
            if (CollectionUtils.notEmpty(details)) {
                apiNames.addAll(details.keySet());
            }
            Map<String, IObjectDescribe> detailObjectDescribes = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), apiNames);
            IObjectDescribe describe = detailObjectDescribes.get(describeApiName);
            fillQuoteValueVirtualField(user, describe, detailObjectDescribes, objectData, details);
        }
    }

    @Override
    public void fillQuoteValueVirtualField(User user, IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailObjectDescribes,
                                           IObjectData objectData, Map<String, List<IObjectData>> details) {
        if (Objects.isNull(objectData) || Objects.isNull(objectDescribe)
                || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FUNCTION_VALUE_CONSISTENT_GRAY_EI, user.getTenantId())) {
            return;
        }
        fillQuoteFieldValue(user, Lists.newArrayList(objectData), objectDescribe, null, false);
        // 补充__q字段
        List<Quote> quoteList = ObjectDescribeExt.of(objectDescribe).getQuoteFieldDescribes();
        fillFunctionVirtualFieldContext(quoteList, objectData);
        if (CollectionUtils.empty(details) || CollectionUtils.empty(detailObjectDescribes)) {
            return;
        }
        details.keySet().forEach(objectApiName -> {
            IObjectDescribe detailObjectDescribe = detailObjectDescribes.get(objectApiName);
            if (Objects.isNull(detailObjectDescribe)) {
                return;
            }
            List<IObjectData> detailDataList = details.get(objectApiName);
            fillQuoteFieldValue(user, detailDataList, detailObjectDescribe, null, false);
            List<Quote> quoteFieldDescribes = ObjectDescribeExt.of(detailObjectDescribe).getQuoteFieldDescribes();
            CollectionUtils.nullToEmpty(detailDataList).forEach(dataList -> fillFunctionVirtualFieldContext(quoteFieldDescribes, dataList));
        });
    }

    private void fillFunctionVirtualFieldContext(List<Quote> quoteFieldDescribes, IObjectData dataList) {
        Map<String, Object> detailDataMap = ObjectDataExt.of(dataList).toMap();
        CollectionUtils.nullToEmpty(quoteFieldDescribes).forEach(quote -> {
            String quotedValueName = FieldDescribeExt.getQuotedValueNameByFieldName(quote.getApiName());
            if (detailDataMap.containsKey(quotedValueName)) {
                detailDataMap.put(FieldDescribeExt.getQuotedFunctionVirtualFieldByFieldName(quote.getApiName()), detailDataMap.get(quotedValueName));
            }
        });
    }

    private List<QuoteInfo.QuoteFieldInfo> filterQuoteFieldInfoList(QuoteInfo info, boolean batchFillCountryProvinceCityDistrict) {
        return info.getFieldInfoList().stream()
                .filter(fieldInfo -> isValidQuoteField(info.getQuotedDescribe(), fieldInfo, batchFillCountryProvinceCityDistrict))
                .collect(Collectors.toList());
    }

    private boolean isValidQuoteField(IObjectDescribe quotedDescribe, QuoteInfo.QuoteFieldInfo fieldInfo,
                                      boolean batchFillCountryProvinceCityDistrict) {
        if (!quotedDescribe.containsField(fieldInfo.getQuotedFieldName())) {
            return false;
        }

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(quotedDescribe);

        // 排除特殊字段类型
        if (describeExt.isEmployeeField(fieldInfo.getQuotedFieldName()) ||
                describeExt.isReference(fieldInfo.getQuotedFieldName()) ||
                describeExt.isDepartmentField(fieldInfo.getQuotedFieldName())) {
            return false;
        }

        // 处理国家地区字段
        if (batchFillCountryProvinceCityDistrict && describeExt.isCountryAreaField(fieldInfo.getQuotedFieldName())) {
            return false;
        }

        return true;
    }

    @Builder
    @Data
    @EqualsAndHashCode
    public static class QuoteInfo {
        private String quotedDescribeApiName;
        //key为引用字段，value为被引用的字段
        private List<QuoteFieldInfo> fieldInfoList;
        private List<IObjectData> dataList;
        private Set<String> idList;
        private User user;
        private IObjectDescribe quotedDescribe;

        @Builder
        @Data
        @EqualsAndHashCode
        static class QuoteFieldInfo {
            //引用字段的apiName
            private String fieldApiName;
            //引用查找关联对象上字段的apiName
            private String quotedFieldName;
            //查找关联字段的apiName
            private String lookupFieldApiName;
            private String quotedFieldType;
            //引用字段是否落地支持筛选
            private boolean index;
        }

        @Data
        @AllArgsConstructor(staticName = "of")
        static class LookupQuoteFieldInfo {
            private QuoteFieldInfo quoteFieldInfo;
            private String targetApiName;
            private String sourceValue;
        }
    }
}
