package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.util.I18KeyBuildUtils;
import com.facishare.paas.foundation.boot.exception.ResponsiveException;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.data.IDuplicatedSearchRefresh;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Location;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.service.IDuplicatedRuleService;
import com.facishare.paas.metadata.api.service.IDuplicatedSearchService;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.util.DataUtils;
import com.fxiaoke.helper.Triple;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("duplicatedSearchService")
public class DuplicatedSearchServiceImpl implements DuplicatedSearchService {

    public static final String LOCATION_REGEX = "([\\d.]+#%\\$){2}";
    @Autowired
    private IDuplicatedSearchService duplicatedSearchService;

    @Autowired
    private DuplicatedSearchDataService duplicatedSearchDataService;

    @Autowired
    private IDuplicatedRuleService duplicatedRuleService;

    @Autowired
    private LogService logService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private DuplicateSearchProxy duplicateSearchProxy;

    @Autowired
    private MetaDataFindService metaDataFindService;

    @Autowired
    private CRMRestServiceProxy crmRestServiceProxy;

    @Autowired
    private ObjectDataProxy objectDataProxy;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private ConfigService configService;

    @Autowired
    private ManageGroupService manageGroupService;

    @Autowired
    private I18nSettingService i18nSettingService;
    @Autowired
    private LicenseService licenseService;

    private final String MULTI_DUPLICATED_RULE_KEY = "multi_duplicated_rule_";

    private static Map<String, IDuplicatedSearch> duplicatedSearchMap;

    private static List<String> usedNameList = Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.CONTACT_API_NAME, Utils.LEADS_API_NAME, Utils.OPPORTUNITY_API_NAME);

    private static List<String> SFAObjects = Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.CONTACT_API_NAME, Utils.LEADS_API_NAME, Utils.OPPORTUNITY_API_NAME);
    private static List<String> NOT_USE_MULTI_OBJ_LIST = Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.CONTACT_API_NAME, Utils.LEADS_API_NAME);

    private static String appName = ConfigHelper.getProcessInfo().getName();
    private static String serverIp = ConfigHelper.getProcessInfo().getIp();
    private static String profile = ConfigHelper.getProcessInfo().getProfile();

    static {
        ConfigFactory.getConfig("duplicate-rule-config", (config) -> {
            List<IDuplicatedSearch> duplicateRuleList =
                    JSON.parseArray(config.get("presetDuplicateRuleGray"), IDuplicatedSearch.class);
            duplicatedSearchMap = duplicateRuleList.stream()
                    .collect(Collectors.toMap(x -> getKey(x.getDescribeApiName(), x.getType()), x -> ((IDuplicatedSearch) x)));
        });

    }

    @Override
    public IDuplicatedSearch presetDuplicateRule(String apiName, IDuplicatedSearch.Type type, User user) {
        IDuplicatedSearch duplicatedSearchRule = duplicatedSearchMap.getOrDefault(getKey(apiName, type), duplicatedSearchMap.get(getKey("default_object__c", type)));
        return IDuplicatedSearch.builder()
                .describeApiName(apiName)
                .effective(true)
                .enable(duplicatedSearchRule.isEnable())
                .id(duplicatedSearchRule.getId())
                .operatorId(user.getUserId())
                .pendingRules(duplicatedSearchRule.getPendingRules())
                .tenantId(user.getTenantId())
                .type(duplicatedSearchRule.getType())
                .useableRules(duplicatedSearchRule.getUseableRules())
                .version(duplicatedSearchRule.getVersion())
                .build();
    }

    private static String getKey(String apiName, IDuplicatedSearch.Type type) {
        return apiName + type;
    }

    @Override   // 后台编辑后同步
    public IDuplicatedSearch createOrUpdateDuplicatedSearch(User user, IDuplicatedSearch duplicatedSearch, boolean isOverrideUsable) {
        try {
            log.info("createOrUpdateDuplicatedSearch user:{},duplicateSearch:{}", JSON.toJSONString(user), JSON.toJSONString(duplicatedSearch));
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), duplicatedSearch.getDescribeApiName());
            validateRule(duplicatedSearch, objectDescribe, user.getTenantId());
            IDuplicatedSearch result = null;
            result = createOrUpdate(user, duplicatedSearch, isOverrideUsable);
            logService.log(user, EventType.ADD, ActionType.CREATE_OR_UPDATE_DUPLICATE_SEARCH, duplicatedSearch.getDescribeApiName(),
                    String.format(I18N.text(I18NKey.OBJECT) + "%s", objectDescribe.getDisplayName()));
            AuditLogDTO dto = AuditLogDTO.builder()
                    .appName(appName)
                    .serverIp(serverIp)
                    .profile(profile)
                    .action("duplicate_search_setting")
                    .tenantId(user.getTenantId())
                    .userId(user.getUserId())
                    .objectApiNames(duplicatedSearch.getDescribeApiName())
                    .message(JSON.toJSONString(duplicatedSearch))
                    .build();
            BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
            return result;
        } catch (ResponsiveException e) {
            log.warn("createOrUpdateDuplicatedSearch warn,tenantId:{},duplicateSearch:{},e:{}", user.getTenantId(), JSON.toJSONString(duplicatedSearch), e);
            throw new ValidateException(e.getMessage());
        }
    }

    private void validateRule(IDuplicatedSearch duplicatedSearch, IObjectDescribe objectDescribe, String tenantId) {
        if (!AppFrameworkConfig.isGrayDuplicatedSupportDistance(tenantId)) {
            return;
        }
        Set<String> geoFields = DuplicatedSearchExt.of(duplicatedSearch).getGeoFields();
        if (CollectionUtils.empty(geoFields)) {
            return;
        }
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(geoFields);
        List<IFieldDescribe> illegalFields = fieldDescribes.stream().filter(x -> !FieldDescribeExt.of(x).isLocation()).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(illegalFields)) {
            throw new ValidateException(I18nMessage.of(I18NKey.DUPLICATE_FILED_TYPE_ERROR, I18N.text(I18NKey.DUPLICATE_FILED_TYPE_ERROR)));
        }
        List<Location> locationList = fieldDescribes.stream().map(x -> (Location) x).filter(x -> !x.getIsGeoIndex()).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(locationList)) {
            List<String> locationFieldLabel = locationList.stream().map(IFieldDescribe::getLabel).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            throw new ValidateException(I18nMessage.of(I18NKey.DUPLICATE_GEO_FIELD_TYPE_ERROR, I18N.text(I18NKey.DUPLICATE_GEO_FIELD_TYPE_ERROR, locationFieldLabel), locationFieldLabel));
        }

    }

    private IDuplicatedSearch createOrUpdate(User user, IDuplicatedSearch duplicatedSearch, boolean isOverrideUsable) {
        IDuplicatedSearch result;
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), duplicatedSearch.getDescribeApiName())) {
            result = duplicatedRuleService.createOrUpdate(user.getTenantId(), duplicatedSearch.getDescribeApiName(),
                    duplicatedSearch.getRuleApiName(), duplicatedSearch, isOverrideUsable);
            // 翻译查重规则名称和描述
            synDuplicatedRulesNameTranslation(Collections.singletonList(result));
            //  开启了多规则，且是创建查重规则的时候需要添加到分管小组
            if (result.getVersion() == 0 && useMultiRule(user, duplicatedSearch.getDescribeApiName())) {
                manageGroupService.addToManageGroup(user, duplicatedSearch.getDescribeApiName(), duplicatedSearch.getRuleApiName(), ManageGroupType.DUPLICATE_SEARCH_RULE);
            }
        } else {
            result = duplicatedSearchService.createOrUpdate(user.getTenantId(), duplicatedSearch, isOverrideUsable);
        }
        return result;
    }


    @Override
    public Map<IDuplicatedSearch.Type, IDuplicatedSearch> findDuplicatedSearchByApiName(User user, String describeApiName, boolean isPending) {
        List<IDuplicatedSearch> duplicatedSearchList = Lists.newArrayList();
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), describeApiName)) {
            duplicatedSearchList.addAll(duplicatedRuleService.findByDescribeApiName(user.getTenantId(),
                    describeApiName, isPending));
        } else {
            duplicatedSearchList.addAll(duplicatedSearchService.findByDescribeApiName(user.getTenantId(), describeApiName, isPending));
        }
        processRelatedRule(duplicatedSearchList);
        return duplicatedSearchList.stream().collect(Collectors.toMap(IDuplicatedSearch::getType, x -> x, (x1, x2) -> x1));
    }

    private void processRelatedRule(Collection<IDuplicatedSearch> duplicatedSearches) {
        if (CollectionUtils.empty(duplicatedSearches)) {
            return;
        }
        duplicatedSearches.forEach(duplicatedSearch -> DuplicatedSearchExt.of(duplicatedSearch).processRelatedRule2appendAllowCreate());
    }

    // TODO: 2023/3/4 关注被调用的地方换接口
    @Override
    public IDuplicatedSearch findDuplicatedSearchByApiNameAndType(String tenantId, String describeApiName, IDuplicatedSearch.Type type, boolean isPending) {
        IDuplicatedSearch duplicatedSearch = duplicatedSearchService.findOneByDescribeApiNameAndType(tenantId, describeApiName, type, isPending);
        processRelatedRule(Objects.isNull(duplicatedSearch) ? Collections.emptyList() : Lists.newArrayList(duplicatedSearch));
        return duplicatedSearch;
    }

    @Override
    public List<IDuplicatedSearch> findDuplicateSearchByApiNameAndType(String tenantId, String describeApiName, IDuplicatedSearch.Type type, boolean isPending) {
        List<IDuplicatedSearch> duplicatedSearches = duplicatedRuleService.findList(tenantId, describeApiName, null, type, isPending, DuplicateSearchOrderByType.ORDER_BY_SORT.getCode());
        processRelatedRule(duplicatedSearches);
        return duplicatedSearches;
    }

    @Override
    public Map<String, IDuplicatedSearch> findDuplicatedSearchByApiNamesAndType(String tenantId, List<String> describeApiNames, IDuplicatedSearch.Type type, boolean isPending) {
        List<IDuplicatedSearch> duplicatedSearches = duplicatedSearchService.findByDescribeApiNames(tenantId, describeApiNames, type, isPending);
        processRelatedRule(duplicatedSearches);
        return duplicatedSearches.stream().collect(Collectors.toMap(IDuplicatedSearch::getDescribeApiName, x -> x, (v1, v2) -> v1));
    }

    public Map<String, List<IDuplicatedSearch>> findDuplicatedSearchListByApiNamesAndType(String tenantId, List<String> describeApiNames, IDuplicatedSearch.Type type, boolean isPending) {
        List<IDuplicatedSearch> duplicatedSearches = duplicatedSearchService.findByDescribeApiNames(tenantId, describeApiNames, type, isPending);
        processRelatedRule(duplicatedSearches);
        return duplicatedSearches.stream().collect(Collectors.groupingBy(IDuplicatedSearch::getDescribeApiName, Collectors.toList()));
    }

    @Override
    public boolean isEnableDuplicate(User user, String describeApiName, IDuplicatedSearch.Type type, boolean isPending) {
        List<IDuplicatedSearch> duplicatedSearchList = Lists.newArrayList();
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), describeApiName)) {
            duplicatedSearchList.addAll(duplicatedRuleService.findList(user.getTenantId(), describeApiName, user.getUserId(), type, isPending, DuplicateSearchOrderByType.ORDER_BY_SORT.getCode()));
        } else {
            duplicatedSearchList.add(duplicatedSearchService.findOneByDescribeApiNameAndType(user.getTenantId(), describeApiName, type, isPending));
        }
        duplicatedSearchList.removeIf(x -> !DuplicatedSearchExt.isEnableDuplicate(x));
        return CollectionUtils.notEmpty(duplicatedSearchList);
    }

    @Override
    public void updateStatus(User user, String describeApiName, IDuplicatedSearch.Type type, boolean enable) {
        try {
            duplicatedSearchService.enableOrDisable(user.getTenantId(), describeApiName, type, enable);
        } catch (ResponsiveException e) {
            log.error("duplicateSearchUpdateStatus error, user:{},describeApiName:{},type:{},enable:{}", user, describeApiName, type, enable, e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public List<DuplicateSearchResult.DuplicateData> findDuplicateDataByType(String describeApiName, User user, IObjectData objectData, IDuplicatedSearch.Type type, IObjectDescribe objectDescribe) {
        IDuplicatedSearch duplicatedSearch = findDuplicatedSearchByApiNameAndType(user.getTenantId(), describeApiName, type, false);
        if (!DuplicatedSearchExt.isEnableDuplicate(duplicatedSearch)) {
            return Lists.newArrayList();
        }

        IObjectData copyObjectData = ObjectDataExt.of(objectData).copy();
        dealLocation(copyObjectData, objectDescribe, duplicatedSearch);
        dealSelectOne(copyObjectData, objectDescribe);
        dealObjectReferent(user, duplicatedSearch, copyObjectData, objectDescribe);
        dealContactObjTelAndMobile(duplicatedSearch, copyObjectData);

        DuplicateSearch arg = DuplicateSearch.builder()
                .describeApiName(describeApiName)
                .objectData(ObjectDataExt.of(copyObjectData).toMap())
                .type(type)
                .tenantId(user.getTenantId())
                .isIdValue(false)
                .build();

        DuplicateSearchResult result = getDuplicateSearchResult(user, duplicatedSearch, arg);

        if (!result.success()) {
            log.error("searchDuplicateDataByType error, arg:{}, result:{},tenantId:{},userId:{}",
                    JSON.toJSONString(arg), JSON.toJSONString(result), user.getTenantId(), user.getUserId());
            throw new MetaDataException(I18NExt.text(I18NKey.DUPLICATE_CHECK_SERVICE_EXCEPTION));
        }

        if (CollectionUtils.empty(result.getDuplicateDataList())) {
            return Lists.newArrayList();
        }
        duplicatedSearchInEsAndRedis(user, objectData, objectDescribe, duplicatedSearch, result);
        // 1.过滤未开启查重对象的数据2.返回的dataIds为null，set一个空list，以免后面报空指针
        Map<String, Boolean> objectEnableMap = DuplicatedSearchExt.of(duplicatedSearch).getEnableMap();
        result.getDuplicateDataList().forEach(x -> {
            if (!objectEnableMap.getOrDefault(x.getApiName(), false) || Objects.isNull(x.getDataIds())) {
                x.setDataIds(Lists.newArrayList());
            }
        });
        return result.getDuplicateDataList();
    }

    @Override
    public List<DuplicateSearchDataInfo> findDuplicateDataListByType(User user,
                                                                     List<IObjectData> dataList,
                                                                     IDuplicatedSearch.Type type,
                                                                     IObjectDescribe objectDescribe,
                                                                     boolean searchFuzzy) {
        if (CollectionUtils.empty(dataList)) {
            return Collections.emptyList();
        }
        IDuplicatedSearch duplicatedSearch = findDuplicatedSearchByApiNameAndType(user.getTenantId(), objectDescribe.getApiName(), type, false);
        if (!DuplicatedSearchExt.isEnableDuplicate(duplicatedSearch)) {
            return Collections.emptyList();
        }
        // 处理待查重数据的格式
        List<Map<String, Object>> objectDataList = handleFieldValueByDuplicatedSearch(duplicatedSearch, objectDescribe, dataList, user);

        BatchQueryDuplicateSearch.Arg arg = BatchQueryDuplicateSearch.Arg.builder()
                .describeApiName(objectDescribe.getApiName())
                .tenantId(user.getTenantId())
                .type(type.toString())
                .dataList(objectDataList)
                .isIdValue(false)
                .build();
        BatchQueryDuplicateSearch.Result result = duplicateSearchProxy.batchQueryDuplicateSearch(user.getTenantId(), arg);
        if (!result.success()) {
            log.warn("findDuplicateDataListByType fail, ei:{}, describeApiName:{}, message:{}", user.getTenantId(),
                    objectDescribe.getApiName(), result.getMessage());
            throw new MetaDataException(I18NExt.text(I18NKey.DUPLICATE_CHECK_SERVICE_EXCEPTION) + ":" + result.getMessage());
        }

        List<BatchQueryDuplicateSearch.DuplicateSearchResult> duplicateSearchResultInRedis = getDuplicateSearchResultsInRedis(user, dataList, objectDescribe, duplicatedSearch);
        return mereResult(result.getDuplicateDataList(), duplicateSearchResultInRedis);
    }

    @Override
    public List<IObjectData> processFieldValueForDuplicatedSearch(IDuplicatedSearch duplicatedSearch, IObjectDescribe objectDescribe, List<IObjectData> dataList, User user) {
        if (CollectionUtils.empty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.stream()
                .map(objectData -> processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, objectData, user))
                .collect(Collectors.toList());
    }

    private List<BatchQueryDuplicateSearch.DuplicateSearchResult> getDuplicateSearchResultsInRedis(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch) {
        List<BatchQueryDuplicateSearch.DuplicateSearchResult> result = Lists.newArrayList();
        dataList.forEach(objectData -> {
            List<String> idsInRedis = duplicatedSearchInRedis(user, duplicatedSearch, objectDescribe, objectData, false);
            if (CollectionUtils.empty(idsInRedis)) {
                return;
            }
            result.add(BatchQueryDuplicateSearch.DuplicateSearchResult.builder()
                    .describeApiName(objectDescribe.getApiName())
                    .sourceDataId(objectData.getId())
                    .dataIds(Sets.newLinkedHashSet(idsInRedis)).build());
        });
        return result;
    }

    private List<DuplicateSearchDataInfo> mereResult(List<BatchQueryDuplicateSearch.DuplicateSearchResult> duplicateDataList,
                                                     List<BatchQueryDuplicateSearch.DuplicateSearchResult> duplicateSearchResultInRedis) {
        if (CollectionUtils.empty(duplicateDataList) && CollectionUtils.empty(duplicateSearchResultInRedis)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.empty(duplicateDataList)) {
            return DuplicateSearchDataInfo.fromList(duplicateSearchResultInRedis);
        }
        if (CollectionUtils.empty(duplicateSearchResultInRedis)) {
            return DuplicateSearchDataInfo.fromList(duplicateDataList);
        }

        // 合并 es 和 redis 的查询结果
        List<DuplicateSearchDataInfo> result = DuplicateSearchDataInfo.fromList(duplicateDataList);
        Map<String, DuplicateSearchDataInfo> dataInfoMap = result.stream().collect(Collectors.toMap(DuplicateSearchDataInfo::getSourceDataId, it -> it));
        for (BatchQueryDuplicateSearch.DuplicateSearchResult it : duplicateSearchResultInRedis) {
            DuplicateSearchDataInfo dataInfo = dataInfoMap.get(it.getSourceDataId());
            if (Objects.isNull(dataInfo)) {
                result.add(DuplicateSearchDataInfo.from(it));
            } else {
                dataInfo.getDataIds().addAll(it.getDataIds());
            }
        }

        return result;
    }

    /**
     * 处理特殊字段的值
     *
     * @param duplicatedSearch
     * @param objectDescribe
     * @param dataList
     * @param user
     * @return
     */
    private List<Map<String, Object>> handleFieldValueByDuplicatedSearch(IDuplicatedSearch duplicatedSearch,
                                                                         IObjectDescribe objectDescribe, List<IObjectData> dataList, User user) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> result = dataList.stream()
                .map(it -> ObjectDataExt.of(it).copy())
                .map(data -> this.processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, data, user))
                .map(it -> ObjectDataExt.of(it).toMap())
                .collect(Collectors.toList());
        return result;
    }

    public IObjectData processFieldValueForDuplicatedSearch(IDuplicatedSearch duplicatedSearch, IObjectDescribe objectDescribe, IObjectData objectData, User user) {
        IObjectData data = ObjectDataExt.of(objectData).copy();
        dealLocation(data, objectDescribe, duplicatedSearch);
        dealSelectOne(data, objectDescribe);
        dealObjectReferent(user, duplicatedSearch, data, objectDescribe);
        dealContactObjTelAndMobile(duplicatedSearch, data);
        return data;
    }


    /**
     * 查重时，去掉定位字段中的经纬度信息
     * <p>
     * 116.332001#%$39.976601#%$北京市海淀区知春路 -> 北京市海淀区知春路
     *
     * @param objectData
     * @param objectDescribe
     * @param duplicatedSearch
     */
    private void dealLocation(IObjectData objectData, IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch) {
        if (Objects.isNull(duplicatedSearch)) {
            return;
        }
        Set<String> fieldApiNames = getDuplicateSearchFieldApiNames(duplicatedSearch);
        List<IFieldDescribe> locationFields = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(Lists.newArrayList(fieldApiNames)).stream()
                .filter(fieldDescribe -> FieldDescribeExt.of(fieldDescribe).isLocation())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(locationFields)) {
            return;
        }
        locationFields.forEach(field -> {
            String value = objectData.get(field.getApiName(), String.class);
            if (Strings.isNullOrEmpty(value)) {
                return;
            }
            //
            String result = value.replaceFirst(LOCATION_REGEX, "");
            objectData.set(field.getApiName(), result);
            if (AppFrameworkConfig.isGrayDuplicatedSupportDistance(objectDescribe.getTenantId())) {
                String geoFieldName = FieldDescribeExt.getLocalizationNameByFieldName(field.getApiName());
                Triple<Double, Double, String> localizationInfo = DataUtils.parseLocation(value);
                if (Objects.isNull(localizationInfo.first) || Objects.isNull(localizationInfo.second)) {
                    return;
                }
                if (0.0 == localizationInfo.first && 0.0 == localizationInfo.second) {
                    return;
                }
                objectData.set(geoFieldName, localizationInfo.first + "," + localizationInfo.second);
            }
        });
    }

    private void duplicatedSearchInEsAndRedis(User user, IObjectData objectData, IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch, DuplicateSearchResult result) {
        // 新建、编辑查重时过滤本身数据
        List<String> dataIds = Lists.newArrayList();
        result.getDuplicateDataList().stream()
                .filter(a -> StringUtils.equals(a.getApiName(), objectDescribe.getApiName()))
                .forEach(b -> {
                    if (CollectionUtils.notEmpty(b.getDataIds())) {
                        b.getDataIds().remove(objectData.getId());
                        dataIds.addAll(b.getDataIds());
                    }
                });
        if (CollectionUtils.notEmpty(dataIds)) {
            return;
        }
        List<String> objIds = duplicatedSearchInRedis(user, duplicatedSearch, objectDescribe, objectData, true);
        if (CollectionUtils.empty(objIds)) {
            return;
        }
        String currentObjId = objectData.getId();
        for (String idInRedis : objIds) {
            if (idInRedis != null && !idInRedis.equals(currentObjId)) {
                dataIds.add(idInRedis);
            }
        }
        result.getDuplicateDataList().stream()
                .filter(a -> StringUtils.equals(a.getApiName(), objectDescribe.getApiName()))
                .forEach(b -> b.setDataIds(dataIds));
    }

    private List<String> duplicatedSearchInRedis(User user, IDuplicatedSearch duplicatedSearch,
                                                 IObjectDescribe objectDescribe, IObjectData objectData,
                                                 boolean needConvertData) {
        DuplicatedSearchExt duplicatedSearchExt = DuplicatedSearchExt.of(duplicatedSearch);
        Set<String> redisKeys;
        if (needConvertData) {
            redisKeys = duplicatedSearchExt.getRedisKey(objectDescribe, objectData, user.getTenantId(),
                    (data) -> processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, data, user));
        } else {
            redisKeys = duplicatedSearchExt.getRedisKey(objectDescribe, objectData, user.getTenantId(), data -> data);
        }
        if (CollectionUtils.empty(redisKeys)) {
            return Collections.emptyList();
        }
        List<String> objIds = redisDao.mget(redisKeys.toArray(new String[0]));
        log.info("query duplicatedSearchInEsAndRedis : tenantId:{}, objDescApiName:{},redisKeys:{},objIds:{}", objectData.getTenantId(), objectDescribe.getApiName(), redisKeys, objIds);
        if (objIds.stream().allMatch(Objects::isNull)) {
            return Collections.emptyList();
        }
        return objIds;
    }

    private DuplicateSearchResult getDuplicateSearchResult(User user, IDuplicatedSearch duplicatedSearch, DuplicateSearch arg) {
        try {
            log.info("searchDuplicateDataByType tenantId:{},userId:{}, duplicatedSearch:{}, arg:{}",
                    user.getTenantId(), user.getUserId(), JSON.toJSONString(duplicatedSearch), JSON.toJSONString(arg));
            return duplicateSearchProxy.getDuplicateSearchResult(user.getTenantId(), arg);
        } catch (Exception e) {
            log.error("searchDuplicateDataByType error, arg:{}, tenantId:{},userId:{}", JSON.toJSONString(arg), user.getTenantId(), user.getUserId(), e);
            throw new MetaDataException(I18NExt.text(I18NKey.DUPLICATE_CHECK_SERVICE_EXCEPTION) + ":" + e.getMessage());
        }
    }

    private void dealContactObjTelAndMobile(IDuplicatedSearch duplicatedSearch, IObjectData copyObjectData) {
        if (Objects.isNull(duplicatedSearch)) {
            return;
        }
        if (!Utils.CONTACT_API_NAME.equals(copyObjectData.getDescribeApiName())) {
            return;
        }
        Set<String> fieldApiNames = getDuplicateSearchFieldApiNames(duplicatedSearch);
        if (fieldApiNames.contains("tel") && copyObjectData.get("tel") != null) {
            Object tel = ((String) copyObjectData.get("tel")).replaceAll(";:", " ");
            copyObjectData.set("tel", tel);
        }
        if (fieldApiNames.contains("mobile") && copyObjectData.get("mobile") != null) {
            String mobile = ((String) copyObjectData.get("mobile")).replaceAll(";:", " ");
            copyObjectData.set("mobile", mobile);
        }
    }

    private void dealObjectReferent(User user, IDuplicatedSearch duplicatedSearch, IObjectData objectData, IObjectDescribe objectDescribe) {
        if (Objects.isNull(duplicatedSearch)) {
            return;
        }
        if (!IDuplicatedSearch.Type.NEW.equals(duplicatedSearch.getType())) {
            return;
        }
        Set<String> fieldApiNames = getDuplicateSearchFieldApiNames(duplicatedSearch);
        List<IFieldDescribe> referenceFieldDescribes = ObjectDescribeExt.of(objectDescribe).getReferenceFieldDescribes(fieldApiNames);
        if (CollectionUtils.empty(referenceFieldDescribes)) {
            return;
        }

        Map<String, List<IFieldDescribe>> collect = referenceFieldDescribes.stream()
                .filter(x -> Objects.nonNull(objectData.get(x.getApiName())))
                .collect(Collectors.groupingBy(x -> ((ObjectReferenceFieldDescribe) x).getTargetApiName()));
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        collect.forEach((k, v) -> {
            List<String> ids = v.stream()
                    .map(x -> (String) objectData.get(x.getApiName()))
                    .filter(x -> !Strings.isNullOrEmpty(x))
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(ids)) {
                return;
            }
            try {
                List<INameCache> nameCacheList = objectDataProxy.findRecordName(actionContext, k, ids);
                Map<String, Object> nameCacheMap = nameCacheList.stream().filter(x -> StringUtils.isNotBlank(x.getName())).collect(Collectors.toMap(INameCache::getId, INameCache::getName));
                v.forEach(x -> objectData.set(x.getApiName(), nameCacheMap.getOrDefault(objectData.get(x.getApiName()), objectData.get(x.getApiName()))));

            } catch (MetadataServiceException e) {
                log.warn("Error in findRecordName,user:{},objectDescribe:{},ids:{},e:{}", user, objectDescribe, ids, e);
            }
        });
    }

    private Set<String> getDuplicateSearchFieldApiNames(IDuplicatedSearch duplicatedSearch) {
        Set<String> fieldNames = Sets.newHashSet();
        duplicatedSearch.getUseableRules().getRules()
                .forEach(x -> x.getConditions()
                        .forEach(y -> fieldNames.add(y.getFieldName())));
        Optional.ofNullable(duplicatedSearch.getUseableRules().getRelatedDescribes()).orElse(Lists.newArrayList())
                .forEach(a -> a.getRulesDef().getRules()
                        .forEach(b -> b.getConditions()
                                .forEach(c -> fieldNames.add(c.getFieldName()))));
        return fieldNames;
    }

    @Override
    public void dealSelectOne(IObjectData objectData, IObjectDescribe objectDescribe) {
        for (IFieldDescribe fieldDescribe : ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribes()) {
            FieldDescribeExt fieldExt = FieldDescribeExt.of(fieldDescribe);
            if (fieldExt.isSelectOne()) {
                String fieldApiName = fieldDescribe.getApiName();
                Object value = objectData.get(fieldApiName);
                // 处理单选其他
                if (Objects.nonNull(value) && SelectOne.OPTION_OTHER_VALUE.equals(value)) {
                    String otherFieldApiName = FieldDescribeExt.getSelectOther(fieldApiName);
                    Object otherValue = objectData.get(otherFieldApiName);
                    if (Objects.nonNull(otherValue)) {
                        objectData.set(fieldApiName, SelectOne.OPTION_OTHER_VALUE + otherValue);
                        ObjectDataExt.of(objectData).remove(otherFieldApiName);
                    }
                }
            }
            if (fieldExt.isSelectMany()) {
                String fieldApiName = fieldDescribe.getApiName();
                Object o = objectData.get(fieldApiName);
                if (o instanceof List) {
                    // 过滤为空的选项
                    List<String> values = CollectionUtils.nullToEmpty(((List<Object>) o))
                            .stream()
                            .filter(Objects::nonNull)
                            .map(String::valueOf)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    // 处理多选其他
                    if (values.contains(SelectOne.OPTION_OTHER_VALUE)) {
                        String otherFieldApiName = FieldDescribeExt.getSelectOther(fieldApiName);
                        Object otherValue = objectData.get(otherFieldApiName);
                        if (Objects.nonNull(otherValue)) {
                            values.remove(SelectOne.OPTION_OTHER_VALUE);
                            values.add(SelectOne.OPTION_OTHER_VALUE + otherValue);
                        }
                    }
                    // 选项按字母表排序
                    values.sort(String::compareTo);
                    objectData.set(fieldApiName, values);
                }
            }
        }
    }

    public List<IDuplicatedSearch> findAll(String tenantId, boolean isPending) {
        return duplicatedSearchService.findAll(tenantId, isPending);
    }

    @Override
    public List<String> findAllObjectApiNameByType(User user, boolean isPending, IDuplicatedSearch.Type type) {
        List<IDuplicatedSearch> duplicatedSearches = Lists.newArrayList();
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleEi(user.getTenantId())) {
            duplicatedSearches.addAll(findAllByObject(user.getTenantId(), user.getUserId(), type,
                    isPending, DuplicateSearchOrderByType.ORDER_BY_SORT));
        } else {
            duplicatedSearches.addAll(findAll(user.getTenantId(), isPending));
        }

        if (CollectionUtils.empty(duplicatedSearches)) {
            return Lists.newArrayList();
        }
        List<String> objectApiNames = duplicatedSearches.stream()
                .filter(IDuplicatedSearch::isEnable)
                .filter(x -> Objects.equals(type, x.getType()))
                .map(IDuplicatedSearch::getDescribeApiName)
                .collect(Collectors.toList());
        // 把老对象放到前几位
        List<String> chaosList = Lists.newArrayList();
        objectApiNames.removeIf(x -> {
            if (SFAObjects.contains(x)) {
                chaosList.add(x);
                return true;
            }
            return false;
        });
        List<String> orderList = SFAObjects.stream().filter(chaosList::contains).collect(Collectors.toList());
        orderList.addAll(objectApiNames);
        return orderList;
    }


    // ---------------------------------------- 清洗规则
    @Override
    public IDuplicatedSearch createOrUpdateCleanRule(User user, IDuplicatedSearch duplicatedSearch, boolean isOverrideUsable) {
        try {
            log.info("createOrUpdateCleanRule user:{},duplicateSearch:{}", JSON.toJSONString(user), JSON.toJSONString(duplicatedSearch));
            IDuplicatedSearch result;
            if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), duplicatedSearch.getDescribeApiName())) {
                if (StringUtils.isBlank(duplicatedSearch.getRuleApiName())) {
                    duplicatedSearch.setRuleApiName(DuplicatedSearchService.getRuleApiName());
                }
                if (StringUtils.isBlank(duplicatedSearch.getName())) {
                    fillRuleName(duplicatedSearch);
                }
                result = duplicatedRuleService.createOrUpdate(user.getTenantId(), duplicatedSearch.getDescribeApiName(), duplicatedSearch.getRuleApiName(), duplicatedSearch);
            } else {
                result = duplicatedSearchService.createOrUpdate(user.getTenantId(), duplicatedSearch, isOverrideUsable);
            }
            return result;
        } catch (ResponsiveException e) {
            log.error("createOrUpdateCleanRule warn,tenantId:{},duplicateSearch:{},e:{}", user.getTenantId(), JSON.toJSONString(duplicatedSearch), e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public void deleteByDescribeApiName(User user, String describeApiName) {
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), describeApiName)) {
            duplicatedRuleService.deleteByDescribe(user.getTenantId(), describeApiName);
        } else {
            duplicatedSearchService.deleteByDescribeApiName(user.getTenantId(), describeApiName);
        }
        deletedDuplicateSearchManageGroup(user, describeApiName);
    }

    /**
     * 获取该对象下的查重规则，type为空则为全部的查重规则，否则则返回跟type一致的查重规则
     *
     * @param describeApiName
     * @param type
     * @param tenantId
     * @return
     */
    @Override
    public List<IDuplicatedSearch> getDuplicateSearchRuleList(String describeApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType) {
        List<IDuplicatedSearch> duplicatedSearches = duplicatedRuleService.findList(tenantId, describeApiName, null, type, isPending, orderByType.getCode());
        processRelatedRule(duplicatedSearches);
        return transDuplicatedRulesName(duplicatedSearches, false); // 都是新版查重
    }

    @Override // 实时
    public List<IDuplicatedSearch> getDuplicateSearchRuleList(String describeApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType, boolean onTime) {
        return getDuplicateSearchRuleList(null, describeApiName, type, tenantId, isPending, orderByType, onTime);
    }


    @Override // 实时
    public List<IDuplicatedSearch> getDuplicateSearchRuleList(String userId, String describeApiName, IDuplicatedSearch.Type type,
                                                              String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType, boolean onTime) {
        List<IDuplicatedSearch> duplicatedSearches = duplicatedRuleService.findList(tenantId, describeApiName, userId, type, isPending, orderByType.getCode());
        processRelatedRule(duplicatedSearches);//
        return transDuplicatedRulesName(duplicatedSearches, onTime);
    }

    private void synDuplicatedRulesNameTranslation(List<IDuplicatedSearch> duplicatedRules) {
        if (CollectionUtils.empty(duplicatedRules) || !checkContextAndGrayMultiDuplicateRuleEi()) {
            return;
        }
        Map<String, String> keyValue = duplicatedRules.stream()
                .collect(Collectors.toMap(x -> I18KeyBuildUtils.getDuplicatedRulesNameKey(x.getDescribeApiName(), x.getRuleApiName()),
                        IDuplicatedSearch::getName));
        i18nSettingService.saveTransValue(keyValue, true);
    }

    private boolean checkContextAndGrayMultiDuplicateRuleEi() {
        RequestContext context = RequestContextManager.getContext();
        if (Objects.isNull(context)) {
            return false;
        }
        String tenantId = context.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return false;
        }
        if (!AppFrameworkConfig.isGrayMultiDuplicateRuleEi(tenantId)) {
            return false;
        }
        return true;
    }

    private List<IDuplicatedSearch> transDuplicatedRulesName(List<IDuplicatedSearch> duplicatedRules, boolean onTime) {
        if (CollectionUtils.empty(duplicatedRules) || !checkContextAndGrayMultiDuplicateRuleEi()) {
            return duplicatedRules;
        }
        List<String> i18Key = duplicatedRules.stream()
                .map(x -> I18KeyBuildUtils.getDuplicatedRulesNameKey(x.getDescribeApiName(), x.getRuleApiName()))
                .collect(Collectors.toList());
        Map<String, String> i18Map = i18nSettingService.getTransValue(i18Key, false, onTime);
        if (CollectionUtils.notEmpty(i18Map)) {
            duplicatedRules.forEach(x -> {
                String nameKey = I18KeyBuildUtils.getDuplicatedRulesNameKey(x.getDescribeApiName(), x.getRuleApiName());
                x.setName(i18Map.getOrDefault(nameKey, x.getName()));
            });
        }
        return duplicatedRules;
    }

    /**
     * 获取该对象下启用的查重规则，type为空则为全部的查重规则，否则则返回跟type一致的查重规则
     *
     * @param describeApiName
     * @param type
     * @param tenantId
     * @return
     */
    @Override
    public List<IDuplicatedSearch> getEnableDuplicateSearchRuleList(String describeApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType) {
        return getDuplicateSearchRuleList(describeApiName, type, tenantId, isPending, orderByType).stream().filter(IDuplicatedSearch::isEnable).collect(Collectors.toList());
    }

    @Override
    public List<IDuplicatedSearch> getEnableDuplicateSearchRuleList(User user, String describeApiName, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType orderByType) {
        return getDuplicateSearchRuleList(user.getUserId(), describeApiName, type, user.getTenantId(), isPending, orderByType, false).stream()
                .filter(IDuplicatedSearch::isEnable).collect(Collectors.toList());
    }


    @Override
    public IDuplicatedSearch findDuplicatedSearchByRuleApiName(String describeApiName, String duplicateSearchRuleApiName, String tenantId, Boolean isPending) {
        IDuplicatedSearch duplicatedSearch = duplicatedRuleService.findByRuleApiName(tenantId, describeApiName, Lists.newArrayList(duplicateSearchRuleApiName), isPending).stream().findFirst().orElse(null);
        if (Objects.nonNull(duplicatedSearch)) {
            DuplicatedSearchExt.of(duplicatedSearch).processRelatedRule2appendAllowCreate();
        } //
        return duplicatedSearch;
    }

    @Override
    public IDuplicatedSearch findDuplicatedSearchByRuleApiName(String describeApiName, String duplicateSearchRuleApiName, String tenantId, Boolean isPending, boolean onTime) {
        IDuplicatedSearch duplicatedSearch = duplicatedRuleService.findByRuleApiName(tenantId, describeApiName, Lists.newArrayList(duplicateSearchRuleApiName), isPending).stream().findFirst().orElse(null);
        if (Objects.nonNull(duplicatedSearch)) {
            DuplicatedSearchExt.of(duplicatedSearch).processRelatedRule2appendAllowCreate();
        } //
        return Objects.isNull(duplicatedSearch) ? duplicatedSearch : transDuplicatedRulesName(Lists.newArrayList(duplicatedSearch), onTime).get(0);
    }

    @Override
    public IDuplicatedSearch findDuplicatedSearchByRuleApiNameOrFirstRule(String describeApiName, String duplicateSearchRuleApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending) {
        List<IDuplicatedSearch> duplicatedSearches = duplicatedRuleService.findList(tenantId, describeApiName, null, type, isPending, DuplicateSearchOrderByType.ORDER_BY_SORT.getCode());
        IDuplicatedSearch duplicatedSearch = null;
        if (StringUtils.isBlank(duplicateSearchRuleApiName)) {
            duplicatedSearch = duplicatedSearches.stream().findFirst().orElse(null);
        } else {
            duplicatedSearch = duplicatedSearches.stream().filter(x -> StringUtils.equals(duplicateSearchRuleApiName, x.getRuleApiName())).findFirst().orElse(null);
        }
        if (Objects.nonNull(duplicatedSearch)) {
            DuplicatedSearchExt.of(duplicatedSearch).processRelatedRule2appendAllowCreate();
        }
        return duplicatedSearch;
    }

    @Override
    public void deleteByRuleApiName(String objectApiName, String duplicateRuleApiName, String tenantId) {
        duplicatedRuleService.delete(tenantId, objectApiName, duplicateRuleApiName);
        deletedDuplicateSearchManageGroup(User.systemUser(tenantId), objectApiName, Lists.newArrayList(duplicateRuleApiName));
    }

    @Override
    public void enableOrDisable(String tenantId, String describeApiName, String ruleApiName, IDuplicatedSearch.Type type, boolean enable) {
        duplicatedRuleService.enableOrDisable(tenantId, describeApiName, ruleApiName, type, enable);
    }

    @Override
    public void enableOrDisable(User user, String describeApiName, String ruleApiName, IDuplicatedSearch.Type type, boolean enable) {
        duplicatedRuleService.enableOrDisable(user.getTenantId(), describeApiName, ruleApiName, type, enable, user.getUserId());
    }

    @Override
    public Integer getDuplicateSearchRuleCount(String objectApiName, IDuplicatedSearch.Type type, String tenantId) {
        return duplicatedRuleService.findCount(tenantId, objectApiName, type);
    }


    @Override
    public void dealPriority(String describeApiName, HashMap<Integer, String> sortMap, String tenantId) {
        duplicatedRuleService.priority(tenantId, describeApiName, sortMap);
    }

    /**
     * 根据查重规则apiName批量查询查重规则
     *
     * @param describeApiName
     * @param duplicateRuleApiNames
     * @param tenantId
     * @return
     */
    @Override
    public List<IDuplicatedSearch> findDuplicatedSearchByRuleApiNames(String describeApiName, List<String> duplicateRuleApiNames, String tenantId, Boolean isPending) {
        List<IDuplicatedSearch> duplicatedSearches = duplicatedRuleService.findByRuleApiName(tenantId, describeApiName, duplicateRuleApiNames, isPending);
        processRelatedRule(duplicatedSearches);
        return duplicatedSearches;
    }


    /**
     * 根据企业id，规则类型，对象apiName，批量获取对象下所有的查重规则
     *
     * @param tenantId
     * @param objectApiNameList
     * @param type
     * @return
     */

    @Override
    public Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(String tenantId, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType) {
        User user = User.builder()
                .tenantId(tenantId)
                .userId(null)
                .build();
        return findAllDuplicateSearchByApiNameAndType(user, objectApiNameList, type, isPending, searchOrderByType);
    }


    @Override
    public Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(User user, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType) {
        Map<String, List<IDuplicatedSearch>> apiNameToDuplicateList = duplicatedRuleService.findByDescribeApiNames(user.getTenantId(), Lists.newArrayList(objectApiNameList), user.getUserId(), isPending, searchOrderByType.getCode());
        apiNameToDuplicateList.forEach((key, value) -> value.removeIf(x -> !DuplicatedSearchExt.isEnableDuplicate(x) || !type.equals(x.getType())));
        apiNameToDuplicateList.forEach((key, value) -> processRelatedRule(value));
        return apiNameToDuplicateList;
    }

    @Override
    public Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(String tenantId, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType, boolean onTime) {
        User user = User.builder()
                .tenantId(tenantId)
                .userId(null)
                .build();
        return findAllDuplicateSearchByApiNameAndType(user, objectApiNameList, type, isPending, searchOrderByType, onTime);
    }

    @Override
    public Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(User user, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType, boolean onTime) {
        Map<String, List<IDuplicatedSearch>> apiNameToDuplicateList = duplicatedRuleService.findByDescribeApiNames(user.getTenantId(), Lists.newArrayList(objectApiNameList), user.getUserId(), isPending, searchOrderByType.getCode());
        apiNameToDuplicateList.forEach((key, value) -> value.removeIf(x -> !DuplicatedSearchExt.isEnableDuplicate(x) || !type.equals(x.getType())));
        apiNameToDuplicateList.forEach((key, value) -> processRelatedRule(value));
        apiNameToDuplicateList.values().forEach(x -> transDuplicatedRulesName(x, onTime));
        return apiNameToDuplicateList;
    }

    @Override
    public List<IDuplicatedSearch> findAllByObject(String tenantId, String userId, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType) {
        Map<String, List<IDuplicatedSearch>> searchByApiNameAndType = duplicatedRuleService.findByDescribeApiNames(tenantId, null, userId, isPending, searchOrderByType.getCode());
        searchByApiNameAndType.forEach((key, value) -> value.removeIf(x -> !DuplicatedSearchExt.isEnableDuplicate(x) || !type.equals(x.getType())));
        List<IDuplicatedSearch> duplicatedList = Lists.newArrayList();
        searchByApiNameAndType.forEach((k, v) -> duplicatedList.addAll(v));
        //如果在这个灰度中,代表所有的规则都在新表中,老表中没有新规则
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MULTI_DUPLICATE_RULE_SUPPORT_PRE_OBJ_GRAY, tenantId)) {
            return duplicatedList;
        }
        Set<String> blackApiNameSet = AppFrameworkConfig.multiDuplicateRuleBlackApiNameList();
        if (CollectionUtils.empty(blackApiNameSet)) {
            return duplicatedList;
        }
        duplicatedList.addAll(duplicatedSearchService.findByDescribeApiNames(tenantId, Lists.newArrayList(blackApiNameSet), type, isPending));
        return duplicatedList;
    }

    @Override
    public List<IDuplicatedSearch> findAllByObject(String tenantId, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType) {
        return findAllByObject(tenantId, null, type, isPending, searchOrderByType);
    }

    @Override
    public List<IDuplicatedSearch> getDuplicateSearchListByType(String tenantId, String objectApiName, IDuplicatedSearch.Type type) {
        List<IDuplicatedSearch> duplicatedSearchList = Lists.newArrayList();
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(tenantId, objectApiName)) {
            //获取对象下所有的查重规则（有序）
            duplicatedSearchList.addAll(getDuplicateSearchRuleList(objectApiName,
                    type, tenantId, false, DuplicateSearchOrderByType.ORDER_BY_SORT));
        } else {
            IDuplicatedSearch duplicatedSearch = findDuplicatedSearchByApiNameAndType(tenantId, objectApiName, type, false);
            if (Objects.nonNull(duplicatedSearch)) {
                duplicatedSearchList.add(duplicatedSearch);
            }
        }
        duplicatedSearchList.removeIf(x -> !DuplicatedSearchExt.of(x).isEnableDuplicate());
        return duplicatedSearchList;
    }

    @Override
    @Transactional
    public void deleteAndRefreshRule(String tenantId, String describeApiName, List<IDuplicatedSearchRefresh> duplicateRuleList) {
        duplicatedRuleService.deleteByDescribe(tenantId, describeApiName);
        duplicatedRuleService.refresh(tenantId, describeApiName, duplicateRuleList, false);
    }

    @Override
    public void fillRuleName(IDuplicatedSearch duplicatedSearch) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(duplicatedSearch.getName())) {
            return;
        }
        switch (duplicatedSearch.getType()) {
            case TOOL:
                duplicatedSearch.setName(I18NExt.text(I18NKey.DEFAULT_DUPLICATE_CHECK_TOOL_RULES));
                break;
            case CLEAN:
                duplicatedSearch.setName(I18NExt.text(I18NKey.DEFAULT_CLEANSING_RULES));
                break;
            case NEW:
                duplicatedSearch.setName(I18NExt.text(I18NKey.DEFAULT_NEW_DUPLICATE_CHECK_RULES));
                break;
        }

    }

    @Override
    public List<IDuplicatedSearch> findByTenant(String tenantId, boolean isPending) {
        return duplicatedSearchService.findByTenant(tenantId, isPending);
    }

    @Override
    public boolean useMultiRule(User user, String describeApiName) {
        if (NOT_USE_MULTI_OBJ_LIST.contains(describeApiName)) {
            return false;
        }
        String useMultiRule = configService.findTenantConfig(user, MULTI_DUPLICATED_RULE_KEY + describeApiName);
        if (StringUtils.isBlank(useMultiRule) || "null".equalsIgnoreCase(useMultiRule)) {
            return true;
        }
        return Boolean.parseBoolean(useMultiRule);
    }

    @Override
    public void changeSupportMultiRule(User user, String describeApiName, boolean useMultiRule) {
        configService.upsertTenantConfig(user, MULTI_DUPLICATED_RULE_KEY + describeApiName, String.valueOf(useMultiRule), ConfigValueType.STRING);
    }

    @Override
    public boolean needDuplicateSearch(RequestContext context, String objectApiName, String ruleApiName, boolean skipDuplicateSearchCheck) {
        if (!context.needCheckDuplicateSearch() || skipDuplicateSearchCheck) {
            return false;
        }
        return StringUtils.isBlank(ruleApiName) || useMultiRule(context.getUser(), objectApiName);
    }

    public List<DuplicateSearchResult.DuplicateData> getDuplicateData(User user, IObjectDescribe objectDescribe, IObjectData objectData, String duplicatedRuleApiName, boolean skipFuzzyRuleDuplicateSearch) {

        boolean useMultiRule = useMultiRule(user, objectDescribe.getApiName());
        //查重
        return duplicatedSearchDataService.getDuplicateData(user, objectDescribe, objectData, duplicatedRuleApiName, useMultiRule, skipFuzzyRuleDuplicateSearch);
    }


    public RLock duplicateSearchLock(User user, IObjectDescribe objectDescribe, IObjectData objectData) {
        if (AppFrameworkConfig.disableDuplicateSearchLock(user.getTenantId())) {
            return null;
        }
        List<IDuplicatedSearch> duplicatedSearchList = getDuplicateSearchListByType(user.getTenantId(), objectDescribe.getApiName(), IDuplicatedSearch.Type.NEW);
        if (CollectionUtils.empty(duplicatedSearchList)) {
            return null;
        }
        return duplicatedSearchDataService.duplicateSearchLock(user, duplicatedSearchList, objectDescribe, objectData);
    }

    @Override
    public ManageGroup queryDuplicateSearchManageGroup(User user, String objectApiName, String sourceInfo) {
        if (!ObjectListConfig.OBJECT_MANAGEMENT.equals(sourceInfo)) {
            return null;
        }
        // 判断查重多规则的灰度开关
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), objectApiName)) {
            return manageGroupService.queryManageGroup(user, objectApiName, ManageGroupType.DUPLICATE_SEARCH_RULE);
        }
        return null;
    }

    @Override
    public void deletedDuplicateSearchManageGroup(User user, String objectApiName) {
        ManageGroup manageGroup = queryDuplicateSearchManageGroup(user, objectApiName, ObjectListConfig.OBJECT_MANAGEMENT);
        if (Objects.isNull(manageGroup)) {
            return;
        }
        Set<String> apiNames = manageGroup.getApiNames();
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        deletedDuplicateSearchManageGroup(user, objectApiName, apiNames);
    }

    private void deletedDuplicateSearchManageGroup(User user, String objectApiName, Collection<String> apiNames) {
        manageGroupService.deleteManageGroup(user, objectApiName, apiNames, ManageGroupType.DUPLICATE_SEARCH_RULE);
    }

}
