package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.i18n.I18nMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class ExtraLayoutManage implements ApplicationContextAware {
    private static final Map<String, ExtraLayoutFactory> layoutProcessorFactoryMap = new ConcurrentHashMap<>();


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ExtraLayoutFactory> map = applicationContext.getBeansOfType(ExtraLayoutFactory.class);
        map.forEach((key, value) -> layoutProcessorFactoryMap.put(value.getLayoutType(), value));
    }


    public ExtraLayoutFactory getLayoutFactory(String layoutType) {
        ExtraLayoutFactory extraLayoutFactory = layoutProcessorFactoryMap.get(layoutType);
        if (Objects.isNull(extraLayoutFactory)) {
            log.warn("No ExtraLayoutFactory found for layoutType: {}", layoutType);
            throw new ValidateException(I18nMessage.ofI8nKey(I18NKey.PARAM_ERROR));
        }
        return extraLayoutFactory;
    }

}
