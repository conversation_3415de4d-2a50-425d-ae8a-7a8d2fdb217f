package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectRelationGraphService单元测试类
 */
@ExtendWith(MockitoExtension.class)
class ObjectRelationGraphServiceTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @InjectMocks
    private ObjectRelationGraphService objectRelationGraphService;

    private ObjectDescribe objectDescribe;
    private List<IObjectDescribe> describes;

    @BeforeEach
    void setUp() {
        objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName("TestObject");
        objectDescribe.setTenantId("74255");

        describes = Lists.newArrayList(objectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildSimpleObjectRelationNetwork方法的基本功能
     */
    @Test
    @DisplayName("构建简单对象关系网络 - 基本功能")
    void testBuildSimpleObjectRelationNetwork_基本功能() {
        // 配置Mock行为
        when(describeLogicService.findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class)))
                .thenReturn(Lists.newArrayList());
        when(describeLogicService.findRelatedDescribesWithoutCopyIfGray(anyString(), anyString()))
                .thenReturn(Lists.newArrayList());

        // 执行被测试方法
        ObjectRelationGraph result = objectRelationGraphService.buildSimpleObjectRelationNetwork(objectDescribe);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertNotNull(result.getDescribeMap());
        assertTrue(result.getDescribeMap().containsKey("TestObject"));

        // 验证Mock交互
        verify(describeLogicService).findAssociationDescribesWithoutCopyIfGray(eq("74255"), eq(objectDescribe));
        verify(describeLogicService).findRelatedDescribesWithoutCopyIfGray(eq("74255"), eq("TestObject"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildObjectRelationNetwork方法的基本功能
     */
    @Test
    @DisplayName("构建对象关系网络 - 基本功能")
    void testBuildObjectRelationNetwork_基本功能() {
        // 准备测试数据
        ObjectDescribe targetDescribe = new ObjectDescribe();
        targetDescribe.setApiName("TargetObject");
        targetDescribe.setTenantId("74255");

        // 不需要Mock配置，直接测试方法执行

        // 执行被测试方法
        ObjectRelationGraph result = objectRelationGraphService.buildObjectRelationNetwork(objectDescribe, targetDescribe);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildObjectRelationNetwork方法带null目标对象
     */
    @Test
    @DisplayName("构建对象关系网络 - null目标对象")
    void testBuildObjectRelationNetwork_null目标对象() {
        // 不需要Mock配置，直接测试方法执行

        // 执行被测试方法
        ObjectRelationGraph result = objectRelationGraphService.buildObjectRelationNetwork(objectDescribe, null);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertNotNull(result.getDescribeMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试服务的基本配置
     */
    @Test
    @DisplayName("服务配置 - 基本验证")
    void testServiceConfiguration_基本验证() {
        // 验证服务实例不为null
        assertNotNull(objectRelationGraphService);

        // 验证依赖注入正确
        assertNotNull(describeLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildSimpleObjectRelationNetwork方法的复杂场景
     */
    @Test
    @DisplayName("构建简单对象关系网络 - 复杂场景")
    void testBuildSimpleObjectRelationNetwork_复杂场景() {
        // 准备测试数据 - 关联对象
        ObjectDescribe associationObject = new ObjectDescribe();
        associationObject.setApiName("AssociationObject");
        associationObject.setTenantId("74255");

        ObjectDescribe relatedObject = new ObjectDescribe();
        relatedObject.setApiName("RelatedObject");
        relatedObject.setTenantId("74255");

        // 配置Mock行为
        when(describeLogicService.findAssociationDescribesWithoutCopyIfGray(anyString(), any(IObjectDescribe.class)))
                .thenReturn(Lists.newArrayList(associationObject));
        when(describeLogicService.findRelatedDescribesWithoutCopyIfGray(anyString(), anyString()))
                .thenReturn(Lists.newArrayList(relatedObject));

        // 执行被测试方法
        ObjectRelationGraph result = objectRelationGraphService.buildSimpleObjectRelationNetwork(objectDescribe);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getNetwork());
        assertNotNull(result.getDescribeMap());
        assertEquals(3, result.getDescribeMap().size());
        assertTrue(result.getDescribeMap().containsKey("TestObject"));
        assertTrue(result.getDescribeMap().containsKey("AssociationObject"));
        assertTrue(result.getDescribeMap().containsKey("RelatedObject"));

        // 验证Mock交互
        verify(describeLogicService).findAssociationDescribesWithoutCopyIfGray(eq("74255"), eq(objectDescribe));
        verify(describeLogicService).findRelatedDescribesWithoutCopyIfGray(eq("74255"), eq("TestObject"));
    }
}
