package com.facishare.paas.appframework.metadata.options.bo;

import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.impl.describe.RecordTypeOption;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * MetadataOptionWrapper的JUnit5测试类
 * 
 * GenerateByAI
 * 
 * 功能：
 * - 测试元数据选项包装器的业务逻辑
 * - 验证抽象类的静态工厂方法
 * - 验证getChildOption()方法
 * - 测试子类SelectOptionWrapper和RecordTypeOptionWrapper
 * 
 * 测试覆盖：
 * - from() - 静态工厂方法（ISelectOption和IRecordTypeOption）
 * - getChildOption() - 获取子选项
 * - updateChildOption() - 更新子选项
 * - SelectOptionWrapper - SelectOption包装器
 * - RecordTypeOptionWrapper - RecordTypeOption包装器
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class MetadataOptionWrapperTest {
    
    // 测试数据常量
    private static final String SELECT_OPTION_VALUE = "select_option_value";
    private static final String RECORD_TYPE_API_NAME = "record_type_api_name";
    private static final String CHILD_FIELD_NAME = "child_field__c";
    private static final List<String> CHILD_OPTIONS = Arrays.asList("child1", "child2");
    
    private ISelectOption mockSelectOption;
    private IRecordTypeOption mockRecordTypeOption;
    private SelectOne mockSelectOne;
    private List<Map<String, List<String>>> testChildOptions;
    
    @BeforeEach
    void setUp() {
        // 创建Mock对象
        mockSelectOption = mock(ISelectOption.class);
        when(mockSelectOption.getValue()).thenReturn(SELECT_OPTION_VALUE);
        
        mockRecordTypeOption = mock(IRecordTypeOption.class);
        when(mockRecordTypeOption.getApiName()).thenReturn(RECORD_TYPE_API_NAME);
        
        mockSelectOne = mock(SelectOne.class);
        when(mockSelectOne.getApiName()).thenReturn(CHILD_FIELD_NAME);
        when(mockSelectOne.getOption(anyString())).thenReturn(Optional.of(mock(ISelectOption.class)));
        
        // 创建测试用的子选项数据
        Map<String, List<String>> childOptionMap = new HashMap<>();
        childOptionMap.put(CHILD_FIELD_NAME, CHILD_OPTIONS);
        testChildOptions = Arrays.asList(childOptionMap);
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试from静态工厂方法创建SelectOptionWrapper
     */
    @Test
    @DisplayName("测试from静态工厂方法 - 创建SelectOptionWrapper")
    void testFromSelectOption_Success() {
        // Act: 执行被测试方法
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        // Assert: 验证结果
        assertNotNull(wrapper, "创建的包装器不应为null");
        assertTrue(wrapper instanceof MetadataOptionWrapper.SelectOptionWrapper, 
                "应该创建SelectOptionWrapper实例");
        assertEquals(SELECT_OPTION_VALUE, wrapper.getValue(), "值应该匹配");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试from静态工厂方法创建RecordTypeOptionWrapper
     */
    @Test
    @DisplayName("测试from静态工厂方法 - 创建RecordTypeOptionWrapper")
    void testFromRecordTypeOption_Success() {
        // Act: 执行被测试方法
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockRecordTypeOption);
        
        // Assert: 验证结果
        assertNotNull(wrapper, "创建的包装器不应为null");
        assertTrue(wrapper instanceof MetadataOptionWrapper.RecordTypeOptionWrapper, 
                "应该创建RecordTypeOptionWrapper实例");
        assertEquals(RECORD_TYPE_API_NAME, wrapper.getValue(), "值应该匹配");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试SelectOptionWrapper的getValue方法
     */
    @Test
    @DisplayName("测试SelectOptionWrapper的getValue方法")
    void testSelectOptionWrapper_GetValue() {
        // Arrange: 创建SelectOptionWrapper
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        // Act: 执行被测试方法
        String result = wrapper.getValue();
        
        // Assert: 验证结果
        assertEquals(SELECT_OPTION_VALUE, result, "值应该匹配");
        verify(mockSelectOption, times(1)).getValue();
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试RecordTypeOptionWrapper的getValue方法
     */
    @Test
    @DisplayName("测试RecordTypeOptionWrapper的getValue方法")
    void testRecordTypeOptionWrapper_GetValue() {
        // Arrange: 创建RecordTypeOptionWrapper
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockRecordTypeOption);
        
        // Act: 执行被测试方法
        String result = wrapper.getValue();
        
        // Assert: 验证结果
        assertEquals(RECORD_TYPE_API_NAME, result, "值应该匹配");
        verify(mockRecordTypeOption, times(1)).getApiName();
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试SelectOptionWrapper的getChildOptions方法
     */
    @Test
    @DisplayName("测试SelectOptionWrapper的getChildOptions方法")
    void testSelectOptionWrapper_GetChildOptions() {
        // Arrange: 创建SelectOptionWrapper并配置Mock
        when(mockSelectOption.getChildOptions()).thenReturn(testChildOptions);
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        // Act: 执行被测试方法
        List<Map<String, List<String>>> result = wrapper.getChildOptions();
        
        // Assert: 验证结果
        assertEquals(testChildOptions, result, "子选项应该匹配");
        verify(mockSelectOption, times(1)).getChildOptions();
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试RecordTypeOptionWrapper的getChildOptions方法
     */
    @Test
    @DisplayName("测试RecordTypeOptionWrapper的getChildOptions方法")
    void testRecordTypeOptionWrapper_GetChildOptions() {
        // Arrange: 创建RecordTypeOptionWrapper并配置Mock
        when(mockRecordTypeOption.getChildOptions()).thenReturn(testChildOptions);
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockRecordTypeOption);
        
        // Act: 执行被测试方法
        List<Map<String, List<String>>> result = wrapper.getChildOptions();
        
        // Assert: 验证结果
        assertEquals(testChildOptions, result, "子选项应该匹配");
        verify(mockRecordTypeOption, times(1)).getChildOptions();
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getChildOption方法的正常场景
     */
    @Test
    @DisplayName("测试getChildOption方法 - 正常场景")
    void testGetChildOption_Success() {
        // Arrange: 创建包装器并配置Mock
        when(mockSelectOption.getChildOptions()).thenReturn(testChildOptions);
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        // Act: 执行被测试方法
        List<String> result = wrapper.getChildOption(mockSelectOne);
        
        // Assert: 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(CHILD_OPTIONS.size(), result.size(), "子选项数量应该匹配");
        assertTrue(result.containsAll(CHILD_OPTIONS), "应该包含所有有效的子选项");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getChildOption方法的空子选项场景
     */
    @Test
    @DisplayName("测试getChildOption方法 - 空子选项场景")
    void testGetChildOption_EmptyChildOptions() {
        // Arrange: 创建包装器并配置Mock返回空子选项
        when(mockSelectOption.getChildOptions()).thenReturn(Collections.emptyList());
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        // Act: 执行被测试方法
        List<String> result = wrapper.getChildOption(mockSelectOne);
        
        // Assert: 验证结果
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "空子选项应该返回空列表");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getChildOption方法的null子选项场景
     */
    @Test
    @DisplayName("测试getChildOption方法 - null子选项场景")
    void testGetChildOption_NullChildOptions() {
        // Arrange: 创建包装器并配置Mock返回null子选项
        when(mockSelectOption.getChildOptions()).thenReturn(null);
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        // Act: 执行被测试方法
        List<String> result = wrapper.getChildOption(mockSelectOne);
        
        // Assert: 验证结果
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "null子选项应该返回空列表");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试updateChildOption方法的正常场景
     */
    @Test
    @DisplayName("测试updateChildOption方法 - 正常场景")
    void testUpdateChildOption_Success() {
        // Arrange: 创建包装器
        when(mockSelectOption.getChildOptions()).thenReturn(new ArrayList<>(testChildOptions));
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        List<String> newChildOptions = Arrays.asList("new_child1", "new_child2");
        
        // Act: 执行被测试方法
        assertDoesNotThrow(() -> {
            wrapper.updateChildOption(CHILD_FIELD_NAME, newChildOptions);
        }, "更新子选项不应抛出异常");
        
        // 验证setChildOptions被调用
        verify(mockSelectOption, times(1)).setChildOptions(any());
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试updateChildOption方法的空选项场景
     */
    @Test
    @DisplayName("测试updateChildOption方法 - 空选项场景")
    void testUpdateChildOption_EmptyOptions() {
        // Arrange: 创建包装器
        when(mockSelectOption.getChildOptions()).thenReturn(new ArrayList<>(testChildOptions));
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(mockSelectOption);
        
        // Act: 执行被测试方法 - 传入空选项列表
        assertDoesNotThrow(() -> {
            wrapper.updateChildOption(CHILD_FIELD_NAME, Collections.emptyList());
        }, "更新空选项不应抛出异常");
        
        // 验证setChildOptions被调用
        verify(mockSelectOption, times(1)).setChildOptions(any());
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试SelectOptionWrapper的setChildOptions方法（SelectOption类型）
     */
    @Test
    @DisplayName("测试SelectOptionWrapper的setChildOptions方法 - SelectOption类型")
    void testSelectOptionWrapper_SetChildOptions_SelectOption() {
        // Arrange: 使用真实的SelectOption对象
        SelectOption realSelectOption = new SelectOption();
        realSelectOption.setValue(SELECT_OPTION_VALUE);
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(realSelectOption);
        
        // Act: 执行被测试方法 - 设置空子选项
        assertDoesNotThrow(() -> {
            wrapper.updateChildOption(CHILD_FIELD_NAME, Collections.emptyList());
        }, "设置空子选项不应抛出异常");
        
        // Assert: 验证子选项被清空
        List<Map<String, List<String>>> result = wrapper.getChildOptions();
        assertTrue(result == null || result.isEmpty(), "子选项应该被清空");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试RecordTypeOptionWrapper的setChildOptions方法（RecordTypeOption类型）
     */
    @Test
    @DisplayName("测试RecordTypeOptionWrapper的setChildOptions方法 - RecordTypeOption类型")
    void testRecordTypeOptionWrapper_SetChildOptions_RecordTypeOption() {
        // Arrange: 使用真实的RecordTypeOption对象
        RecordTypeOption realRecordTypeOption = new RecordTypeOption();
        realRecordTypeOption.setApiName(RECORD_TYPE_API_NAME);
        MetadataOptionWrapper wrapper = MetadataOptionWrapper.from(realRecordTypeOption);
        
        // Act: 执行被测试方法 - 设置空子选项
        assertDoesNotThrow(() -> {
            wrapper.updateChildOption(CHILD_FIELD_NAME, Collections.emptyList());
        }, "设置空子选项不应抛出异常");
        
        // Assert: 验证子选项被清空
        List<Map<String, List<String>>> result = wrapper.getChildOptions();
        assertTrue(result == null || result.isEmpty(), "子选项应该被清空");
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试不同包装器类型的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideWrapperTypes")
    @DisplayName("测试不同包装器类型 - 参数化测试")
    void testDifferentWrapperTypes_ParameterizedTest(String testCase, MetadataOptionWrapper wrapper, 
                                                    String expectedValue, Class<?> expectedType) {
        // Assert: 验证包装器类型和值
        assertNotNull(wrapper, "测试用例 " + testCase + " 的包装器不应为null");
        assertTrue(expectedType.isInstance(wrapper), "测试用例 " + testCase + " 应该是正确的类型");
        assertEquals(expectedValue, wrapper.getValue(), "测试用例 " + testCase + " 的值应该匹配");
        
        // 测试getChildOptions方法
        assertDoesNotThrow(() -> {
            List<Map<String, List<String>>> childOptions = wrapper.getChildOptions();
            // 子选项可能为null或空，这都是正常的
        }, "测试用例 " + testCase + " 的getChildOptions不应抛出异常");
    }
    
    /**
     * 提供不同包装器类型的测试数据
     */
    private static Stream<Arguments> provideWrapperTypes() {
        ISelectOption selectOption = mock(ISelectOption.class);
        when(selectOption.getValue()).thenReturn("test_select_value");
        when(selectOption.getChildOptions()).thenReturn(Collections.emptyList());
        
        IRecordTypeOption recordTypeOption = mock(IRecordTypeOption.class);
        when(recordTypeOption.getApiName()).thenReturn("test_record_type");
        when(recordTypeOption.getChildOptions()).thenReturn(Collections.emptyList());
        
        return Stream.of(
            Arguments.of("SelectOptionWrapper", 
                        MetadataOptionWrapper.from(selectOption), 
                        "test_select_value", 
                        MetadataOptionWrapper.SelectOptionWrapper.class),
            Arguments.of("RecordTypeOptionWrapper", 
                        MetadataOptionWrapper.from(recordTypeOption), 
                        "test_record_type", 
                        MetadataOptionWrapper.RecordTypeOptionWrapper.class)
        );
    }
}
