package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.data.IDuplicatedSearchRefresh;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang3.RandomStringUtils;
import org.redisson.api.RLock;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface DuplicatedSearchService {

    static String getRuleApiName() {
        return "rule_" +
                RandomStringUtils.randomAlphanumeric(6) +
                "__c";
    }

    IDuplicatedSearch presetDuplicateRule(String apiName, IDuplicatedSearch.Type type, User user);

    /**
     * 保存查重规则
     *
     * @param user
     * @param duplicatedSearch
     * @return
     */
    IDuplicatedSearch createOrUpdateDuplicatedSearch(User user, IDuplicatedSearch duplicatedSearch, boolean isOverrideUsable);

    /**
     * 根据对象APIName查询查重规则
     *
     * @param user
     * @param describeApiName
     * @return
     */
    Map<IDuplicatedSearch.Type, IDuplicatedSearch> findDuplicatedSearchByApiName(User user, String describeApiName, boolean isPending);

    /**
     * 根据对象APIName和查重类型查询查重规则
     *
     * @param tenantId
     * @param describeApiName
     * @param type
     * @return
     */
    IDuplicatedSearch findDuplicatedSearchByApiNameAndType(String tenantId, String describeApiName, IDuplicatedSearch.Type type, boolean isPending);

    /**
     * 根据order和createTime排序获取查重规则列表
     *
     * @param tenantId
     * @param describeApiName
     * @param type
     * @param isPending
     * @return
     */
    List<IDuplicatedSearch> findDuplicateSearchByApiNameAndType(String tenantId, String describeApiName, IDuplicatedSearch.Type type, boolean isPending);

    /**
     * 根据apiNames和查重类型，查询多个对象查重规则
     *
     * @param tenantId
     * @param describeApiNames
     * @param type
     * @param isPending
     * @return
     */
    Map<String, IDuplicatedSearch> findDuplicatedSearchByApiNamesAndType(String tenantId, List<String> describeApiNames, IDuplicatedSearch.Type type, boolean isPending);


    Map<String, List<IDuplicatedSearch>> findDuplicatedSearchListByApiNamesAndType(String tenantId, List<String> describeApiNames, IDuplicatedSearch.Type type, boolean isPending);


    /**
     * 查重是否启用
     *
     * @param user
     * @param describeApiName
     * @param type
     * @param isPending
     * @return
     */
    boolean isEnableDuplicate(User user, String describeApiName, IDuplicatedSearch.Type type, boolean isPending);

    /**
     * 更新查重规则的启用状态
     *
     * @param user
     * @param describeApiName
     * @param type
     * @param enable
     */
    void updateStatus(User user, String describeApiName, IDuplicatedSearch.Type type, boolean enable);

    /**
     * 查询重复数据
     *
     * @param user
     * @param objectData
     * @param type
     */
    List<DuplicateSearchResult.DuplicateData> findDuplicateDataByType(String describeApiName, User user, IObjectData objectData, IDuplicatedSearch.Type type, IObjectDescribe objectDescribe);

    List<DuplicateSearchDataInfo> findDuplicateDataListByType(User user,
                                                              List<IObjectData> dataList,
                                                              IDuplicatedSearch.Type type,
                                                              IObjectDescribe objectDescribe,
                                                              boolean searchFuzzy);

    List<IObjectData> processFieldValueForDuplicatedSearch(IDuplicatedSearch duplicatedSearch,
                                                           IObjectDescribe objectDescribe,
                                                           List<IObjectData> dataList,
                                                           User user);

    IObjectData processFieldValueForDuplicatedSearch(IDuplicatedSearch duplicatedSearch, IObjectDescribe objectDescribe, IObjectData objectData, User user);

    /**
     * 查询企业下所有查重规则
     *
     * @param tenantId
     * @return
     */
    List<IDuplicatedSearch> findAll(String tenantId, boolean isPending);

    /**
     * 查询企业下所有开启查重规则的对象apiName
     *
     * @param user
     * @param isPending
     * @param type
     * @return
     */
    List<String> findAllObjectApiNameByType(User user, boolean isPending, IDuplicatedSearch.Type type);

    void dealSelectOne(IObjectData objectData, IObjectDescribe objectDescribe);


    // ---------------------------------------- 清洗规则

    /**
     * 保存清洗规则
     *
     * @param user
     * @param duplicatedSearch
     * @return
     */
    IDuplicatedSearch createOrUpdateCleanRule(User user, IDuplicatedSearch duplicatedSearch, boolean isOverrideUsable);

    /**
     * 通过企业id和对象apiName批量删除查重规则——提供app-task项目使用
     *
     * @param user            用户
     * @param describeApiName describe api name
     */
    void deleteByDescribeApiName(User user, String describeApiName);

    List<IDuplicatedSearch> getDuplicateSearchRuleList(String userId, String describeApiName, IDuplicatedSearch.Type type,
                                                       String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType, boolean onTime);

    // 实时 && 非实时
    List<IDuplicatedSearch> getDuplicateSearchRuleList(String describeApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType, boolean onTime);

    List<IDuplicatedSearch> getDuplicateSearchRuleList(String describeApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType);

    List<IDuplicatedSearch> getEnableDuplicateSearchRuleList(String describeApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending, DuplicateSearchOrderByType orderByType);

    List<IDuplicatedSearch> getEnableDuplicateSearchRuleList(User user, String describeApiName, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType orderByType);


    IDuplicatedSearch findDuplicatedSearchByRuleApiName(String describeApiName, String duplicateSearchRuleApiName, String tenantId, Boolean isPending);

    IDuplicatedSearch findDuplicatedSearchByRuleApiName(String describeApiName, String duplicateSearchRuleApiName, String tenantId, Boolean isPending, boolean onTime);

    IDuplicatedSearch findDuplicatedSearchByRuleApiNameOrFirstRule(String describeApiName, String duplicateSearchRuleApiName, IDuplicatedSearch.Type type, String tenantId, Boolean isPending);

    void deleteByRuleApiName(String objectApiName, String duplicateRuleApiName, String tenantId);

    @Deprecated
    void enableOrDisable(String tenantId, String describeApiName, String ruleApiName, IDuplicatedSearch.Type type, boolean enable);

    void enableOrDisable(User user, String describeApiName, String ruleApiName, IDuplicatedSearch.Type type, boolean enable);

    Integer getDuplicateSearchRuleCount(String objectApiName, IDuplicatedSearch.Type type, String tenantId);

    void dealPriority(String describeApiName, HashMap<Integer, String> sortMap, String tenantId);

    List<IDuplicatedSearch> findDuplicatedSearchByRuleApiNames(String describeApiName, List<String> duplicateRuleApiNames, String tenantId, Boolean isPending);

    /**
     * 查询多个对象下的查重规则
     *
     * @param tenantId
     * @param objectApiNameList
     * @param type
     * @return
     */
    Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(String tenantId, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType);

    Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(User user, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType);

    Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(String tenantId, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType, boolean onTime);

    Map<String, List<IDuplicatedSearch>> findAllDuplicateSearchByApiNameAndType(User user, Set<String> objectApiNameList, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType, boolean onTime);

    List<IDuplicatedSearch> findAllByObject(String tenantId, String userId, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType);

    List<IDuplicatedSearch> findAllByObject(String tenantId, IDuplicatedSearch.Type type, Boolean isPending, DuplicateSearchOrderByType searchOrderByType);

    List<IDuplicatedSearch> getDuplicateSearchListByType(String tenantId, String objectApiName, IDuplicatedSearch.Type type);

    /**
     * 刷描述接口
     *
     * @param tenantId
     * @return
     */
    void deleteAndRefreshRule(String tenantId, String describeApiName, List<IDuplicatedSearchRefresh> duplicateRuleList);


    List<IDuplicatedSearch> findByTenant(String tenantId, boolean isPending);

    boolean useMultiRule(User user, String describeApiName);

    void changeSupportMultiRule(User user, String describeApiName, boolean useMultiRule);

    boolean needDuplicateSearch(RequestContext context, String objectApiName, String ruleApiName, boolean skipDuplicateSearchCheck);

    List<DuplicateSearchResult.DuplicateData> getDuplicateData(User user, IObjectDescribe objectDescribe, IObjectData objectData, String duplicatedRuleApiName, boolean skipFuzzyRuleDuplicateSearch);

    RLock duplicateSearchLock(User user, IObjectDescribe objectDescribe, IObjectData objectData);

    void fillRuleName(IDuplicatedSearch duplicatedSearch);

    ManageGroup queryDuplicateSearchManageGroup(User user, String objectApiName, String sourceInfo);

    void deletedDuplicateSearchManageGroup(User user, String objectApiName);
}
